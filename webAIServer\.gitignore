# Dependencies
node_modules/
package-lock.json
yarn.lock
pnpm-lock.yaml

# Production build
dist/
build/

# Environment files
.env
.env.local
.env.*.local

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# IDE
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store

# Testing
coverage/
.nyc_output/

# Temporary files
tmp/
temp/
*.tmp

# Database
*.db
*.sqlite
*.sqlite3

# API Keys (just in case)
api-keys.json
keys.json
