// File: pow-js-solver-corrected.ts (with simplified Base64 encoding)
/**
 * Convert a Uint8Array to a hexadecimal string.
 */
function bytesToHex(bytes) {
    return Array.from(bytes).map(b => b.toString(16).padStart(2, '0')).join('');
}
/**
 * Compute SHA-256 hash of a string asynchronously using Web Crypto API
 * and return it as a hex string.
 */
async function sha256Hex(str) {
    const encoder = new TextEncoder();
    const data = encoder.encode(str);
    const hashBuffer = await crypto.subtle.digest('SHA-256', data);
    return bytesToHex(new Uint8Array(hashBuffer));
}
/**
 * Solves the DeepSeek "DeepSeekHashV1" Proof-of-Work challenge using the correct
 * difficulty interpretation (comparing hash value against a target threshold).
 *
 * @param challengeObj - The challenge object received from the API.
 * @returns A Promise resolving to the Base64-encoded JSON string for the header.
 * @throws If the algorithm is not "DeepSeekHashV1" or if difficulty is missing/invalid.
 */
export async function solveDeepSeekPow(challengeObj) {
    console.log("Starting PoW solve with corrected logic:", challengeObj);
    if (challengeObj.algorithm !== 'DeepSeekHashV1') {
        throw new Error('Unsupported PoW algorithm: ' + challengeObj.algorithm);
    }
    if (typeof challengeObj.difficulty !== 'number' || challengeObj.difficulty < 0) {
        throw new Error('Invalid or missing difficulty value in challenge object.');
    }
    const { challenge, salt, signature, difficulty, target_path } = challengeObj;
    // --- Correct Difficulty Interpretation ---
    // Calculate the target threshold as a 64-character hex string
    // target = 2^256 / (difficulty + 1)
    // Use BigInt for calculations involving 2^256
    const n256 = 2n ** 256n;
    const difficultyPlus1 = BigInt(difficulty) + 1n;
    const targetValue = n256 / difficultyPlus1;
    // Convert the target value to a 64-character zero-padded hex string
    const targetHex = targetValue.toString(16).padStart(64, '0');
    console.log(`Difficulty: ${difficulty}, Target Hex Threshold: ${targetHex}`);
    // --- End Correct Difficulty Interpretation ---
    let answer = 0;
    let found = false;
    let computedHash = '';
    const startTime = Date.now();
    while (!found) {
        const testStr = challenge + salt + answer;
        computedHash = await sha256Hex(testStr);
        // --- Correct Check ---
        // Compare the computed hash string directly with the target hex string.
        // Lexicographical comparison works because they are fixed-length hex strings.
        if (computedHash < targetHex) {
            found = true;
            break;
        }
        // --- End Correct Check ---
        answer++;
        // Yield control occasionally to prevent blocking the main thread
        // Increased check interval slightly for potentially better performance
        if (answer % 100000 === 0) {
            const elapsedTime = Date.now() - startTime;
            console.log(`PoW Checkpoint: Answer=${answer}, Elapsed=${elapsedTime}ms, Current Hash: ${computedHash.substring(0, 10)}...`);
            await new Promise(resolve => setTimeout(resolve, 0)); // Yield using setTimeout
        }
    }
    const endTime = Date.now();
    console.log(`PoW Solution Found! Answer: ${answer}, Hash: ${computedHash}, Time: ${endTime - startTime}ms`);
    const solutionObj = {
        algorithm: challengeObj.algorithm,
        challenge: challenge,
        salt: salt,
        answer: answer,
        signature: signature,
        target_path: target_path
    };
    const jsonStr = JSON.stringify(solutionObj);
    // *** USE SIMPLE BTOA ENCODING ***
    // This assumes the JSON string contains only ASCII characters, which is true for this object.
    // Reverted from the UTF-8 safe version as a test for the "Invalid PoW response" error.
    console.log('Encoding JSON string for header:', jsonStr); // Log the JSON before encoding
    const base64Solution = btoa(jsonStr);
    console.log('Generated Base64 Header Value:', base64Solution); // Log the final Base64
    return base64Solution;
}
// Example Usage (replace with actual challenge data when used)
/*
async function exampleCorrected() {
  const challengeResponse = { // Example data structure from your log
    algorithm: "DeepSeekHashV1",
    challenge: "0053629127e47af42f2f8d35893075b0f2db7d836d02125dd92a5fa92d590e2e",
    salt: "d84043e34f2f43127d78",
    signature: "73851ef9eea5b08e393c6b680f9fa90e22dec555fe979449cf8f3db197000434",
    difficulty: 144000,
    expire_at: 1746183361929, // Example timestamp
    expire_after: 300000,
    target_path: "/api/v0/chat/completion"
  };

  try {
    const powSolutionHeader = await solveDeepSeekPow(challengeResponse);
    console.log("PoW Solution Header (Corrected Logic, Simple Base64):", powSolutionHeader);
    // Check decoding:
    console.log("Decoded Header Value:", atob(powSolutionHeader));
  } catch (error) {
    console.error("Failed to solve PoW (Corrected Logic, Simple Base64):", error);
  }
}

// You would typically call this after fetching the challenge from the API
// exampleCorrected();
*/
