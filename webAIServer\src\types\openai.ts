/**
 * OpenAI API type definitions
 * Based on OpenAI API v1 specifications
 */

export interface ChatCompletionRequest {
  model: string;
  messages: ChatMessage[];
  temperature?: number;
  top_p?: number;
  n?: number;
  stream?: boolean;
  stop?: string | string[];
  max_tokens?: number;
  presence_penalty?: number;
  frequency_penalty?: number;
  logit_bias?: Record<string, number>;
  user?: string;
  functions?: Function[];
  function_call?: 'none' | 'auto' | { name: string };
  tools?: Tool[];
  tool_choice?: 'none' | 'auto' | { type: 'function'; function: { name: string } };
}

export interface ChatMessage {
  role: 'system' | 'user' | 'assistant' | 'function' | 'tool';
  content: string | null;
  name?: string;
  function_call?: {
    name: string;
    arguments: string;
  };
  tool_calls?: ToolCall[];
}

export interface Function {
  name: string;
  description?: string;
  parameters?: Record<string, any>;
}

export interface Tool {
  type: 'function';
  function: Function;
}

export interface ToolCall {
  id: string;
  type: 'function';
  function: {
    name: string;
    arguments: string;
  };
}

export interface ChatCompletionResponse {
  id: string;
  object: 'chat.completion';
  created: number;
  model: string;
  choices: ChatCompletionChoice[];
  usage?: Usage;
  system_fingerprint?: string;
}

export interface ChatCompletionChoice {
  index: number;
  message: ChatMessage;
  finish_reason: 'stop' | 'length' | 'function_call' | 'content_filter' | 'tool_calls' | null;
  logprobs?: null;
}

export interface Usage {
  prompt_tokens: number;
  completion_tokens: number;
  total_tokens: number;
}

// Streaming response types
export interface ChatCompletionChunk {
  id: string;
  object: 'chat.completion.chunk';
  created: number;
  model: string;
  choices: ChatCompletionChunkChoice[];
  system_fingerprint?: string;
}

export interface ChatCompletionChunkChoice {
  index: number;
  delta: ChatMessageDelta;
  finish_reason: 'stop' | 'length' | 'function_call' | 'content_filter' | 'tool_calls' | null;
  logprobs?: null;
}

export interface ChatMessageDelta {
  role?: 'system' | 'user' | 'assistant' | 'function' | 'tool';
  content?: string | null;
  function_call?: {
    name?: string;
    arguments?: string;
  };
  tool_calls?: ToolCallDelta[];
}

export interface ToolCallDelta {
  index?: number;
  id?: string;
  type?: 'function';
  function?: {
    name?: string;
    arguments?: string;
  };
}

// Model list response
export interface ModelListResponse {
  object: 'list';
  data: Model[];
}

export interface Model {
  id: string;
  object: 'model';
  created: number;
  owned_by: string;
  permission?: Permission[];
  root?: string;
  parent?: string | null;
}

export interface Permission {
  id: string;
  object: 'model_permission';
  created: number;
  allow_create_engine: boolean;
  allow_sampling: boolean;
  allow_logprobs: boolean;
  allow_search_indices: boolean;
  allow_view: boolean;
  allow_fine_tuning: boolean;
  organization: string;
  group: string | null;
  is_blocking: boolean;
}

// Error response
export interface ErrorResponse {
  error: {
    message: string;
    type: string;
    param?: string | null;
    code: string | null;
  };
}
