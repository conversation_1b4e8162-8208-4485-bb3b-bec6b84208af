const path = require('path');
const CopyWebpackPlugin = require('copy-webpack-plugin');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');

module.exports = {
  entry: {
    background: './src/background/index.js',
    popup: './src/popup/popup.js',
    settings: './src/settings/settings.js'
  },
  output: {
    path: path.resolve(__dirname, 'dist'),
    filename: '[name].bundle.js',
    clean: true
  },
  module: {
    rules: [
      {
        test: /\.css$/,
        use: [MiniCssExtractPlugin.loader, 'css-loader']
      },
      {
        test: /\.(png|jpg|gif|svg)$/,
        type: 'asset/resource',
        generator: {
          filename: 'assets/[name][ext]'
        }
      }
    ]
  },
  plugins: [
    new MiniCssExtractPlugin({
      filename: '[name].css'
    }),
    new HtmlWebpackPlugin({
      template: './src/popup/popup.html',
      filename: '../popup.html',
      chunks: ['popup']
    }),
    new HtmlWebpackPlugin({
      template: './src/settings/settings.html',
      filename: '../settings.html',
      chunks: ['settings']
    }),
    new CopyWebpackPlugin({
      patterns: [
        { from: 'manifest.json', to: '../manifest.json' },
        { from: 'rules.json', to: '../rules.json' },
        { from: 'icons', to: '../icons' },
        { from: 'lib', to: '../lib' },
        { from: 'assets', to: '../assets' }
      ]
    })
  ],
  resolve: {
    extensions: ['.js', '.json']
  },
  devtool: 'source-map',
  optimization: {
    minimize: process.env.NODE_ENV === 'production'
  }
};
