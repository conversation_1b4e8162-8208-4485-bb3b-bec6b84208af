{"version": 3, "file": "background.bundle.js", "mappings": ";;;;;;AAAA;AACA,MAAM,IAA0C;AAChD,IAAI,iCAAgC,CAAC,MAAQ,CAAC,oCAAE,OAAO;AAAA;AAAA;AAAA,kGAAC;AACxD,IAAI,KAAK;AAAA,YAQN;AACH,CAAC;AACD;;AAEA,sCAAsC;;AAEtC;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,wHAAwH;AACxH;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB,UAAU;AAC3B;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,iBAAiB,GAAG;AACpB,mBAAmB,SAAS;AAC5B;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB,QAAQ;AACzB;AACA;AACA,iBAAiB,UAAU;AAC3B;AACA,iBAAiB,UAAU;AAC3B;AACA,iBAAiB,QAAQ;AACzB;AACA,iBAAiB,SAAS;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB;AACnB;AACA;;;AAGA;AACA;AACA;AACA;AACA,YAAY;AACZ;AACA,YAAY;AACZ;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,iBAAiB,QAAQ;AACzB;AACA,iBAAiB,QAAQ;AACzB;AACA,iBAAiB,SAAS;AAC1B;AACA;AACA;AACA,iBAAiB,SAAS;AAC1B;AACA;AACA;AACA,iBAAiB,SAAS;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB;AACnB;AACA;;;AAGA;AACA;AACA;AACA,iDAAiD,kBAAkB,EAAE,sCAAsC,MAAM,KAAK,UAAU,YAAY;AAC5I;;AAEA;AACA,gDAAgD,kBAAkB,EAAE,sCAAsC,MAAM,KAAK,UAAU,YAAY;AAC3I;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB,gBAAgB;AAChB,gCAAgC,MAAM;AACtC,uCAAuC;AACvC;;AAEA;AACA;AACA;AACA;AACA,cAAc;AACd;AACA;AACA,cAAc;AACd;AACA;AACA;AACA,eAAe;AACf;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB,QAAQ;AACzB;AACA,iBAAiB,UAAU;AAC3B;AACA;AACA,iBAAiB,UAAU;AAC3B;AACA;AACA;AACA,mBAAmB;AACnB;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;;AAEA,SAAS;AACT;;AAEA;AACA;AACA;AACA;AACA;AACA,iBAAiB,QAAQ;AACzB;AACA;AACA,iBAAiB,QAAQ,cAAc;AACvC;AACA;AACA;AACA,6DAA6D,gBAAgB;AAC7E;AACA,iBAAiB,QAAQ,cAAc;AACvC;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA,mBAAmB;AACnB;;AAEA,+CAA+C,eAAe;AAC9D;AACA;AACA;AACA;AACA,WAAW;;AAEX;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB;AAChB;AACA;AACA;AACA;AACA,gBAAgB;AAChB;AACA;AACA;AACA;AACA,cAAc;AACd;AACA;AACA;AACA;AACA,cAAc;AACd;AACA;AACA,cAAc;AACd;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,iBAAiB;;AAEjB;AACA;AACA;;AAEA,eAAe;AACf;AACA;;AAEA;AACA;AACA,WAAW;;AAEX;AACA;AACA;AACA,cAAc;AACd;AACA;;AAEA;AACA,WAAW;;AAEX;AACA;AACA,WAAW;;AAEX;AACA;AACA;;AAEA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB,oCAAoC;AACrD;AACA;AACA;AACA;AACA,mBAAmB;AACnB;;;AAGA;AACA;AACA;AACA,SAAS;;AAET;AACA;AACA,SAAS;;AAET;AACA;AACA;;AAEA,OAAO;;AAEP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB,QAAQ;AAC3B;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB,GAAG;AACtB;AACA,mBAAmB,QAAQ;AAC3B;AACA,mBAAmB,aAAa;AAChC;AACA;AACA,qBAAqB;AACrB;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;;AAEA;AACA;AACA,YAAY;AACZ;AACA;;AAEA,0EAA0E;AAC1E;AACA;;AAEA;AACA;AACA,YAAY;AACZ;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;;AAEA;AACA;AACA,gBAAgB;AAChB;AACA;;AAEA;AACA;AACA;AACA,eAAe;AACf,aAAa;AACb;AACA;AACA,aAAa;AACb,aAAa;AACb;AACA;;;AAGA;AACA;AACA,YAAY;AACZ;AACA,YAAY;;;AAGZ;AACA;AACA,OAAO;;AAEP;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA,YAAY;AACZ;AACA;AACA,UAAU;AACV;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;;AAEA;AACA;AACA,+CAA+C,kBAAkB,EAAE,sCAAsC,MAAM,KAAK,UAAU,YAAY;AAC1I;;AAEA;AACA,8CAA8C,kBAAkB,EAAE,sCAAsC,MAAM,KAAK,UAAU,YAAY;AACzI;;AAEA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA,SAAS;AACT;;AAEA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX,SAAS;AACT;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;;;AAGA;AACA,IAAI;AACJ;AACA;AACA,CAAC;AACD;;;;;;;UCpvCA;UACA;;UAEA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;;UAEA;UACA;;UAEA;UACA;UACA;;;;;WCtBA;WACA;WACA;WACA;WACA;WACA,iCAAiC,WAAW;WAC5C;WACA,E;;;;;WCPA;WACA;WACA;WACA;WACA,yCAAyC,wCAAwC;WACjF;WACA;WACA,E;;;;;WCPA;WACA;WACA;WACA;WACA,GAAG;WACH;WACA;WACA,CAAC,I;;;;;WCPD,wF;;;;;WCAA;WACA;WACA;WACA,uDAAuD,iBAAiB;WACxE;WACA,gDAAgD,aAAa;WAC7D,E;;;;;;;;;;;;;;;;;;;;;;ACNO;AACP;;;ACDA;AAC0E;AACrB;AAC9C;AACA;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO;AACP;AACO;AACP;AACA;AACA;AACA,UAAU,WAAiB;AAC3B;AACA;AACA;;;AC5BA;AACA;AACA;AACA;AACgC;AAChC;AACO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iDAAiD,IAAI;AACrD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAY;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU,MAAM;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV,gDAAgD,OAAO;AACvD;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,cAAc;AAClC;AACA,KAAK,UAAU;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP,KAAK,SAAS;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA,4CAA4C;AAC5C;AACA;AACA;AACA;AACA;;;ACnOA,MAAM,aAAa,gnCAAgnC,SAAS,GAAG,sBAAsB,iCAAiC,+CAA+C,gGAAgG,qBAAM,CAAC,qBAAM,gCAAgC,cAAc,sFAAsF,OAAO,YAAY,qFAAqF,gHAAgH,8FAA8F,wEAAwE,SAAS,QAAQ,OAAO,oBAAoB,WAAW,oBAAoB,MAAM,oBAAoB,SAAS,qBAAqB,YAAY,QAAQ,oBAAoB,MAAM,oBAAoB,cAAc,oBAAoB,YAAY,oBAAoB,aAAa,oBAAoB,UAAU,oBAAoB,OAAO,oBAAoB,SAAS,oBAAoB,aAAa,oBAAoB,SAAS,oBAAoB,SAAS,qBAAqB,gBAAgB,SAAS,4CAA4C,SAAS,4CAA4C,0BAA0B,oBAAoB,eAAe,oBAAoB,WAAW,oBAAoB,WAAW,oBAAoB,YAAY,oBAAoB,0BAA0B,4CAA4C,eAAe,4CAA4C,UAAU,oBAAoB,WAAW,4CAA4C,WAAW,6CAA6C,eAAe,QAAQ,oBAAoB,cAAc,oBAAoB,gBAAgB,oBAAoB,kBAAkB,oBAAoB,iBAAiB,oBAAoB,gBAAgB,oBAAoB,qBAAqB,oBAAoB,kBAAkB,oBAAoB,mBAAmB,oBAAoB,WAAW,qBAAqB,WAAW,QAAQ,qBAAqB,eAAe,QAAQ,oBAAoB,YAAY,oBAAoB,SAAS,qBAAqB,UAAU,KAAK,oBAAoB,SAAS,oBAAoB,qBAAqB,oBAAoB,SAAS,oBAAoB,MAAM,qBAAqB,WAAW,iBAAiB,MAAM,0CAA0C,SAAS,QAAQ,yCAAyC,WAAW,mBAAmB,uBAAuB,YAAY,QAAQ,oBAAoB,WAAW,oBAAoB,QAAQ,oBAAoB,cAAc,oBAAoB,OAAO,4CAA4C,QAAQ,oBAAoB,aAAa,oBAAoB,SAAS,oBAAoB,SAAS,oBAAoB,OAAO,6CAA6C,YAAY,2BAA2B,oBAAoB,2BAA2B,qBAAqB,UAAU,QAAQ,oBAAoB,YAAY,oBAAoB,cAAc,oBAAoB,YAAY,oBAAoB,YAAY,oBAAoB,SAAS,qBAAqB,OAAO,gBAAgB,oBAAoB,qBAAqB,qBAAqB,WAAW,mBAAmB,qBAAqB,OAAO,YAAY,qBAAqB,aAAa,KAAK,oBAAoB,SAAS,oBAAoB,UAAU,oBAAoB,aAAa,oBAAoB,gBAAgB,qBAAqB,gBAAgB,OAAO,oBAAoB,SAAS,oBAAoB,SAAS,oBAAoB,qBAAqB,oBAAoB,SAAS,qBAAqB,aAAa,UAAU,oBAAoB,WAAW,oBAAoB,OAAO,4CAA4C,UAAU,oBAAoB,WAAW,4CAA4C,WAAW,4CAA4C,OAAO,6CAA6C,cAAc,UAAU,oBAAoB,SAAS,oBAAoB,SAAS,oBAAoB,UAAU,qBAAqB,UAAU,mBAAmB,oBAAoB,kBAAkB,oBAAoB,kBAAkB,oBAAoB,qBAAqB,oBAAoB,cAAc,oBAAoB,oBAAoB,oBAAoB,kBAAkB,qBAAqB,WAAW,YAAY,oBAAoB,oBAAoB,oBAAoB,UAAU,qBAAqB,UAAU,OAAO,OAAO,oBAAoB,MAAM,oBAAoB,gBAAgB,oBAAoB,SAAS,oBAAoB,MAAM,qBAAqB,UAAU,KAAK,oBAAoB,gBAAgB,qBAAqB,OAAO,OAAO,oBAAoB,MAAM,oBAAoB,gBAAgB,oBAAoB,SAAS,oBAAoB,MAAM,sBAAsB,OAAO,mBAAmB,oBAAoB,SAAS,oBAAoB,iBAAiB,oBAAoB,UAAU,oBAAoB,YAAY,oBAAoB,gBAAgB,oBAAoB,MAAM,oBAAoB,aAAa,oBAAoB,UAAU,oBAAoB,kBAAkB,oBAAoB,SAAS,oBAAoB,YAAY,oBAAoB,YAAY,oBAAoB,YAAY,oBAAoB,OAAO,oBAAoB,QAAQ,oBAAoB,SAAS,oBAAoB,SAAS,oBAAoB,YAAY,oBAAoB,cAAc,oBAAoB,UAAU,oBAAoB,kBAAkB,oBAAoB,SAAS,qBAAqB,WAAW,KAAK,qBAAqB,gBAAgB,cAAc,oBAAoB,WAAW,qBAAqB,aAAa,wBAAwB,qBAAqB,UAAU,QAAQ,oBAAoB,MAAM,oBAAoB,SAAS,oBAAoB,aAAa,oBAAoB,iBAAiB,oBAAoB,SAAS,oBAAoB,SAAS,uBAAuB,4GAA4G,wBAAwB,wBAAwB,2BAA2B,OAAO,iEAAiE,8EAA8E,6JAA6J,4DAA4D,2DAA2D,WAAW,EAAE,cAAc,MAAM,EAAE,UAAU,SAAS,GAAG,0DAA0D,WAAW,EAAE,cAAc,MAAM,EAAE,UAAU,SAAS,GAAG,4BAA4B,8BAA8B,aAAa,mBAAmB,KAAK,SAAS,gBAAgB,GAAG,qKAAqK,gDAAgD,mBAAmB,KAAK,GAAG,yBAAyB,gCAAgC,EAAE,0DAA0D,eAAe,KAAK,IAAI,6BAA6B,qCAAqC,sBAAsB,oBAAoB,WAAW,oEAAoE,gBAAgB,gBAAgB,cAAc,iBAAiB,wEAAwE,KAAK,+CAA+C,kDAAkD,QAAQ,IAAI,mBAAmB,gBAAgB,kJAAkJ,oBAAoB,sBAAsB,QAAQ,sBAAsB,6BAA6B,gEAAgE,4BAA4B,iDAAiD,cAAc,EAAE,YAAY,qBAAqB,EAAE,KAAK,sDAAsD,gCAAgC,cAAc,WAAW,GAAG,IAAI,WAAW,SAAS,oBAAoB,qBAAqB,2BAA2B,YAAY,YAAY,KAAK,OAAO,MAAM,kGAAkG,+CAA+C,EAAE,cAAc,2DAA2D,IAAI,mBAAmB,OAAO,mBAAmB,MAAM,sKAAsK,kBAAkB,2DAA2D,WAAW,EAAE,cAAc,MAAM,EAAE,UAAU,SAAS,GAAG,0DAA0D,WAAW,EAAE,cAAc,MAAM,EAAE,UAAU,SAAS,GAAG,4BAA4B,qBAAqB,mBAAmB,EAAE,8BAA8B,GAAG,IAAI,UAAU,SAAS,wBAAwB,UAAU,6EAA6E,oBAAoB,EAAE,OAAO,uCAAuC,oBAAoB,GAAG,IAAI,OAAO,oBAAoB,MAAM,oBAAoB,MAAM,sBAAsB,kBAAkB,SAAS,MAAM,WAAW,MAAM,WAAW,OAAO,WAAW,oBAAoB,kCAAkC,IAAI,mBAAmB,MAAM,2BAA2B,aAAa,kOAAkO,YAAY,WAAW,YAAY,MAAM,0CAA0C,OAAO,0FAA0F,kBAAkB,0CAA0C,gBAAgB,uBAAuB,wDAAwD,6LAA6L,IAAI,QAAQ,cAAc,qCAAqC,uBAAuB,eAAe,EAAE,IAAI,SAAS,iCAAiC,2LAA2L,oDAAoD,iCAAiC,+BAA+B,wBAAwB,6CAA6C,uCAAuC,sCAAsC,gCAAgC,+BAA+B,2BAA2B,GAAG,IAAI,SAAS,WAAW,wHAAwH,mFAAmF,sBAAsB,IAAI,oFAAoF,SAAS,mEAAmE,8BAA8B,IAAI,2BAA2B,0BAA0B,EAAE,SAAS,uDAAuD,mBAAmB,0BAA0B,oBAAoB,yDAAyD,2EAA2E,qBAAqB,mBAAmB,kGAAkG,oFAAoF,6FAA6F,2BAA2B,mCAAmC,6GAA6G,mBAAmB,OAAO,8CAA8C,aAAa,oBAAoB,0BAA0B,2VAA2V,iEAAiE,GAAG,IAAI,EAAE,OAAO,2CAA2C,QAAQ,yBAAyB,qBAAqB,uCAAuC,2BAA2B,gEAAgE,gBAAgB,iCAAiC,oBAAoB,sBAAsB,IAAI,MAAM,EAAE,MAAM,eAAe,gBAAgB,YAAY,SAAS,QAAQ,iBAAiB,sBAAsB,6CAA6C,SAAS,MAAM,EAAE,MAAM,eAAe,gBAAgB,WAAW,eAAe,UAAU,EAAE,sBAAsB,cAAc,kBAAkB,iBAAiB,8BAA8B,gBAAgB,qCAAqC,eAAe,mBAAmB,4BAA4B,uBAAuB,iCAAiC,iBAAiB,uBAAuB,aAAa,GAAG,qCAAqC,SAAS,oIAAoI,uBAAuB,oBAAoB,SAAS,aAAa,IAAI,yFAAyF,EAAE,mCAAmC,SAAS,4DAA4D,cAAc,oDAAoD,oBAAoB,IAAI,gCAAgC,SAAS,OAAO,mCAAmC,SAAS,SAAS,kDAAkD,EAAE,UAAU,oBAAoB,aAAa,EAAE,EAAE,EAAE,OAAO,IAAI,qDAAqD,GAAG,uBAAuB,KAAK,sFAAsF,+FAA+F,SAAS,gDAAgD,SAAS,OAAO,aAAa,IAAI,0CAA0C,MAAM,sBAAsB,uEAAuE,kFAAkF,yEAAyE,SAAS,sEAAsE,YAAY,aAAa,IAAI,YAAY,sBAAsB,QAAQ,4BAA4B,eAAe,gDAAgD,6HAA6H,WAAW,SAAS,qEAAqE,YAAY,4BAA4B,WAAW,mBAAmB,GAAG,4HAA4H,GAAG,2FAA2F,SAAS,IAAI,gCAAgC,YAAY,EAAE,SAAS,yBAAyB,GAAG,gDAAgD,EAAE,WAAW,8BAA8B,GAAG,8CAA8C,EAAE,6CAA6C,qBAAqB,GAAG,0EAA0E,SAAS,4BAA4B,MAAM,EAAE,gBAAgB,GAAG,uBAAuB,UAAU,0BAA0B,EAAE,IAAI,uGAAuG,GAAG,kCAAkC,MAAM,iCAAiC,MAAM,IAAI,KAAK,gBAAgB,GAAG,0DAA0D,MAAM,QAAQ,MAAM,IAAI,IAAI,yCAAyC,QAAQ,WAAW,qBAAqB,EAAE,yBAAyB,8BAA8B,GAAG,iEAAiE,KAAK,GAAG,MAAM,gBAAgB,GAAG,wCAAwC,KAAK,oCAAoC,SAAS,8EAA8E,GAAG,2CAA2C,MAAM,oHAAoH,GAAG,qDAAqD,MAAM,sHAAsH,GAAG,+CAA+C,MAAM,QAAQ,MAAM,gDAAgD,GAAG,mEAAmE,KAAK,QAAQ,SAAS,kBAAkB,GAAG,kDAAkD,EAAE,MAAM,OAAO,mBAAmB,GAAG,6HAA6H,GAAG,0FAA0F,WAAW,IAAI,gBAAgB,GAAG,+CAA+C,EAAE,gCAAgC,mDAAmD,kBAAkB,GAAG,+CAA+C,MAAM,IAAI,yBAAyB,qEAAqE,gBAAgB,GAAG,mCAAmC,EAAE,mBAAmB,GAAG,gHAAgH,GAAG,uEAAuE,EAAE,GAAG,IAAI,yCAAyC,QAAQ,QAAQ,qBAAqB,EAAE,gBAAgB,GAAG,gHAAgH,GAAG,wFAAwF,GAAG,mEAAmE,SAAS,kBAAkB,GAAG,2DAA2D,SAAS,kBAAkB,GAAG,2DAA2D,QAAQ,mBAAmB,GAAG,qEAAqE,gBAAgB,GAAG,0DAA0D,KAAK,GAAG,IAAI,6CAA6C,GAAG,4DAA4D,SAAS,4EAA4E,GAAG,mEAAmE,GAAG,yDAAyD,qBAAqB,GAAG,0EAA0E,yBAAyB,GAAG,8DAA8D,GAAG,gEAAgE,+BAA+B,gBAAgB,GAAG,gEAAgE,EAAE,OAAO,IAAI,qCAAqC,4CAA4C,sEAAsE,EAAE,mBAAmB,GAAG,sFAAsF,8DAA8D,EAAE,EAAE,wBAAwB,GAAG,0CAA0C,EAAE,iBAAiB,SAAS,qBAAqB,GAAG,8LAA8L,mHAAmH,oCAAoC,SAAS,oBAAoB,mFAAmF,6FAA6F,0BAA0B,KAAK,oDAAoD,8CAA8C,gBAAgB,8FAA8F,oDAAoD,oBAAoB,EAAE,OAAO,EAAE,UAAU,EAAE,aAAa,EAAE,aAAa,EAAE,gBAAgB,EAAE,aAAa,EAAE,gBAAgB,EAAE,OAAO,EAAE,qLAAqL,WAAW,KAAK,MAAM,KAAK,uBAAuB,gBAAgB,0FAA0F,aAAa,kCAAkC,EAAE,wCAAwC,IAAI,iBAAiB,EAAE,+BAA+B,iBAAiB,uEAAuE,gBAAgB,wBAAwB,uBAAuB,wBAAwB,0BAA0B,0BAA0B,+BAA+B,kDAAkD,mDAAmD,eAAe,0DAA0D,SAAS,IAAI,yBAAyB,oEAAoE,uBAAuB,qBAAqB,SAAS,oBAAoB,UAAU,mFAAmF,cAAc,iMAAiM;AAAA,UAAK,CAAC,cAAc,6BAA6B,iBAAiB,IAAI,gCAAgC,MAAM,YAAY,cAAc,2BAA2B,iBAAiB,WAAW,2BAA2B,6BAA6B,qCAAqC,uBAAuB,+BAA+B,+CAA+C,oBAAoB,oEAAoE,SAAS,cAAc,0DAA0D,wGAAwG,KAAK,GAAG,KAAK,iBAAiB,KAAK,GAAG,KAAK,OAAO;AAAA,aAAO,CAAC,6BAA6B,sBAAsB,GAAG,QAAQ,IAAI,mBAAmB,GAAG,QAAQ,EAAE,mBAAmB,GAAG,mBAAmB,iBAAiB,EAAE,+BAA+B,iBAAiB,8DAA8D,mBAAmB,+BAA+B,gBAAgB,kCAAkC,MAAM,uBAAuB,sBAAsB,uBAAuB,0BAA0B,IAAI,0CAA0C,YAAY,wCAAwC,kBAAkB,YAAY,wBAAwB,SAAS,SAAS,SAAS,MAAM,iBAAiB,uBAAuB,oEAAoE,MAAM,iBAAiB,OAAO,gFAAgF,SAAS,kBAAkB,eAAe,iEAAiE,GAAG,4BAA4B,8CAA8C,iDAAiD,MAAM,2BAA2B,OAAO,OAAO,gHAAgH,OAAO,qBAAqB,iCAAiC,8KAA8K,mBAAmB,IAAI,4CAA4C,kBAAkB,0EAA0E,OAAO,4BAA4B,uBAAuB,iBAAiB,+EAA+E,iEAAiE,qBAAqB,+BAA+B,wIAAwI,QAAQ,qBAAqB,wBAAwB,sBAAsB,iBAAiB,mFAAmF,SAAS,sCAAsC,MAAM,sDAAsD,kDAAkD,GAAG,sCAAsC,uBAAuB,qDAAqD,gBAAgB,kFAAkF,oBAAoB,oDAAoD,wCAAwC,4CAA4C,uBAAuB,qBAAM,QAAQ,qBAAM,CAAC,kDAAkD,qBAAqB,EAAE,MAAM,mGAAmG,GAAG,oBAAoB,qEAAqE,6BAA6B,MAAM,4EAA4E,2CAA2C,sGAAsG,gGAAgG,iEAAiE,uBAAuB,GAAG,oBAAoB,+IAA+I,EAAE,IAAI,kBAAkB,kBAAkB,mBAAmB,EAAE,sBAAsB,8BAA8B,EAAE,IAAI,EAAE,EAAE,MAAM,EAAE,KAAK,WAAW,cAAc,SAAS,0EAA0E,aAAa,EAAE,qKAAqK,kCAAkC,EAAE,SAAS,IAAI,8DAA8D,6BAA6B,EAAE,SAAS,qEAAqE,MAAM,wbAAwb,uBAAuB,iBAAiB,sLAAsL,6TAA6T,8NAA8N,cAAc,mBAAmB,8EAA8E,2CAA2C,+CAA+C,IAAI,wCAAwC,SAAS,2FAA2F,QAAQ,mBAAmB,mGAAmG,gFAAgF,mBAAmB,kBAAkB,eAAe,wEAAwE,6CAA6C,UAAU,YAAY,6DAA6D,sBAAsB,MAAM,oEAAoE,MAAM,gDAAgD,qOAAqO,uBAAuB,4BAA4B,sDAAsD,KAAK,MAAM,oBAAoB,kCAAkC,IAAI,EAAE,qKAAqK,EAAE,mBAAmB,SAAS,cAAc,yCAAyC,iBAAiB,uBAAuB,EAAE,sBAAsB,cAAc,mBAAmB,cAAc,qBAAqB,oBAAoB,uEAAuE,yBAAyB,uEAAuE,8BAA8B,uEAAuE,wCAAwC,uEAAuE,4BAA4B,uEAAuE,qBAAqB,uEAAuE,mBAAmB,uEAAuE,4BAA4B,wEAAwE,oFAAoF,0BAA0B,8GAA8G,gCAAgC,mCAAmC,SAAS,4HAA4H,wHAAwH,yBAAyB,6UAA6U,oBAAoB,oCAAoC,8FAA8F,mBAAmB,0GAA0G,UAAU,oBAAoB,qBAAqB,SAAS,sBAAsB,oBAAoB,yHAAyH,sJAAsJ,yBAAyB,2BAA2B,IAAI,+CAA+C,oBAAoB,oDAAoD,gBAAgB,0BAA0B,2EAA2E,SAAS,2FAA2F,qBAAqB,IAAI,oDAAoD,uIAAuI,MAAM,IAAI,gBAAgB,SAAS,gEAAgE,EAAE,qCAAqC,yIAAyI,gCAAgC,mFAAmF,wGAAwG,QAAQ,kBAAkB,eAAe,0DAA0D,KAAK,IAAI,QAAQ,KAAK,QAAQ,KAAK,OAAO,cAAc,SAAS,0EAA0E,EAAE,sCAAsC,qBAAqB,IAAI,SAAS,kDAAkD,0KAA0K,gEAAgE,uBAAuB,qCAAqC,2BAA2B,eAAe,OAAO,MAAM,EAAE,oCAAoC,+FAA+F,mBAAmB,uBAAuB,2EAA2E,QAAQ,EAAE,SAAS,iEAAiE,OAAO,6BAA6B,2BAA2B,wBAAwB,uHAAuH,eAAe,oDAAoD,+FAA+F,iCAAiC,uBAAuB,MAAM,iIAAiI,IAAI,WAAW,2BAA2B,SAAS,gEAAgE,4CAA4C,mBAAmB,+BAA+B,QAAQ,gGAAgG,4HAA4H,IAAI,wCAAwC,iBAAiB,kBAAkB,SAAS,iDAAiD,OAAO,wBAAwB,wPAAwP,uCAAuC,kDAAkD,wBAAwB,2BAA2B,2GAA2G,EAAE,EAAE,mBAAmB,uBAAuB,kDAAkD,UAAU,IAAI,mBAAmB,2BAA2B,6CAA6C,0BAA0B,MAAM,EAAE,MAAM,eAAe,gBAAgB,WAAW,MAAM,mBAAmB,UAAU,EAAE,uBAAuB,EAAE,4BAA4B,2CAA2C,WAAW,sBAAsB,0EAA0E,gBAAgB,uBAAuB,sBAAsB,wFAAwF,eAAe,mDAAmD,0BAA0B,uBAAuB,kBAAkB,2BAA2B,sBAAsB,uBAAuB,2BAA2B,QAAQ,GAAG,yFAAyF,mBAAmB,qOAAqO,SAAS,wDAAwD,OAAO,0EAA0E,IAAI,gCAAgC,SAAS,2JAA2J,0CAA0C,eAAe,eAAe,gCAAgC,mBAAmB,yCAAyC,0DAA0D,kBAAkB,eAAe,EAAE,SAAS,sHAAsH,oBAAoB,2DAA2D,oCAAoC,qBAAqB,sIAAsI,kBAAkB,oCAAoC,8FAA8F,mIAAmI,kBAAkB,uBAAuB,yGAAyG,mBAAmB,8BAA8B,oFAAoF,6FAA6F,uBAAuB,IAAI,qDAAqD,+EAA+E,KAAK,2HAA2H,0IAA0I,yBAAyB,OAAO,wBAAwB,0GAA0G,2HAA2H,cAAc,sDAAsD,+LAA+L,+EAA+E,EAAE,oDAAoD,qEAAqE,mIAAmI,2BAA2B,6CAA6C,EAAE,mCAAmC,uBAAuB,kDAAkD,eAAe,yCAAyC,gGAAgG,sKAAsK,EAAE,6VAA6V,qDAAqD,IAAI,uDAAuD,yYAAyY,SAAS,oRAAoR,SAAS,2FAA2F,uCAAuC,IAAI,mCAAmC,qBAAqB,oDAAoD,OAAO,6CAA6C,GAAG,qBAAqB,SAAS,iCAAiC,6CAA6C,GAAG,4BAA4B,YAAY,cAAc,SAAS,4CAA4C,6CAA6C,GAAG,wHAAwH,GAAG,oEAAoE,SAAS,mBAAmB,2EAA2E,OAAO,wBAAwB,OAAO,qFAAqF,EAAE,yFAAyF,GAAG,2EAA2E,SAAS,mGAAmG,EAAE,mDAAmD,WAAW,6FAA6F,EAAE,2BAA2B,IAAI,4HAA4H,SAAS,kFAAkF,EAAE,wFAAwF,GAAG,wEAAwE,UAAU,4JAA4J,kJAAkJ,yBAAyB,iBAAiB,EAAE,GAAG,EAAE,2DAA2D,GAAG,WAAW,EAAE,IAAI,wBAAwB,EAAE,GAAG,aAAa,GAAG,uBAAuB,kDAAkD,eAAe,0CAA0C,EAAE,qFAAqF,EAAE,YAAY,SAAS,uEAAuE,SAAS,yBAAyB,8DAA8D,EAAE,IAAI,4GAA4G,kJAAkJ,yBAAyB,iBAAiB,EAAE,GAAG,EAAE,2DAA2D,GAAG,WAAW,EAAE,IAAI,wBAAwB,EAAE,GAAG,aAAa,GAAG,uBAAuB,kDAAkD,eAAe,0CAA0C,EAAE,qFAAqF,EAAE,YAAY,SAAS,uEAAuE,SAAS,yBAAyB,8DAA8D,EAAE,6DAA6D,GAAG,4CAA4C,SAAS,gEAAgE,EAAE,OAAO,SAAS,4GAA4G,6BAA6B,IAAI,2BAA2B,kFAAkF,KAAK,0IAA0I,yJAAyJ,0CAA0C,4FAA4F,4KAA4K,+EAA+E,eAAe,oDAAoD,qEAAqE,mIAAmI,2BAA2B,YAAY,EAAE,8BAA8B,EAAE,+BAA+B,uBAAuB,kDAAkD,eAAe,8BAA8B,gGAAgG,0LAA0L,EAAE,kRAAkR,qCAAqC,gEAAgE,kCAAkC,2DAA2D,SAAS,oBAAoB,WAAW,oEAAoE,sEAAsE,yEAAyE,iEAAiE,oBAAoB,WAAW,kEAAkE,gBAAgB,gFAAgF,sEAAsE,8EAA8E,sEAAsE,yEAAyE,0BAA0B,uEAAuE,SAAS,SAAS,qHAAqH,uCAAuC,yDAAyD,2BAA2B,IAAI,yBAAyB,4BAA4B,EAAE,0DAA0D,gNAAgN,KAAK,wHAAwH,oIAAoI,sDAAsD,gbAAgb,sLAAsL,qDAAqD,gEAAgE,qBAAqB,qCAAqC,6BAA6B,wGAAwG,EAAE,gBAAgB,uBAAuB,kDAAkD,eAAe,4CAA4C,WAAW,IAAI,uEAAuE,SAAS,8CAA8C,oDAAoD,qGAAqG,eAAe,SAAS,IAAI,sBAAsB,sDAAsD,4BAA4B,kCAAkC,SAAS,iHAAiH,oGAAoG,qCAAqC,EAAE,EAAE,8HAA8H,SAAS,2FAA2F,6BAA6B,IAAI,uDAAuD,+BAA+B,+DAA+D,KAAK,wHAAwH,mJAAmJ,oIAAoI,aAAa,qJAAqJ,wIAAwI,qDAAqD,gEAAgE,qBAAqB,qCAAqC,oDAAoD,2GAA2G,EAAE,gBAAgB,uBAAuB,kDAAkD,eAAe,4CAA4C,WAAW,IAAI,0EAA0E,SAAS,8CAA8C,oDAAoD,wGAAwG,eAAe,IAAI,sBAAsB,wMAAwM,SAAS,2GAA2G,SAAS,SAAS,4FAA4F,6zBAA6zB,YAAY,gHAAgH,qBAAM,IAAI,qBAAM,YAAY,qBAAM,mJAAmJ,UAAU,mBAAmB,cAAc,mGAAmG,0BAA0B,8GAA8G,gCAAgC,mCAAmC,SAAS,0HAA0H,sHAAsH,uBAAuB,yBAAyB,UAAU,qBAAqB,qBAAqB,SAAS,uCAAuC,YAAY,sBAAsB,QAAQ,4BAA4B,eAAe,gDAAgD,mLAAmL,UAAU,kFAAkF,4BAA4B,yCAAyC,aAAa,0BAA0B,wBAAwB,oBAAoB,sEAAsE,uCAAuC,oKAAoK,yBAAyB,EAAE,oEAAoE,sBAAsB,2BAA2B,IAAI,eAAe,aAAa,4CAA4C,cAAc,uCAAuC,sDAAsD,aAAa,eAAe,uBAAuB,+DAA+D,sBAAsB,kFAAkF,4BAA4B,EAAE,UAAU,6NAA6N,wEAAwE,SAAS,+CAA+C,uBAAuB,uFAAuF,uCAAuC,sFAAsF,SAAS,sJAAsJ,uBAAuB,mIAAmI,kBAAkB,IAAI,qBAAqB,+BAA+B,4CAA4C,cAAc,uCAAuC,qEAAqE,iHAAiH,wCAAwC,aAAa,6HAA6H,OAAO,uHAAuH,2BAA2B,SAAS,kCAAkC,0EAA0E,MAAM,gCAAgC,2IAA2I,mCAAmC,uBAAuB,8CAA8C,iHAAiH,oBAAoB,GAAG,YAAY,8BAA8B,2CAA2C,qCAAqC,QAAQ,SAAS,uGAAuG,wBAAwB,yBAAyB,SAAS,iDAAiD,OAAO,uBAAuB,wBAAwB,6BAA6B,SAAS,gFAAgF,iGAAiG,kBAAkB,+BAA+B,wCAAwC,sBAAsB,GAAG,yBAAyB,mJAAmJ,cAAc,0FAA0F,mDAAmD,IAAI,UAAU,SAAS,6CAA6C,OAAO,QAAQ,oBAAoB,cAAc,2DAA2D,IAAI,WAAW,WAAW,mBAAmB,UAAU,0BAA0B,EAAE,SAAS,+DAA+D,4EAA4E,SAAS,mHAAmH,iBAAiB,IAAI,2BAA2B,mHAAmH,yFAAyF,wDAAwD,2BAA2B,QAAQ,EAAE,0BAA0B,0CAA0C,uEAAuE,kBAAkB,eAAe,0BAA0B,0FAA0F,OAAO,uDAAuD,oDAAoD,wCAAwC,mCAAmC,qBAAqB,8BAA8B,iCAAiC,eAAe,GAAG,SAAS,sIAAsI,0BAA0B,6BAA6B,YAAY,SAAS,sFAAsF,eAAe,gHAAgH,eAAe,qDAAqD,OAAO,oHAAoH,OAAO,sBAAsB,mDAAmD,2CAA2C,GAAG,SAAS,4BAA4B,UAAU,SAAS,kEAAkE,+EAA+E,2BAA2B,wBAAwB,qHAAqH,eAAe,oDAAoD,+FAA+F,iCAAiC,uBAAuB,yGAAyG,sBAAsB,wCAAwC,oBAAoB,0HAA0H,yBAAyB,oBAAoB,yDAAyD,yGAAyG,6CAA6C,wBAAwB,oCAAoC,8FAA8F,mBAAmB,qEAAqE,mBAAmB,0FAA0F,yBAAyB,qBAAqB,IAAI,6DAA6D,mBAAmB,mBAAmB,KAAK,6BAA6B,8KAA8K,SAAS,iCAAiC,kBAAkB,aAAa,qBAAqB,2CAA2C,OAAO,oCAAoC,EAAE,UAAU,2DAA2D,uBAAuB,2FAA2F,SAAS,yBAAyB,uBAAuB,wIAAwI,SAAS,uIAAuI,mBAAmB,SAAS,mBAAmB,gBAAgB,EAAE,mFAAmF,0BAA0B,8GAA8G,gCAAgC,mCAAmC,SAAS,4HAA4H,wHAAwH,yBAAyB,4CAA4C,UAAU,mBAAmB,qBAAqB,SAAS,4BAA4B,YAAY,IAAI,gDAAgD,EAAE,sBAAsB,oEAAoE,gBAAgB,EAAE,SAAS,SAAS,qOAAqO,+BAA+B,IAAI,wDAAwD,EAAE,sBAAsB,EAAE,SAAS,oEAAoE,oCAAoC,EAAE,0CAA0C,SAAS,6EAA6E,aAAa,SAAS,6DAA6D,gDAAgD,gBAAgB,KAAK,2BAA2B,wBAAwB,uHAAuH,eAAe,oDAAoD,+FAA+F,iCAAiC,oBAAoB,oCAAoC,8FAA8F,mBAAmB,+GAA+G,uBAAuB,yGAAyG,sBAAsB,IAAI,uCAAuC,oFAAoF,yCAAyC,oBAAoB,uHAAuH,mCAAmC,yBAAyB,SAAS,wGAAwG,oBAAoB,yDAAyD,2EAA2E,GAAG,gCAAgC,uGAAuG,uBAAuB,iIAAiI,IAAI,WAAW,2BAA2B,SAAS,kCAAkC,+DAA+D,MAAM,qKAAqK,4CAA4C,gBAAgB,iBAAiB,0CAA0C,qBAAqB,QAAQ,MAAM,MAAM,qBAAqB,mBAAmB,IAAI,6CAA6C,iBAAiB,UAAU,2CAA2C,EAAE,UAAU,uBAAuB,wDAAwD,EAAE,uBAAuB,sHAAsH,IAAI,6GAA6G,EAAE,SAAS,iDAAiD,OAAO,wBAAwB,sFAAsF,4CAA4C,YAAY,oCAAoC,2DAA2D,iBAAiB,sBAAsB,iBAAiB,cAAc,mGAAmG,iKAAiK,EAAE,EAAE,UAAU,uBAAuB,IAAI,sBAAsB,4EAA4E,4CAA4C,SAAS,qBAAqB,uCAAuC,gFAAgF,kCAAkC,EAAE,2DAA2D,EAAE,6BAA6B,SAAS,4DAA4D,gBAAgB,6BAA6B,kDAAkD,kBAAkB,2BAA2B,SAAS,sKAAsK,UAAU,IAAI,mBAAmB,4BAA4B,yFAAyF,6CAA6C,wBAAwB,MAAM,EAAE,MAAM,eAAe,gBAAgB,WAAW,eAAe,UAAU,EAAE,sBAAsB,cAAc,6HAA6H,cAAc,iCAAiC,0BAA0B,2BAA2B,QAAQ,cAAc,aAAa,sBAAsB,mEAAmE,+DAA+D,iCAAiC,0BAA0B,2BAA2B,QAAQ,IAAI,0CAA0C,iGAAiG,iFAAiF,qBAAqB,0BAA0B,uBAAuB,EAAE,mCAAmC,kBAAkB,eAAe,EAAE,SAAS,0EAA0E,sBAAsB,sBAAsB,IAAI,UAAU,uCAAuC,sCAAsC,MAAM,kCAAkC,6KAA6K,IAAI,gCAAgC,SAAS,eAAe,iCAAiC,0EAA0E,4BAA4B,EAAE,2DAA2D,EAAE,qCAAqC,SAAS,4DAA4D,UAAU,qCAAqC,uBAAuB,oDAAoD,EAAE,WAAW,SAAS,iCAAiC,GAAG,qDAAqD,GAAG,4BAA4B,YAAY,uBAAuB,4BAA4B,uBAAuB,wDAAwD,GAAG,qBAAqB,IAAI,qDAAqD,oFAAoF,KAAK,wHAAwH,6IAA6I,2DAA2D,iBAAiB,sBAAsB,iBAAiB,GAAG,kFAAkF,OAAO,EAAE,EAAE,iEAAiE,SAAS,+CAA+C,4EAA4E,SAAS,kGAAkG,yCAAyC,IAAI,wIAAwI,2DAA2D,EAAE,kCAAkC,mFAAmF,qBAAqB,EAAE,EAAE,qEAAqE,SAAS,+CAA+C,qBAAqB,iDAAiD,SAAS,SAAS,+FAA+F,2BAA2B,IAAI,2BAA2B,oFAAoF,KAAK,wHAAwH,6IAA6I,2DAA2D,iBAAiB,sBAAsB,iBAAiB,SAAS,oFAAoF,EAAE,EAAE,UAAU,uBAAuB,8DAA8D,SAAS,kCAAkC,uBAAuB,sGAAsG,mCAAmC,OAAO,EAAE,8HAA8H,SAAS,2FAA2F,oBAAoB,iDAAiD,yBAAyB,gFAAgF,gDAAgD,wBAAwB,6EAA6E,mBAAmB,sCAAsC,2DAA2D,EAAE,eAAe,6DAA6D,EAAE,0EAA0E,SAAS,+CAA+C,6BAA6B,IAAI,2BAA2B,oFAAoF,KAAK,qIAAqI,0JAA0J,2DAA2D,iBAAiB,sBAAsB,iBAAiB,GAAG,6DAA6D,EAAE,gFAAgF,SAAS,+CAA+C,SAAS,2FAA2F,iCAAiC,sCAAsC,IAAI,2DAA2D,EAAE,sBAAsB,+FAA+F,EAAE,sFAAsF,SAAS,+CAA+C,SAAS,4FAA4F,6BAA6B,sCAAsC,IAAI,2DAA2D,EAAE,GAAG,6DAA6D,EAAE,qFAAqF,SAAS,+CAA+C,SAAS,2FAA2F,gCAAgC,IAAI,2DAA2D,+FAA+F,EAAE,kMAAkM,SAAS,+CAA+C,SAAS,2FAA2F,0BAA0B,kDAAkD,IAAI,6CAA6C,yJAAyJ,SAAS,gHAAgH,mBAAmB,SAAS,ktCAAktC,mBAAmB,gBAAgB,EAAE,8GAA8G,gxBAAgxB,oFAAoF,0BAA0B,8GAA8G,gCAAgC,mCAAmC,SAAS,gIAAgI,4HAA4H,6BAA6B,yBAAyB,UAAU,uBAAuB,YAAY,mBAAmB,mBAAmB,iCAAiC,qBAAqB,SAAS,qBAAqB,IAAI,WAAW,aAAa,oCAAoC,sFAAsF,mJAAmJ,oBAAoB,iEAAiE,EAAE,mIAAmI,uCAAuC,gDAAgD,mBAAmB,oBAAoB,qBAAqB,EAAE,+LAA+L,SAAS,uGAAuG,uCAAuC,6BAA6B,iBAAiB,SAAS,wGAAwG,OAAO,6EAA6E,sEAAsE,gBAAgB,2DAA2D,kBAAkB,IAAI,eAAe,aAAa,4CAA4C,cAAc,uCAAuC,oBAAoB,aAAa,iBAAiB,uCAAuC,EAAE,6CAA6C,oBAAoB,aAAa,oBAAoB,uCAAuC,EAAE,cAAc,eAAe,+GAA+G,IAAI,oBAAoB,aAAa,sBAAsB,yCAAyC,EAAE,yBAAyB,SAAS,+CAA+C,qBAAqB,YAAY,SAAS,oJAAoJ,uBAAuB,IAAI,mBAAmB,aAAa,mBAAmB,yCAAyC,aAAa,SAAS,2FAA2F,yBAAyB,IAAI,mBAAmB,aAAa,2BAA2B,yCAAyC,eAAe,SAAS,6FAA6F,2BAA2B,wBAAwB,2HAA2H,eAAe,oDAAoD,+FAA+F,iCAAiC,wBAAwB,oCAAoC,8FAA8F,mBAAmB,6FAA6F,uBAAuB,yGAAyG,sBAAsB,IAAI,uBAAuB,kBAAkB,oBAAoB,uHAAuH,wCAAwC,yBAAyB,SAAS,wGAAwG,oBAAoB,yDAAyD,2EAA2E,GAAG,gCAAgC,qBAAqB,uBAAuB,6BAA6B,sEAAsE,QAAQ,mCAAmC,EAAE,oEAAoE,EAAE,KAAK,iBAAiB,0HAA0H,cAAc,mSAAmS,gBAAgB,sNAAsN,gBAAgB,yEAAyE,IAAI,WAAW,2BAA2B,SAAS,kCAAkC,oCAAoC,SAAS,gCAAgC,yHAAyH,4BAA4B,kCAAkC,UAAU,SAAS,wDAAwD,OAAO,wBAAwB,4CAA4C,MAAM,yBAAyB,iBAAiB,kBAAkB,qBAAqB,IAAI,+BAA+B,SAAS,0IAA0I,8BAA8B,+BAA+B,gJAAgJ,sEAAsE,gvBAAgvB,MAAM,MAAM,0FAA0F,+EAA+E,qEAAqE,yFAAyF,SAAS,QAAQ,UAAU,oBAAoB,kBAAkB,aAAa,2BAA2B,iFAAiF,EAAE,UAAU,uBAAuB,8KAA8K,UAAU,IAAI,mBAAmB,2BAA2B,yFAAyF,6CAA6C,mGAAmG,MAAM,EAAE,MAAM,eAAe,gBAAgB,WAAW,oBAAoB,UAAU,EAAE,MAAM,4DAA4D,uBAAuB,EAAE,4BAA4B,6CAA6C,8BAA8B,sBAAsB,YAAY,WAAW,OAAO,4BAA4B,yCAAyC,8BAA8B,wBAAwB,iDAAiD,SAAS,qBAAqB,sBAAsB,SAAS,0RAA0R,yBAAyB,mEAAmE,eAAe,2BAA2B,QAAQ,yKAAyK,0BAA0B,oCAAoC,iIAAiI,iCAAiC,qFAAqF,GAAG,SAAS,qEAAqE,uEAAuE,0CAA0C,6EAA6E,kBAAkB,eAAe,EAAE,SAAS,iFAAiF,wBAAwB,IAAI,oBAAoB,aAAa,gBAAgB,yCAAyC,EAAE,+HAA+H,SAAS,kGAAkG,uCAAuC,IAAI,wHAAwH,mCAAmC,qBAAqB,8BAA8B,OAAO,uBAAuB,GAAG,wBAAwB,SAAS,iCAAiC,uBAAuB,GAAG,4BAA4B,YAAY,GAAG,SAAS,mBAAmB,0CAA0C,0BAA0B,GAAG,2IAA2I,GAAG,uEAAuE,SAAS,kBAAkB,aAAa,2CAA2C,iEAAiE,2DAA2D,EAAE,EAAE,oFAAoF,GAAG,0EAA0E,gCAAgC,SAAS,uCAAuC,EAAE,OAAO,SAAS,6FAA6F,0BAA0B,IAAI,gCAAgC,qCAAqC,uHAAuH,+HAA+H,oBAAoB,aAAa,oCAAoC,+DAA+D,8EAA8E,EAAE,EAAE,kIAAkI,SAAS,aAAa,UAAU,gBAAgB,EAAE,SAAS,2FAA2F,4BAA4B,IAAI,gCAAgC,qCAAqC,kIAAkI,+GAA+G,oBAAoB,aAAa,oCAAoC,+DAA+D,8EAA8E,EAAE,EAAE,yCAAyC,SAAS,sGAAsG,mBAAmB,IAAI,gCAAgC,4HAA4H,qCAAqC,wGAAwG,YAAY,aAAa,gCAAgC,+DAA+D,kEAAkE,EAAE,2EAA2E,SAAS,mGAAmG,ihCAAihC,qFAAqF,cAAc,sEAAsE,iFAAiF,kCAAkC,sBAAsB,qBAAqB,iBAAiB,qDAAqD,wCAAwC,eAAe,qCAAqC,WAAW,0BAA0B,eAAe,aAAa,eAAe,cAAc,gCAAgC,SAAS,iBAAiB,yCAAyC,2DAA2D,UAAU,yIAAyI,yBAAyB,8DAA8D,oDAAoD,KAAK,cAAc,iHAAiH,sEAAsE,cAAc,kCAAkC,+BAA+B,wMAAwM,KAAK,cAAc,uBAAuB,0CAA0C,0CAA0C,+CAA+C,iIAAiI,gDAAgD,sEAAsE,uDAAuD,wCAAwC,+DAA+D,6JAA6J,qBAAqB,+JAA+J,2FAA2F,yFAAyF,+BAA+B,0BAA0B,gBAAgB,gEAAgE,kDAAkD,IAAI,iBAAiB,2BAA2B,mCAAmC,OAAO,SAAS,KAAK,kBAAkB,8EAA8E,MAAM,WAAW,6CAA6C,0WAA0W,SAAS,mIAAmI,QAAQ,GAAG,8DAA8D,MAAM,2FAA2F,GAAG,2IAA2I,KAAK,MAAM,sEAAsE,YAAY,EAAE,GAAG,EAAE,GAAG,oCAAoC,IAAI,+CAA+C,MAAM,aAAa,WAAW,UAAU,aAAa,WAAW,WAAW,mDAAmD,EAAE,MAAM,EAAE,GAAG,4FAA4F,EAAE,KAAK,4CAA4C,GAAG,wGAAwG,GAAG,qIAAqI,EAAE,qCAAqC,GAAG,QAAQ,EAAE,QAAQ,EAAE,sCAAsC,EAAE,qCAAqC,GAAG,QAAQ,EAAE,QAAQ,EAAE,sCAAsC,EAAE,qCAAqC,UAAU,GAAG,0BAA0B,uBAAuB,0BAA0B,0CAA0C,KAAK,KAAK,iGAAiG,EAAE,iBAAiB,cAAc,4DAA4D,GAAG,6CAA6C,gEAAgE,EAAE,KAAK,EAAE,+JAA+J,oBAAoB,6FAA6F,EAAE,iBAAiB,cAAc,gEAAgE,GAAG,8CAA8C,MAAM,0CAA0C,EAAE,gCAAgC,IAAI,KAAK,EAAE,mBAAmB,0CAA0C,EAAE,+HAA+H,EAAE,KAAK,SAAS,uGAAuG,qBAAqB,eAAe,SAAS,yEAAyE,8DAA8D,SAAS,8DAA8D,QAAQ;AAAA,UAAK,CAAC,mBAAmB,SAAS,mBAAmB,cAAc,oFAAoF,0DAA0D,GAAG,mBAAmB,8CAA8C,IAAI,iDAAiD,mBAAmB,4BAA4B,4BAA4B,EAAE,mMAAmM,SAAS,kGAAkG,wBAAwB,oBAAoB,wFAAwF,iDAAiD,mBAAmB,4BAA4B,yOAAyO,yBAAyB,EAAE,EAAE,sBAAsB,0BAA0B,mCAAmC,wMAAwM,gCAAgC,mCAAmC,aAAa,SAAS,WAAW,2IAA2I,MAAM,2CAA2C,8FAA8F,2BAA2B,oEAAoE,sBAAsB,oCAAoC,gKAAgK,2BAA2B,wBAAwB,yHAAyH,eAAe,oDAAoD,wGAAwG,iCAAiC,uBAAuB,yGAAyG,UAAU,qBAAqB,qBAAqB,SAAS,uBAAuB,QAAQ,4BAA4B,IAAI,0CAA0C,wEAAwE,UAAU,IAAI,0DAA0D,GAAG,8UAA8U,QAAQ,EAAE,KAAK,UAAU,iCAAiC,uBAAuB,kBAAkB,8BAA8B,EAAE,2BAA2B,EAAE,wGAAwG,sCAAsC,gKAAgK,QAAQ,EAAE,KAAK,UAAU,iCAAiC,kCAAkC,wBAAwB,kKAAkK,EAAE,6JAA6J,kCAAkC,qGAAqG,SAAS,2GAA2G,MAAM,yHAAyH,+BAA+B,SAAS,qCAAqC,WAAW,mBAAmB,sCAAsC,0CAA0C,MAAM,IAAI,2CAA2C,yEAAyE,SAAS,iLAAiL,4BAA4B,SAAS,8FAA8F,IAAI,mBAAmB,SAAS,gGAAgG,mCAAmC,IAAI,yCAAyC,qCAAqC,WAAW,8BAA8B,kCAAkC,WAAW,kBAAkB,2CAA2C,iBAAiB,EAAE,EAAE,GAAG,0BAA0B,4CAA4C,GAAG,gBAAgB,SAAS,gCAAgC,8CAA8C,IAAI,gBAAgB,oSAAoS,SAAS,gDAAgD,GAAG,4BAA4B,wDAAwD,GAAG,mCAAmC,oBAAoB,WAAW,mBAAmB,uBAAuB,IAAI,yCAAyC,aAAa,SAAS,SAAS,kFAAkF,qCAAqC,WAAW,mBAAmB,wCAAwC,EAAE,EAAE,IAAI,0CAA0C,aAAa,EAAE,0GAA0G,qFAAqF,4EAA4E,EAAE,4CAA4C,SAAS,2FAA2F,wBAAwB,kBAAkB,WAAW,mBAAmB,mBAAmB,mBAAmB,wBAAwB,IAAI,uBAAuB,aAAa,EAAE,iFAAiF,SAAS,oDAAoD,IAAI,uBAAuB,aAAa,EAAE,gFAAgF,SAAS,mDAAmD,YAAY,2BAA2B,8BAA8B,WAAW,mBAAmB,6BAA6B,IAAI,2CAA2C,oBAAoB,mBAAmB,SAAS,oHAAoH,mBAAmB,uBAAuB,wEAAwE,wEAAwE,EAAE,mCAAmC,SAAS,8FAA8F,sBAAsB,gCAAgC,IAAI,6BAA6B,wCAAwC,kDAAkD,EAAE,uBAAuB,wHAAwH,qCAAqC,mFAAmF,uBAAuB,YAAY,EAAE,IAAI,SAAS,0GAA0G,uBAAuB,WAAW,gCAAgC,8HAA8H,4BAA4B,iCAAiC,UAAU,SAAS,QAAQ,IAAI,WAAW,2BAA2B,SAAS,gEAAgE,yEAAyE,yBAAyB,iBAAiB,mBAAmB,oEAAoE,6EAA6E,mLAAmL,+IAA+I,WAAW,mBAAmB,6DAA6D,uBAAuB,8DAA8D,8CAA8C,EAAE,kGAAkG,6CAA6C,SAAS,SAAS,6UAA6U,gBAAgB,KAAK,GAAG,EAAE,MAAM,eAAe,gBAAgB,WAAW,MAAM,mBAAmB,UAAU,EAAE,2BAA2B,EAAE,yBAAyB,mBAAmB,kBAAkB,sBAAsB,oEAAoE,gCAAgC,8BAA8B,uBAAuB,gBAAgB,SAAS,UAAU,wDAAwD,0BAA0B,iCAAiC,EAAE,yEAAyE,yBAAyB,qBAAqB,sDAAsD,qBAAqB,oeAAoe,wCAAwC,kBAAkB,SAAS,4NAA4N,EAAE,2LAA2L,2BAA2B,6FAA6F,EAAE,wCAAwC,kBAAkB,iIAAiI,2BAA2B,6FAA6F,GAAG,sFAAsF,+CAA+C,0GAA0G,iBAAiB,qYAAqY,sCAAsC,6CAA6C,6EAA6E,yDAAyD,gLAAgL,kBAAkB,eAAe,EAAE,SAAS,+GAA+G,qBAAqB,IAAI,qDAAqD,kEAAkE,KAAK,2HAA2H,4IAA4I,yBAAyB,iGAAiG,WAAW,mBAAmB,sCAAsC,0BAA0B,sCAAsC,yEAAyE,SAAS,uCAAuC,iEAAiE,8EAA8E,EAAE,kCAAkC,4EAA4E,SAAS,2GAA2G,uCAAuC,IAAI,mCAAmC,kBAAkB,+DAA+D,OAAO,6CAA6C,GAAG,qBAAqB,SAAS,iCAAiC,6CAA6C,GAAG,4BAA4B,YAAY,cAAc,SAAS,8CAA8C,gDAAgD,GAAG,mKAAmK,GAAG,+EAA+E,SAAS,uCAAuC,mBAAmB,gCAAgC,kBAAkB,sCAAsC,yEAAyE,SAAS,uCAAuC,wEAAwE,wEAAwE,EAAE,kCAAkC,iFAAiF,GAAG,mDAAmD,SAAS,wGAAwG,oBAAoB,6BAA6B,gFAAgF,mBAAmB,yCAAyC,mBAAmB,WAAW,MAAM,0EAA0E,IAAI,uBAAuB,qDAAqD,EAAE,iBAAiB,SAAS,2FAA2F,qCAAqC,+DAA+D,6DAA6D,EAAE,4BAA4B,uBAAuB,maAAma,SAAS,uIAAuI,oCAAoC,WAAW,mBAAmB,oCAAoC,sBAAsB,EAAE,YAAY,IAAI,KAAK,MAAM,wCAAwC,IAAI,uBAAuB,2DAA2D,6BAA6B,IAAI,EAAE,iBAAiB,SAAS,SAAS,mEAAmE,iCAAiC,mFAAmF,uFAAuF,qqBAAgmC;;;ACAzi1H;AACA;AACA;AACA;;AAM2C;;AAEpC;AACP;AACA,6BAA6B;AAC7B,oCAAoC;AACpC;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA,oCAAoC,2DAAgB;AACpD;;AAEA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,2BAA2B,2DAAgB;;AAE3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP,MAAM;AACN;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA,iDAAiD,SAAS,GAAG,UAAU;;AAEvE;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN,sDAAsD,SAAS,GAAG,UAAU;AAC5E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,YAAY,2DAA2D;;AAEvE,4DAA4D,QAAQ;;AAEpE;AACA;AACA;;AAEA;AACA;;AAEA;AACA,0DAA0D,QAAQ,GAAG,WAAW;AAChF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA,QAAQ;AACR;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,OAAO;;AAEP;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA,SAAS;AACT,mCAAmC;AACnC;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf;AACA,aAAa;AACb;AACA;;AAEA;AACA,SAAS;;AAET;AACA;AACA;AACA;AACA;AACA,WAAW;;AAEX;AACA;AACA;AACA;AACA;AACA,uBAAuB;AACvB;AACA,aAAa;AACb,qCAAqC;AACrC;AACA;;AAEA;AACA,oBAAoB,qBAAqB;AACzC,SAAS;;AAET;AACA;AACA;AACA,SAAS;AACT,OAAO;AACP,MAAM;AACN;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,wBAAwB,SAAS,GAAG,UAAU;;AAE9C;AACA;AACA;;AAEA,+DAA+D,SAAS;;AAExE;AACA,0BAA0B,oDAAW;AACrC;AACA;AACA,MAAM;AACN,6DAA6D,SAAS;AACtE;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,wDAAwD;AACxD;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA,4CAA4C,iBAAiB;AAC7D;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;;ACxYA;AACA;AACA;AACA;;AAE4C;;AAErC;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA,kBAAkB;AAClB;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,2BAA2B,kCAAe;AAC1C,eAAe;AACf,MAAM;AACN;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,0BAA0B;AAC1B,YAAY,kCAAe,YAAY,mBAAmB;AAC1D;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,6BAA6B,QAAQ;AACrC;AACA,MAAM;AACN;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN,8DAA8D,YAAY;AAC1E;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sDAAsD,YAAY;AAClE,MAAM;AACN,8DAA8D,YAAY;AAC1E;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sDAAsD,YAAY;AAClE,MAAM;AACN,+DAA+D,YAAY;AAC3E;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,2BAA2B,kCAAe;AAC1C;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB,YAAY,kCAAe,aAAa,iBAAiB;AACzD,MAAM;AACN;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,YAAY,kCAAe;AAC3B,YAAY,kCAAe;AAC3B;AACA,MAAM;AACN;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc,kCAAe,aAAa,mBAAmB;AAC7D;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,YAAY;AAChC;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;;;ACtXA;AACA;AACA;AACA;AACA;AAC4C;AACK;AACA;AACK;AAKX;AAC3C;AACA;AACA,oBAAoB,cAAc;AAClC,yBAAyB,YAAY;AACrC,qBAAqB,eAAe;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB,CAAiB;AACtC,oBAAoB,CAAgB;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa,6BAA6B;AAC1C,aAAa,6BAA6B;AAC1C,YAAY,+BAA+B;AAC3C,eAAe;AACf;AACA;AACA;AACA;AACA,EAAE,iCAAc,gBAAgB,kBAAkB;AAClD,EAAE,iCAAc,2BAA2B,oBAAoB;AAC/D;AACA;AACA;AACA;AACA;AACA,kCAAe;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA,mBAAmB,6BAA6B;AAChD,IAAI;AACJ;AACA,mBAAmB,sCAAsC;AACzD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB,6BAA6B;AAChD,IAAI;AACJ;AACA,mBAAmB,sCAAsC;AACzD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB,6BAA6B;AAChD,IAAI;AACJ;AACA;AACA,mBAAmB,sCAAsC;AACzD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB,eAAe;AAClC,IAAI;AACJ;AACA,mBAAmB,sCAAsC;AACzD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB,0BAA0B;AAC7C,IAAI;AACJ;AACA,mBAAmB,sCAAsC;AACzD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU,iEAAiE;AAC3E;AACA;AACA;AACA,mBAAmB,0CAA0C;AAC7D;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB,6CAA6C,cAAc,GAAG;AACjF;AACA;AACA;AACA;AACA,wBAAwB,CAA0B;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+CAA+C,YAAY;AAC3D,mBAAmB,qCAAqC;AACxD,IAAI;AACJ,wDAAwD,YAAY;AACpE,mBAAmB,sCAAsC;AACzD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB,eAAe;AAClC,IAAI;AACJ;AACA,mBAAmB,sCAAsC;AACzD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsB,qCAAqC;AAC3D;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA,kCAAe;AACf;AACA;AACA;AACA;AACA,IAAI,kCAAe;AACnB;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA,kCAAe;AACf;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA", "sources": ["webpack://webai-extension/./node_modules/webextension-polyfill/dist/browser-polyfill.js", "webpack://webai-extension/webpack/bootstrap", "webpack://webai-extension/webpack/runtime/compat get default export", "webpack://webai-extension/webpack/runtime/define property getters", "webpack://webai-extension/webpack/runtime/global", "webpack://webai-extension/webpack/runtime/hasOwnProperty shorthand", "webpack://webai-extension/webpack/runtime/make namespace object", "webpack://webai-extension/./node_modules/nanoid/url-alphabet/index.js", "webpack://webai-extension/./node_modules/nanoid/index.browser.js", "webpack://webai-extension/./src/background/websocket.js", "webpack://webai-extension/./lib/ai-models-bridge.esm.js", "webpack://webai-extension/./src/background/modelManager.js", "webpack://webai-extension/./src/shared/storage.js", "webpack://webai-extension/./src/background/index.js"], "sourcesContent": ["(function (global, factory) {\n  if (typeof define === \"function\" && define.amd) {\n    define(\"webextension-polyfill\", [\"module\"], factory);\n  } else if (typeof exports !== \"undefined\") {\n    factory(module);\n  } else {\n    var mod = {\n      exports: {}\n    };\n    factory(mod);\n    global.browser = mod.exports;\n  }\n})(typeof globalThis !== \"undefined\" ? globalThis : typeof self !== \"undefined\" ? self : this, function (module) {\n  /* webextension-polyfill - v0.10.0 - Fri Aug 12 2022 19:42:44 */\n\n  /* -*- Mode: indent-tabs-mode: nil; js-indent-level: 2 -*- */\n\n  /* vim: set sts=2 sw=2 et tw=80: */\n\n  /* This Source Code Form is subject to the terms of the Mozilla Public\n   * License, v. 2.0. If a copy of the MPL was not distributed with this\n   * file, You can obtain one at http://mozilla.org/MPL/2.0/. */\n  \"use strict\";\n\n  if (!globalThis.chrome?.runtime?.id) {\n    throw new Error(\"This script should only be loaded in a browser extension.\");\n  }\n\n  if (typeof globalThis.browser === \"undefined\" || Object.getPrototypeOf(globalThis.browser) !== Object.prototype) {\n    const CHROME_SEND_MESSAGE_CALLBACK_NO_RESPONSE_MESSAGE = \"The message port closed before a response was received.\"; // Wrapping the bulk of this polyfill in a one-time-use function is a minor\n    // optimization for Firefox. Since Spidermonkey does not fully parse the\n    // contents of a function until the first time it's called, and since it will\n    // never actually need to be called, this allows the polyfill to be included\n    // in Firefox nearly for free.\n\n    const wrapAPIs = extensionAPIs => {\n      // NOTE: apiMetadata is associated to the content of the api-metadata.json file\n      // at build time by replacing the following \"include\" with the content of the\n      // JSON file.\n      const apiMetadata = {\n        \"alarms\": {\n          \"clear\": {\n            \"minArgs\": 0,\n            \"maxArgs\": 1\n          },\n          \"clearAll\": {\n            \"minArgs\": 0,\n            \"maxArgs\": 0\n          },\n          \"get\": {\n            \"minArgs\": 0,\n            \"maxArgs\": 1\n          },\n          \"getAll\": {\n            \"minArgs\": 0,\n            \"maxArgs\": 0\n          }\n        },\n        \"bookmarks\": {\n          \"create\": {\n            \"minArgs\": 1,\n            \"maxArgs\": 1\n          },\n          \"get\": {\n            \"minArgs\": 1,\n            \"maxArgs\": 1\n          },\n          \"getChildren\": {\n            \"minArgs\": 1,\n            \"maxArgs\": 1\n          },\n          \"getRecent\": {\n            \"minArgs\": 1,\n            \"maxArgs\": 1\n          },\n          \"getSubTree\": {\n            \"minArgs\": 1,\n            \"maxArgs\": 1\n          },\n          \"getTree\": {\n            \"minArgs\": 0,\n            \"maxArgs\": 0\n          },\n          \"move\": {\n            \"minArgs\": 2,\n            \"maxArgs\": 2\n          },\n          \"remove\": {\n            \"minArgs\": 1,\n            \"maxArgs\": 1\n          },\n          \"removeTree\": {\n            \"minArgs\": 1,\n            \"maxArgs\": 1\n          },\n          \"search\": {\n            \"minArgs\": 1,\n            \"maxArgs\": 1\n          },\n          \"update\": {\n            \"minArgs\": 2,\n            \"maxArgs\": 2\n          }\n        },\n        \"browserAction\": {\n          \"disable\": {\n            \"minArgs\": 0,\n            \"maxArgs\": 1,\n            \"fallbackToNoCallback\": true\n          },\n          \"enable\": {\n            \"minArgs\": 0,\n            \"maxArgs\": 1,\n            \"fallbackToNoCallback\": true\n          },\n          \"getBadgeBackgroundColor\": {\n            \"minArgs\": 1,\n            \"maxArgs\": 1\n          },\n          \"getBadgeText\": {\n            \"minArgs\": 1,\n            \"maxArgs\": 1\n          },\n          \"getPopup\": {\n            \"minArgs\": 1,\n            \"maxArgs\": 1\n          },\n          \"getTitle\": {\n            \"minArgs\": 1,\n            \"maxArgs\": 1\n          },\n          \"openPopup\": {\n            \"minArgs\": 0,\n            \"maxArgs\": 0\n          },\n          \"setBadgeBackgroundColor\": {\n            \"minArgs\": 1,\n            \"maxArgs\": 1,\n            \"fallbackToNoCallback\": true\n          },\n          \"setBadgeText\": {\n            \"minArgs\": 1,\n            \"maxArgs\": 1,\n            \"fallbackToNoCallback\": true\n          },\n          \"setIcon\": {\n            \"minArgs\": 1,\n            \"maxArgs\": 1\n          },\n          \"setPopup\": {\n            \"minArgs\": 1,\n            \"maxArgs\": 1,\n            \"fallbackToNoCallback\": true\n          },\n          \"setTitle\": {\n            \"minArgs\": 1,\n            \"maxArgs\": 1,\n            \"fallbackToNoCallback\": true\n          }\n        },\n        \"browsingData\": {\n          \"remove\": {\n            \"minArgs\": 2,\n            \"maxArgs\": 2\n          },\n          \"removeCache\": {\n            \"minArgs\": 1,\n            \"maxArgs\": 1\n          },\n          \"removeCookies\": {\n            \"minArgs\": 1,\n            \"maxArgs\": 1\n          },\n          \"removeDownloads\": {\n            \"minArgs\": 1,\n            \"maxArgs\": 1\n          },\n          \"removeFormData\": {\n            \"minArgs\": 1,\n            \"maxArgs\": 1\n          },\n          \"removeHistory\": {\n            \"minArgs\": 1,\n            \"maxArgs\": 1\n          },\n          \"removeLocalStorage\": {\n            \"minArgs\": 1,\n            \"maxArgs\": 1\n          },\n          \"removePasswords\": {\n            \"minArgs\": 1,\n            \"maxArgs\": 1\n          },\n          \"removePluginData\": {\n            \"minArgs\": 1,\n            \"maxArgs\": 1\n          },\n          \"settings\": {\n            \"minArgs\": 0,\n            \"maxArgs\": 0\n          }\n        },\n        \"commands\": {\n          \"getAll\": {\n            \"minArgs\": 0,\n            \"maxArgs\": 0\n          }\n        },\n        \"contextMenus\": {\n          \"remove\": {\n            \"minArgs\": 1,\n            \"maxArgs\": 1\n          },\n          \"removeAll\": {\n            \"minArgs\": 0,\n            \"maxArgs\": 0\n          },\n          \"update\": {\n            \"minArgs\": 2,\n            \"maxArgs\": 2\n          }\n        },\n        \"cookies\": {\n          \"get\": {\n            \"minArgs\": 1,\n            \"maxArgs\": 1\n          },\n          \"getAll\": {\n            \"minArgs\": 1,\n            \"maxArgs\": 1\n          },\n          \"getAllCookieStores\": {\n            \"minArgs\": 0,\n            \"maxArgs\": 0\n          },\n          \"remove\": {\n            \"minArgs\": 1,\n            \"maxArgs\": 1\n          },\n          \"set\": {\n            \"minArgs\": 1,\n            \"maxArgs\": 1\n          }\n        },\n        \"devtools\": {\n          \"inspectedWindow\": {\n            \"eval\": {\n              \"minArgs\": 1,\n              \"maxArgs\": 2,\n              \"singleCallbackArg\": false\n            }\n          },\n          \"panels\": {\n            \"create\": {\n              \"minArgs\": 3,\n              \"maxArgs\": 3,\n              \"singleCallbackArg\": true\n            },\n            \"elements\": {\n              \"createSidebarPane\": {\n                \"minArgs\": 1,\n                \"maxArgs\": 1\n              }\n            }\n          }\n        },\n        \"downloads\": {\n          \"cancel\": {\n            \"minArgs\": 1,\n            \"maxArgs\": 1\n          },\n          \"download\": {\n            \"minArgs\": 1,\n            \"maxArgs\": 1\n          },\n          \"erase\": {\n            \"minArgs\": 1,\n            \"maxArgs\": 1\n          },\n          \"getFileIcon\": {\n            \"minArgs\": 1,\n            \"maxArgs\": 2\n          },\n          \"open\": {\n            \"minArgs\": 1,\n            \"maxArgs\": 1,\n            \"fallbackToNoCallback\": true\n          },\n          \"pause\": {\n            \"minArgs\": 1,\n            \"maxArgs\": 1\n          },\n          \"removeFile\": {\n            \"minArgs\": 1,\n            \"maxArgs\": 1\n          },\n          \"resume\": {\n            \"minArgs\": 1,\n            \"maxArgs\": 1\n          },\n          \"search\": {\n            \"minArgs\": 1,\n            \"maxArgs\": 1\n          },\n          \"show\": {\n            \"minArgs\": 1,\n            \"maxArgs\": 1,\n            \"fallbackToNoCallback\": true\n          }\n        },\n        \"extension\": {\n          \"isAllowedFileSchemeAccess\": {\n            \"minArgs\": 0,\n            \"maxArgs\": 0\n          },\n          \"isAllowedIncognitoAccess\": {\n            \"minArgs\": 0,\n            \"maxArgs\": 0\n          }\n        },\n        \"history\": {\n          \"addUrl\": {\n            \"minArgs\": 1,\n            \"maxArgs\": 1\n          },\n          \"deleteAll\": {\n            \"minArgs\": 0,\n            \"maxArgs\": 0\n          },\n          \"deleteRange\": {\n            \"minArgs\": 1,\n            \"maxArgs\": 1\n          },\n          \"deleteUrl\": {\n            \"minArgs\": 1,\n            \"maxArgs\": 1\n          },\n          \"getVisits\": {\n            \"minArgs\": 1,\n            \"maxArgs\": 1\n          },\n          \"search\": {\n            \"minArgs\": 1,\n            \"maxArgs\": 1\n          }\n        },\n        \"i18n\": {\n          \"detectLanguage\": {\n            \"minArgs\": 1,\n            \"maxArgs\": 1\n          },\n          \"getAcceptLanguages\": {\n            \"minArgs\": 0,\n            \"maxArgs\": 0\n          }\n        },\n        \"identity\": {\n          \"launchWebAuthFlow\": {\n            \"minArgs\": 1,\n            \"maxArgs\": 1\n          }\n        },\n        \"idle\": {\n          \"queryState\": {\n            \"minArgs\": 1,\n            \"maxArgs\": 1\n          }\n        },\n        \"management\": {\n          \"get\": {\n            \"minArgs\": 1,\n            \"maxArgs\": 1\n          },\n          \"getAll\": {\n            \"minArgs\": 0,\n            \"maxArgs\": 0\n          },\n          \"getSelf\": {\n            \"minArgs\": 0,\n            \"maxArgs\": 0\n          },\n          \"setEnabled\": {\n            \"minArgs\": 2,\n            \"maxArgs\": 2\n          },\n          \"uninstallSelf\": {\n            \"minArgs\": 0,\n            \"maxArgs\": 1\n          }\n        },\n        \"notifications\": {\n          \"clear\": {\n            \"minArgs\": 1,\n            \"maxArgs\": 1\n          },\n          \"create\": {\n            \"minArgs\": 1,\n            \"maxArgs\": 2\n          },\n          \"getAll\": {\n            \"minArgs\": 0,\n            \"maxArgs\": 0\n          },\n          \"getPermissionLevel\": {\n            \"minArgs\": 0,\n            \"maxArgs\": 0\n          },\n          \"update\": {\n            \"minArgs\": 2,\n            \"maxArgs\": 2\n          }\n        },\n        \"pageAction\": {\n          \"getPopup\": {\n            \"minArgs\": 1,\n            \"maxArgs\": 1\n          },\n          \"getTitle\": {\n            \"minArgs\": 1,\n            \"maxArgs\": 1\n          },\n          \"hide\": {\n            \"minArgs\": 1,\n            \"maxArgs\": 1,\n            \"fallbackToNoCallback\": true\n          },\n          \"setIcon\": {\n            \"minArgs\": 1,\n            \"maxArgs\": 1\n          },\n          \"setPopup\": {\n            \"minArgs\": 1,\n            \"maxArgs\": 1,\n            \"fallbackToNoCallback\": true\n          },\n          \"setTitle\": {\n            \"minArgs\": 1,\n            \"maxArgs\": 1,\n            \"fallbackToNoCallback\": true\n          },\n          \"show\": {\n            \"minArgs\": 1,\n            \"maxArgs\": 1,\n            \"fallbackToNoCallback\": true\n          }\n        },\n        \"permissions\": {\n          \"contains\": {\n            \"minArgs\": 1,\n            \"maxArgs\": 1\n          },\n          \"getAll\": {\n            \"minArgs\": 0,\n            \"maxArgs\": 0\n          },\n          \"remove\": {\n            \"minArgs\": 1,\n            \"maxArgs\": 1\n          },\n          \"request\": {\n            \"minArgs\": 1,\n            \"maxArgs\": 1\n          }\n        },\n        \"runtime\": {\n          \"getBackgroundPage\": {\n            \"minArgs\": 0,\n            \"maxArgs\": 0\n          },\n          \"getPlatformInfo\": {\n            \"minArgs\": 0,\n            \"maxArgs\": 0\n          },\n          \"openOptionsPage\": {\n            \"minArgs\": 0,\n            \"maxArgs\": 0\n          },\n          \"requestUpdateCheck\": {\n            \"minArgs\": 0,\n            \"maxArgs\": 0\n          },\n          \"sendMessage\": {\n            \"minArgs\": 1,\n            \"maxArgs\": 3\n          },\n          \"sendNativeMessage\": {\n            \"minArgs\": 2,\n            \"maxArgs\": 2\n          },\n          \"setUninstallURL\": {\n            \"minArgs\": 1,\n            \"maxArgs\": 1\n          }\n        },\n        \"sessions\": {\n          \"getDevices\": {\n            \"minArgs\": 0,\n            \"maxArgs\": 1\n          },\n          \"getRecentlyClosed\": {\n            \"minArgs\": 0,\n            \"maxArgs\": 1\n          },\n          \"restore\": {\n            \"minArgs\": 0,\n            \"maxArgs\": 1\n          }\n        },\n        \"storage\": {\n          \"local\": {\n            \"clear\": {\n              \"minArgs\": 0,\n              \"maxArgs\": 0\n            },\n            \"get\": {\n              \"minArgs\": 0,\n              \"maxArgs\": 1\n            },\n            \"getBytesInUse\": {\n              \"minArgs\": 0,\n              \"maxArgs\": 1\n            },\n            \"remove\": {\n              \"minArgs\": 1,\n              \"maxArgs\": 1\n            },\n            \"set\": {\n              \"minArgs\": 1,\n              \"maxArgs\": 1\n            }\n          },\n          \"managed\": {\n            \"get\": {\n              \"minArgs\": 0,\n              \"maxArgs\": 1\n            },\n            \"getBytesInUse\": {\n              \"minArgs\": 0,\n              \"maxArgs\": 1\n            }\n          },\n          \"sync\": {\n            \"clear\": {\n              \"minArgs\": 0,\n              \"maxArgs\": 0\n            },\n            \"get\": {\n              \"minArgs\": 0,\n              \"maxArgs\": 1\n            },\n            \"getBytesInUse\": {\n              \"minArgs\": 0,\n              \"maxArgs\": 1\n            },\n            \"remove\": {\n              \"minArgs\": 1,\n              \"maxArgs\": 1\n            },\n            \"set\": {\n              \"minArgs\": 1,\n              \"maxArgs\": 1\n            }\n          }\n        },\n        \"tabs\": {\n          \"captureVisibleTab\": {\n            \"minArgs\": 0,\n            \"maxArgs\": 2\n          },\n          \"create\": {\n            \"minArgs\": 1,\n            \"maxArgs\": 1\n          },\n          \"detectLanguage\": {\n            \"minArgs\": 0,\n            \"maxArgs\": 1\n          },\n          \"discard\": {\n            \"minArgs\": 0,\n            \"maxArgs\": 1\n          },\n          \"duplicate\": {\n            \"minArgs\": 1,\n            \"maxArgs\": 1\n          },\n          \"executeScript\": {\n            \"minArgs\": 1,\n            \"maxArgs\": 2\n          },\n          \"get\": {\n            \"minArgs\": 1,\n            \"maxArgs\": 1\n          },\n          \"getCurrent\": {\n            \"minArgs\": 0,\n            \"maxArgs\": 0\n          },\n          \"getZoom\": {\n            \"minArgs\": 0,\n            \"maxArgs\": 1\n          },\n          \"getZoomSettings\": {\n            \"minArgs\": 0,\n            \"maxArgs\": 1\n          },\n          \"goBack\": {\n            \"minArgs\": 0,\n            \"maxArgs\": 1\n          },\n          \"goForward\": {\n            \"minArgs\": 0,\n            \"maxArgs\": 1\n          },\n          \"highlight\": {\n            \"minArgs\": 1,\n            \"maxArgs\": 1\n          },\n          \"insertCSS\": {\n            \"minArgs\": 1,\n            \"maxArgs\": 2\n          },\n          \"move\": {\n            \"minArgs\": 2,\n            \"maxArgs\": 2\n          },\n          \"query\": {\n            \"minArgs\": 1,\n            \"maxArgs\": 1\n          },\n          \"reload\": {\n            \"minArgs\": 0,\n            \"maxArgs\": 2\n          },\n          \"remove\": {\n            \"minArgs\": 1,\n            \"maxArgs\": 1\n          },\n          \"removeCSS\": {\n            \"minArgs\": 1,\n            \"maxArgs\": 2\n          },\n          \"sendMessage\": {\n            \"minArgs\": 2,\n            \"maxArgs\": 3\n          },\n          \"setZoom\": {\n            \"minArgs\": 1,\n            \"maxArgs\": 2\n          },\n          \"setZoomSettings\": {\n            \"minArgs\": 1,\n            \"maxArgs\": 2\n          },\n          \"update\": {\n            \"minArgs\": 1,\n            \"maxArgs\": 2\n          }\n        },\n        \"topSites\": {\n          \"get\": {\n            \"minArgs\": 0,\n            \"maxArgs\": 0\n          }\n        },\n        \"webNavigation\": {\n          \"getAllFrames\": {\n            \"minArgs\": 1,\n            \"maxArgs\": 1\n          },\n          \"getFrame\": {\n            \"minArgs\": 1,\n            \"maxArgs\": 1\n          }\n        },\n        \"webRequest\": {\n          \"handlerBehaviorChanged\": {\n            \"minArgs\": 0,\n            \"maxArgs\": 0\n          }\n        },\n        \"windows\": {\n          \"create\": {\n            \"minArgs\": 0,\n            \"maxArgs\": 1\n          },\n          \"get\": {\n            \"minArgs\": 1,\n            \"maxArgs\": 2\n          },\n          \"getAll\": {\n            \"minArgs\": 0,\n            \"maxArgs\": 1\n          },\n          \"getCurrent\": {\n            \"minArgs\": 0,\n            \"maxArgs\": 1\n          },\n          \"getLastFocused\": {\n            \"minArgs\": 0,\n            \"maxArgs\": 1\n          },\n          \"remove\": {\n            \"minArgs\": 1,\n            \"maxArgs\": 1\n          },\n          \"update\": {\n            \"minArgs\": 2,\n            \"maxArgs\": 2\n          }\n        }\n      };\n\n      if (Object.keys(apiMetadata).length === 0) {\n        throw new Error(\"api-metadata.json has not been included in browser-polyfill\");\n      }\n      /**\n       * A WeakMap subclass which creates and stores a value for any key which does\n       * not exist when accessed, but behaves exactly as an ordinary WeakMap\n       * otherwise.\n       *\n       * @param {function} createItem\n       *        A function which will be called in order to create the value for any\n       *        key which does not exist, the first time it is accessed. The\n       *        function receives, as its only argument, the key being created.\n       */\n\n\n      class DefaultWeakMap extends WeakMap {\n        constructor(createItem, items = undefined) {\n          super(items);\n          this.createItem = createItem;\n        }\n\n        get(key) {\n          if (!this.has(key)) {\n            this.set(key, this.createItem(key));\n          }\n\n          return super.get(key);\n        }\n\n      }\n      /**\n       * Returns true if the given object is an object with a `then` method, and can\n       * therefore be assumed to behave as a Promise.\n       *\n       * @param {*} value The value to test.\n       * @returns {boolean} True if the value is thenable.\n       */\n\n\n      const isThenable = value => {\n        return value && typeof value === \"object\" && typeof value.then === \"function\";\n      };\n      /**\n       * Creates and returns a function which, when called, will resolve or reject\n       * the given promise based on how it is called:\n       *\n       * - If, when called, `chrome.runtime.lastError` contains a non-null object,\n       *   the promise is rejected with that value.\n       * - If the function is called with exactly one argument, the promise is\n       *   resolved to that value.\n       * - Otherwise, the promise is resolved to an array containing all of the\n       *   function's arguments.\n       *\n       * @param {object} promise\n       *        An object containing the resolution and rejection functions of a\n       *        promise.\n       * @param {function} promise.resolve\n       *        The promise's resolution function.\n       * @param {function} promise.reject\n       *        The promise's rejection function.\n       * @param {object} metadata\n       *        Metadata about the wrapped method which has created the callback.\n       * @param {boolean} metadata.singleCallbackArg\n       *        Whether or not the promise is resolved with only the first\n       *        argument of the callback, alternatively an array of all the\n       *        callback arguments is resolved. By default, if the callback\n       *        function is invoked with only a single argument, that will be\n       *        resolved to the promise, while all arguments will be resolved as\n       *        an array if multiple are given.\n       *\n       * @returns {function}\n       *        The generated callback function.\n       */\n\n\n      const makeCallback = (promise, metadata) => {\n        return (...callbackArgs) => {\n          if (extensionAPIs.runtime.lastError) {\n            promise.reject(new Error(extensionAPIs.runtime.lastError.message));\n          } else if (metadata.singleCallbackArg || callbackArgs.length <= 1 && metadata.singleCallbackArg !== false) {\n            promise.resolve(callbackArgs[0]);\n          } else {\n            promise.resolve(callbackArgs);\n          }\n        };\n      };\n\n      const pluralizeArguments = numArgs => numArgs == 1 ? \"argument\" : \"arguments\";\n      /**\n       * Creates a wrapper function for a method with the given name and metadata.\n       *\n       * @param {string} name\n       *        The name of the method which is being wrapped.\n       * @param {object} metadata\n       *        Metadata about the method being wrapped.\n       * @param {integer} metadata.minArgs\n       *        The minimum number of arguments which must be passed to the\n       *        function. If called with fewer than this number of arguments, the\n       *        wrapper will raise an exception.\n       * @param {integer} metadata.maxArgs\n       *        The maximum number of arguments which may be passed to the\n       *        function. If called with more than this number of arguments, the\n       *        wrapper will raise an exception.\n       * @param {boolean} metadata.singleCallbackArg\n       *        Whether or not the promise is resolved with only the first\n       *        argument of the callback, alternatively an array of all the\n       *        callback arguments is resolved. By default, if the callback\n       *        function is invoked with only a single argument, that will be\n       *        resolved to the promise, while all arguments will be resolved as\n       *        an array if multiple are given.\n       *\n       * @returns {function(object, ...*)}\n       *       The generated wrapper function.\n       */\n\n\n      const wrapAsyncFunction = (name, metadata) => {\n        return function asyncFunctionWrapper(target, ...args) {\n          if (args.length < metadata.minArgs) {\n            throw new Error(`Expected at least ${metadata.minArgs} ${pluralizeArguments(metadata.minArgs)} for ${name}(), got ${args.length}`);\n          }\n\n          if (args.length > metadata.maxArgs) {\n            throw new Error(`Expected at most ${metadata.maxArgs} ${pluralizeArguments(metadata.maxArgs)} for ${name}(), got ${args.length}`);\n          }\n\n          return new Promise((resolve, reject) => {\n            if (metadata.fallbackToNoCallback) {\n              // This API method has currently no callback on Chrome, but it return a promise on Firefox,\n              // and so the polyfill will try to call it with a callback first, and it will fallback\n              // to not passing the callback if the first call fails.\n              try {\n                target[name](...args, makeCallback({\n                  resolve,\n                  reject\n                }, metadata));\n              } catch (cbError) {\n                console.warn(`${name} API method doesn't seem to support the callback parameter, ` + \"falling back to call it without a callback: \", cbError);\n                target[name](...args); // Update the API method metadata, so that the next API calls will not try to\n                // use the unsupported callback anymore.\n\n                metadata.fallbackToNoCallback = false;\n                metadata.noCallback = true;\n                resolve();\n              }\n            } else if (metadata.noCallback) {\n              target[name](...args);\n              resolve();\n            } else {\n              target[name](...args, makeCallback({\n                resolve,\n                reject\n              }, metadata));\n            }\n          });\n        };\n      };\n      /**\n       * Wraps an existing method of the target object, so that calls to it are\n       * intercepted by the given wrapper function. The wrapper function receives,\n       * as its first argument, the original `target` object, followed by each of\n       * the arguments passed to the original method.\n       *\n       * @param {object} target\n       *        The original target object that the wrapped method belongs to.\n       * @param {function} method\n       *        The method being wrapped. This is used as the target of the Proxy\n       *        object which is created to wrap the method.\n       * @param {function} wrapper\n       *        The wrapper function which is called in place of a direct invocation\n       *        of the wrapped method.\n       *\n       * @returns {Proxy<function>}\n       *        A Proxy object for the given method, which invokes the given wrapper\n       *        method in its place.\n       */\n\n\n      const wrapMethod = (target, method, wrapper) => {\n        return new Proxy(method, {\n          apply(targetMethod, thisObj, args) {\n            return wrapper.call(thisObj, target, ...args);\n          }\n\n        });\n      };\n\n      let hasOwnProperty = Function.call.bind(Object.prototype.hasOwnProperty);\n      /**\n       * Wraps an object in a Proxy which intercepts and wraps certain methods\n       * based on the given `wrappers` and `metadata` objects.\n       *\n       * @param {object} target\n       *        The target object to wrap.\n       *\n       * @param {object} [wrappers = {}]\n       *        An object tree containing wrapper functions for special cases. Any\n       *        function present in this object tree is called in place of the\n       *        method in the same location in the `target` object tree. These\n       *        wrapper methods are invoked as described in {@see wrapMethod}.\n       *\n       * @param {object} [metadata = {}]\n       *        An object tree containing metadata used to automatically generate\n       *        Promise-based wrapper functions for asynchronous. Any function in\n       *        the `target` object tree which has a corresponding metadata object\n       *        in the same location in the `metadata` tree is replaced with an\n       *        automatically-generated wrapper function, as described in\n       *        {@see wrapAsyncFunction}\n       *\n       * @returns {Proxy<object>}\n       */\n\n      const wrapObject = (target, wrappers = {}, metadata = {}) => {\n        let cache = Object.create(null);\n        let handlers = {\n          has(proxyTarget, prop) {\n            return prop in target || prop in cache;\n          },\n\n          get(proxyTarget, prop, receiver) {\n            if (prop in cache) {\n              return cache[prop];\n            }\n\n            if (!(prop in target)) {\n              return undefined;\n            }\n\n            let value = target[prop];\n\n            if (typeof value === \"function\") {\n              // This is a method on the underlying object. Check if we need to do\n              // any wrapping.\n              if (typeof wrappers[prop] === \"function\") {\n                // We have a special-case wrapper for this method.\n                value = wrapMethod(target, target[prop], wrappers[prop]);\n              } else if (hasOwnProperty(metadata, prop)) {\n                // This is an async method that we have metadata for. Create a\n                // Promise wrapper for it.\n                let wrapper = wrapAsyncFunction(prop, metadata[prop]);\n                value = wrapMethod(target, target[prop], wrapper);\n              } else {\n                // This is a method that we don't know or care about. Return the\n                // original method, bound to the underlying object.\n                value = value.bind(target);\n              }\n            } else if (typeof value === \"object\" && value !== null && (hasOwnProperty(wrappers, prop) || hasOwnProperty(metadata, prop))) {\n              // This is an object that we need to do some wrapping for the children\n              // of. Create a sub-object wrapper for it with the appropriate child\n              // metadata.\n              value = wrapObject(value, wrappers[prop], metadata[prop]);\n            } else if (hasOwnProperty(metadata, \"*\")) {\n              // Wrap all properties in * namespace.\n              value = wrapObject(value, wrappers[prop], metadata[\"*\"]);\n            } else {\n              // We don't need to do any wrapping for this property,\n              // so just forward all access to the underlying object.\n              Object.defineProperty(cache, prop, {\n                configurable: true,\n                enumerable: true,\n\n                get() {\n                  return target[prop];\n                },\n\n                set(value) {\n                  target[prop] = value;\n                }\n\n              });\n              return value;\n            }\n\n            cache[prop] = value;\n            return value;\n          },\n\n          set(proxyTarget, prop, value, receiver) {\n            if (prop in cache) {\n              cache[prop] = value;\n            } else {\n              target[prop] = value;\n            }\n\n            return true;\n          },\n\n          defineProperty(proxyTarget, prop, desc) {\n            return Reflect.defineProperty(cache, prop, desc);\n          },\n\n          deleteProperty(proxyTarget, prop) {\n            return Reflect.deleteProperty(cache, prop);\n          }\n\n        }; // Per contract of the Proxy API, the \"get\" proxy handler must return the\n        // original value of the target if that value is declared read-only and\n        // non-configurable. For this reason, we create an object with the\n        // prototype set to `target` instead of using `target` directly.\n        // Otherwise we cannot return a custom object for APIs that\n        // are declared read-only and non-configurable, such as `chrome.devtools`.\n        //\n        // The proxy handlers themselves will still use the original `target`\n        // instead of the `proxyTarget`, so that the methods and properties are\n        // dereferenced via the original targets.\n\n        let proxyTarget = Object.create(target);\n        return new Proxy(proxyTarget, handlers);\n      };\n      /**\n       * Creates a set of wrapper functions for an event object, which handles\n       * wrapping of listener functions that those messages are passed.\n       *\n       * A single wrapper is created for each listener function, and stored in a\n       * map. Subsequent calls to `addListener`, `hasListener`, or `removeListener`\n       * retrieve the original wrapper, so that  attempts to remove a\n       * previously-added listener work as expected.\n       *\n       * @param {DefaultWeakMap<function, function>} wrapperMap\n       *        A DefaultWeakMap object which will create the appropriate wrapper\n       *        for a given listener function when one does not exist, and retrieve\n       *        an existing one when it does.\n       *\n       * @returns {object}\n       */\n\n\n      const wrapEvent = wrapperMap => ({\n        addListener(target, listener, ...args) {\n          target.addListener(wrapperMap.get(listener), ...args);\n        },\n\n        hasListener(target, listener) {\n          return target.hasListener(wrapperMap.get(listener));\n        },\n\n        removeListener(target, listener) {\n          target.removeListener(wrapperMap.get(listener));\n        }\n\n      });\n\n      const onRequestFinishedWrappers = new DefaultWeakMap(listener => {\n        if (typeof listener !== \"function\") {\n          return listener;\n        }\n        /**\n         * Wraps an onRequestFinished listener function so that it will return a\n         * `getContent()` property which returns a `Promise` rather than using a\n         * callback API.\n         *\n         * @param {object} req\n         *        The HAR entry object representing the network request.\n         */\n\n\n        return function onRequestFinished(req) {\n          const wrappedReq = wrapObject(req, {}\n          /* wrappers */\n          , {\n            getContent: {\n              minArgs: 0,\n              maxArgs: 0\n            }\n          });\n          listener(wrappedReq);\n        };\n      });\n      const onMessageWrappers = new DefaultWeakMap(listener => {\n        if (typeof listener !== \"function\") {\n          return listener;\n        }\n        /**\n         * Wraps a message listener function so that it may send responses based on\n         * its return value, rather than by returning a sentinel value and calling a\n         * callback. If the listener function returns a Promise, the response is\n         * sent when the promise either resolves or rejects.\n         *\n         * @param {*} message\n         *        The message sent by the other end of the channel.\n         * @param {object} sender\n         *        Details about the sender of the message.\n         * @param {function(*)} sendResponse\n         *        A callback which, when called with an arbitrary argument, sends\n         *        that value as a response.\n         * @returns {boolean}\n         *        True if the wrapped listener returned a Promise, which will later\n         *        yield a response. False otherwise.\n         */\n\n\n        return function onMessage(message, sender, sendResponse) {\n          let didCallSendResponse = false;\n          let wrappedSendResponse;\n          let sendResponsePromise = new Promise(resolve => {\n            wrappedSendResponse = function (response) {\n              didCallSendResponse = true;\n              resolve(response);\n            };\n          });\n          let result;\n\n          try {\n            result = listener(message, sender, wrappedSendResponse);\n          } catch (err) {\n            result = Promise.reject(err);\n          }\n\n          const isResultThenable = result !== true && isThenable(result); // If the listener didn't returned true or a Promise, or called\n          // wrappedSendResponse synchronously, we can exit earlier\n          // because there will be no response sent from this listener.\n\n          if (result !== true && !isResultThenable && !didCallSendResponse) {\n            return false;\n          } // A small helper to send the message if the promise resolves\n          // and an error if the promise rejects (a wrapped sendMessage has\n          // to translate the message into a resolved promise or a rejected\n          // promise).\n\n\n          const sendPromisedResult = promise => {\n            promise.then(msg => {\n              // send the message value.\n              sendResponse(msg);\n            }, error => {\n              // Send a JSON representation of the error if the rejected value\n              // is an instance of error, or the object itself otherwise.\n              let message;\n\n              if (error && (error instanceof Error || typeof error.message === \"string\")) {\n                message = error.message;\n              } else {\n                message = \"An unexpected error occurred\";\n              }\n\n              sendResponse({\n                __mozWebExtensionPolyfillReject__: true,\n                message\n              });\n            }).catch(err => {\n              // Print an error on the console if unable to send the response.\n              console.error(\"Failed to send onMessage rejected reply\", err);\n            });\n          }; // If the listener returned a Promise, send the resolved value as a\n          // result, otherwise wait the promise related to the wrappedSendResponse\n          // callback to resolve and send it as a response.\n\n\n          if (isResultThenable) {\n            sendPromisedResult(result);\n          } else {\n            sendPromisedResult(sendResponsePromise);\n          } // Let Chrome know that the listener is replying.\n\n\n          return true;\n        };\n      });\n\n      const wrappedSendMessageCallback = ({\n        reject,\n        resolve\n      }, reply) => {\n        if (extensionAPIs.runtime.lastError) {\n          // Detect when none of the listeners replied to the sendMessage call and resolve\n          // the promise to undefined as in Firefox.\n          // See https://github.com/mozilla/webextension-polyfill/issues/130\n          if (extensionAPIs.runtime.lastError.message === CHROME_SEND_MESSAGE_CALLBACK_NO_RESPONSE_MESSAGE) {\n            resolve();\n          } else {\n            reject(new Error(extensionAPIs.runtime.lastError.message));\n          }\n        } else if (reply && reply.__mozWebExtensionPolyfillReject__) {\n          // Convert back the JSON representation of the error into\n          // an Error instance.\n          reject(new Error(reply.message));\n        } else {\n          resolve(reply);\n        }\n      };\n\n      const wrappedSendMessage = (name, metadata, apiNamespaceObj, ...args) => {\n        if (args.length < metadata.minArgs) {\n          throw new Error(`Expected at least ${metadata.minArgs} ${pluralizeArguments(metadata.minArgs)} for ${name}(), got ${args.length}`);\n        }\n\n        if (args.length > metadata.maxArgs) {\n          throw new Error(`Expected at most ${metadata.maxArgs} ${pluralizeArguments(metadata.maxArgs)} for ${name}(), got ${args.length}`);\n        }\n\n        return new Promise((resolve, reject) => {\n          const wrappedCb = wrappedSendMessageCallback.bind(null, {\n            resolve,\n            reject\n          });\n          args.push(wrappedCb);\n          apiNamespaceObj.sendMessage(...args);\n        });\n      };\n\n      const staticWrappers = {\n        devtools: {\n          network: {\n            onRequestFinished: wrapEvent(onRequestFinishedWrappers)\n          }\n        },\n        runtime: {\n          onMessage: wrapEvent(onMessageWrappers),\n          onMessageExternal: wrapEvent(onMessageWrappers),\n          sendMessage: wrappedSendMessage.bind(null, \"sendMessage\", {\n            minArgs: 1,\n            maxArgs: 3\n          })\n        },\n        tabs: {\n          sendMessage: wrappedSendMessage.bind(null, \"sendMessage\", {\n            minArgs: 2,\n            maxArgs: 3\n          })\n        }\n      };\n      const settingMetadata = {\n        clear: {\n          minArgs: 1,\n          maxArgs: 1\n        },\n        get: {\n          minArgs: 1,\n          maxArgs: 1\n        },\n        set: {\n          minArgs: 1,\n          maxArgs: 1\n        }\n      };\n      apiMetadata.privacy = {\n        network: {\n          \"*\": settingMetadata\n        },\n        services: {\n          \"*\": settingMetadata\n        },\n        websites: {\n          \"*\": settingMetadata\n        }\n      };\n      return wrapObject(extensionAPIs, staticWrappers, apiMetadata);\n    }; // The build process adds a UMD wrapper around this file, which makes the\n    // `module` variable available.\n\n\n    module.exports = wrapAPIs(chrome);\n  } else {\n    module.exports = globalThis.browser;\n  }\n});\n//# sourceMappingURL=browser-polyfill.js.map\n", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "// getDefaultExport function for compatibility with non-harmony modules\n__webpack_require__.n = (module) => {\n\tvar getter = module && module.__esModule ?\n\t\t() => (module['default']) :\n\t\t() => (module);\n\t__webpack_require__.d(getter, { a: getter });\n\treturn getter;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.g = (function() {\n\tif (typeof globalThis === 'object') return globalThis;\n\ttry {\n\t\treturn this || new Function('return this')();\n\t} catch (e) {\n\t\tif (typeof window === 'object') return window;\n\t}\n})();", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "export const urlAlphabet =\n  'useandom-26T198340PX75pxJACKVERYMINDBUSHWOLF_GQZbfghjklqvwyzrict'\n", "/* @ts-self-types=\"./index.d.ts\" */\nimport { url<PERSON>lphabet as scopedUrlAlphabet } from './url-alphabet/index.js'\nexport { urlAlphabet } from './url-alphabet/index.js'\nexport let random = bytes => crypto.getRandomValues(new Uint8Array(bytes))\nexport let customRandom = (alphabet, defaultSize, getRandom) => {\n  let mask = (2 << Math.log2(alphabet.length - 1)) - 1\n  let step = -~((1.6 * mask * defaultSize) / alphabet.length)\n  return (size = defaultSize) => {\n    let id = ''\n    while (true) {\n      let bytes = getRandom(step)\n      let j = step | 0\n      while (j--) {\n        id += alphabet[bytes[j] & mask] || ''\n        if (id.length >= size) return id\n      }\n    }\n  }\n}\nexport let customAlphabet = (alphabet, size = 21) =>\n  customRandom(alphabet, size | 0, random)\nexport let nanoid = (size = 21) => {\n  let id = ''\n  let bytes = crypto.getRandomValues(new Uint8Array((size |= 0)))\n  while (size--) {\n    id += scopedUrlAlphabet[bytes[size] & 63]\n  }\n  return id\n}\n", "/**\r\n * WebSocket client for communication with the server\r\n */\r\n\r\nimport { nanoid } from 'nanoid';\r\n\r\nexport class WebSocketClient {\r\n  constructor() {\r\n    this.ws = null;\r\n    this.url = null;\r\n    this.reconnectTimeout = null;\r\n    this.pingInterval = null;\r\n    this.messageQueue = [];\r\n    this.listeners = new Map();\r\n    this.isConnecting = false;\r\n  }\r\n\r\n  /**\r\n   * Connect to WebSocket server\r\n   */\r\n  async connect(url) {\r\n    if (this.isConnecting || (this.ws && this.ws.readyState === WebSocket.OPEN)) {\r\n      console.log('[WebSocket] Already connected or connecting');\r\n      return;\r\n    }\r\n\r\n    this.isConnecting = true;\r\n    this.url = url;\r\n\r\n    return new Promise((resolve, reject) => {\r\n      try {\r\n        console.log(`[WebSocket] Connecting to ${url}`);\r\n        this.ws = new WebSocket(url);\r\n\r\n        this.ws.onopen = () => {\r\n          console.log('[WebSocket] Connected');\r\n          this.isConnecting = false;\r\n          this.emit('connected');\r\n          this.startPingInterval();\r\n          this.flushMessageQueue();\r\n          resolve();\r\n        };\r\n\r\n        this.ws.onmessage = (event) => {\r\n          try {\r\n            const message = JSON.parse(event.data);\r\n            console.log('[WebSocket] Received:', message.type);\r\n            this.emit('message', message);\r\n          } catch (error) {\r\n            console.error('[WebSocket] Error parsing message:', error);\r\n          }\r\n        };\r\n\r\n        this.ws.onerror = (error) => {\r\n          console.error('[WebSocket] Error:', error);\r\n          this.isConnecting = false;\r\n          this.emit('error', error);\r\n          reject(error);\r\n        };\r\n\r\n        this.ws.onclose = () => {\r\n          console.log('[WebSocket] Disconnected');\r\n          this.isConnecting = false;\r\n          this.stopPingInterval();\r\n          this.emit('disconnected');\r\n          this.scheduleReconnect();\r\n        };\r\n\r\n      } catch (error) {\r\n        console.error('[WebSocket] Connection failed:', error);\r\n        this.isConnecting = false;\r\n        reject(error);\r\n      }\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Disconnect from WebSocket server\r\n   */\r\n  async disconnect() {\r\n    if (this.reconnectTimeout) {\r\n      clearTimeout(this.reconnectTimeout);\r\n      this.reconnectTimeout = null;\r\n    }\r\n\r\n    this.stopPingInterval();\r\n\r\n    if (this.ws) {\r\n      this.ws.close();\r\n      this.ws = null;\r\n    }\r\n\r\n    this.emit('disconnected');\r\n  }\r\n\r\n  /**\r\n   * Send message to server\r\n   */\r\n  send(message) {\r\n    const wrappedMessage = {\r\n      id: nanoid(),\r\n      timestamp: Date.now(),\r\n      ...message\r\n    };\r\n\r\n    if (this.ws && this.ws.readyState === WebSocket.OPEN) {\r\n      try {\r\n        this.ws.send(JSON.stringify(wrappedMessage));\r\n        console.log('[WebSocket] Sent:', wrappedMessage.type);\r\n      } catch (error) {\r\n        console.error('[WebSocket] Send error:', error);\r\n        this.messageQueue.push(wrappedMessage);\r\n      }\r\n    } else {\r\n      console.log('[WebSocket] Queuing message (not connected)');\r\n      this.messageQueue.push(wrappedMessage);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Check if connected\r\n   */\r\n  isConnected() {\r\n    return this.ws && this.ws.readyState === WebSocket.OPEN;\r\n  }\r\n\r\n  /**\r\n   * Get current WebSocket URL\r\n   */\r\n  getUrl() {\r\n    return this.url;\r\n  }\r\n\r\n  /**\r\n   * Add event listener\r\n   */\r\n  on(event, callback) {\r\n    if (!this.listeners.has(event)) {\r\n      this.listeners.set(event, []);\r\n    }\r\n    this.listeners.get(event).push(callback);\r\n  }\r\n\r\n  /**\r\n   * Remove event listener\r\n   */\r\n  off(event, callback) {\r\n    if (this.listeners.has(event)) {\r\n      const callbacks = this.listeners.get(event);\r\n      const index = callbacks.indexOf(callback);\r\n      if (index !== -1) {\r\n        callbacks.splice(index, 1);\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Emit event\r\n   */\r\n  emit(event, ...args) {\r\n    if (this.listeners.has(event)) {\r\n      const callbacks = this.listeners.get(event);\r\n      callbacks.forEach(callback => {\r\n        try {\r\n          callback(...args);\r\n        } catch (error) {\r\n          console.error(`[WebSocket] Error in ${event} listener:`, error);\r\n        }\r\n      });\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Start ping interval to keep connection alive\r\n   */\r\n  startPingInterval() {\r\n    this.stopPingInterval();\r\n    \r\n    this.pingInterval = setInterval(() => {\r\n      if (this.isConnected()) {\r\n        this.send({ type: 'ping' });\r\n      }\r\n    }, 30000); // Ping every 30 seconds\r\n  }\r\n\r\n  /**\r\n   * Stop ping interval\r\n   */\r\n  stopPingInterval() {\r\n    if (this.pingInterval) {\r\n      clearInterval(this.pingInterval);\r\n      this.pingInterval = null;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Schedule reconnection attempt\r\n   */\r\n  scheduleReconnect() {\r\n    if (this.reconnectTimeout) {\r\n      clearTimeout(this.reconnectTimeout);\r\n    }\r\n\r\n    this.reconnectTimeout = setTimeout(() => {\r\n      console.log('[WebSocket] Attempting to reconnect...');\r\n      this.connect(this.url).catch(error => {\r\n        console.error('[WebSocket] Reconnection failed:', error);\r\n      });\r\n    }, 5000); // Reconnect after 5 seconds\r\n  }\r\n\r\n  /**\r\n   * Flush queued messages\r\n   */\r\n  flushMessageQueue() {\r\n    while (this.messageQueue.length > 0 && this.isConnected()) {\r\n      const message = this.messageQueue.shift();\r\n      try {\r\n        this.ws.send(JSON.stringify(message));\r\n        console.log('[WebSocket] Sent queued message:', message.type);\r\n      } catch (error) {\r\n        console.error('[WebSocket] Error sending queued message:', error);\r\n        this.messageQueue.unshift(message); // Put it back\r\n        break;\r\n      }\r\n    }\r\n  }\r\n}\r\n", "var e;!function(e){e.UNKNOWN_ERROR=\"unknown_error\",e.NETWORK_ERROR=\"network_error\",e.UNAUTHORIZED=\"unauthorized\",e.SERVICE_UNAVAILABLE=\"service_unavailable\",e.MISSING_API_KEY=\"missing_api_key\",e.MISSING_HOST_PERMISSION=\"missing_host_permission\",e.CONVERSATION_LIMIT=\"conversation_limit\",e.CONTENT_FILTERED=\"content_filtered\",e.INVALID_REQUEST=\"invalid_request\",e.INVALID_API_KEY=\"invalid_api_key\",e.INVALID_THREAD_ID=\"invalid_thread_id\",e.INVALID_METADATA=\"invalid_metadata\",e.INVALID_MESSAGE_ID=\"invalid_message_id\",e.INVALID_MODEL=\"invalid_model\",e.INVALID_IMAGE_TYPE=\"invalid_image_type\",e.INVALID_IMAGE_CONTENT=\"invalid_image_content\",e.UPLOAD_FAILED=\"upload_failed\",e.UPLOAD_TIMEOUT=\"upload_timeout\",e.UPLOAD_SIZE_EXCEEDED=\"upload_size_exceeded\",e.UPLOAD_TYPE_EXCEEDED=\"upload_type_exceeded\",e.UPLOAD_AMOUNT_EXCEEDED=\"upload_amount_exceeded\",e.UPLOAD_TYPE_NOT_SUPPORTED=\"upload_type_not_supported\",e.RATE_LIMIT_EXCEEDED=\"rate_limit_exceeded\",e.METADATA_INITIALIZATION_ERROR=\"metadata_initialization_error\",e.FEATURE_NOT_SUPPORTED=\"feature_not_supported\",e.RESPONSE_PARSING_ERROR=\"response_parsing_error\",e.POW_CHALLENGE_FAILED=\"pow_challenge_failed\"}(e||(e={}));class t extends Error{constructor(t,r=e.UNKNOWN_ERROR){super(t),this.code=r,this.name=\"AIModelError\"}}\"undefined\"!=typeof globalThis?globalThis:\"undefined\"!=typeof window?window:\"undefined\"!=typeof global?global:\"undefined\"!=typeof self&&self;function r(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,\"default\")?e.default:e}var a={exports:{}};\"undefined\"!=typeof globalThis?globalThis:\"undefined\"!=typeof self&&self,function(e){if(!globalThis.chrome?.runtime?.id)throw new Error(\"This script should only be loaded in a browser extension.\");if(void 0===globalThis.browser||Object.getPrototypeOf(globalThis.browser)!==Object.prototype){const t=\"The message port closed before a response was received.\",r=e=>{const r={alarms:{clear:{minArgs:0,maxArgs:1},clearAll:{minArgs:0,maxArgs:0},get:{minArgs:0,maxArgs:1},getAll:{minArgs:0,maxArgs:0}},bookmarks:{create:{minArgs:1,maxArgs:1},get:{minArgs:1,maxArgs:1},getChildren:{minArgs:1,maxArgs:1},getRecent:{minArgs:1,maxArgs:1},getSubTree:{minArgs:1,maxArgs:1},getTree:{minArgs:0,maxArgs:0},move:{minArgs:2,maxArgs:2},remove:{minArgs:1,maxArgs:1},removeTree:{minArgs:1,maxArgs:1},search:{minArgs:1,maxArgs:1},update:{minArgs:2,maxArgs:2}},browserAction:{disable:{minArgs:0,maxArgs:1,fallbackToNoCallback:!0},enable:{minArgs:0,maxArgs:1,fallbackToNoCallback:!0},getBadgeBackgroundColor:{minArgs:1,maxArgs:1},getBadgeText:{minArgs:1,maxArgs:1},getPopup:{minArgs:1,maxArgs:1},getTitle:{minArgs:1,maxArgs:1},openPopup:{minArgs:0,maxArgs:0},setBadgeBackgroundColor:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setBadgeText:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setIcon:{minArgs:1,maxArgs:1},setPopup:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setTitle:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0}},browsingData:{remove:{minArgs:2,maxArgs:2},removeCache:{minArgs:1,maxArgs:1},removeCookies:{minArgs:1,maxArgs:1},removeDownloads:{minArgs:1,maxArgs:1},removeFormData:{minArgs:1,maxArgs:1},removeHistory:{minArgs:1,maxArgs:1},removeLocalStorage:{minArgs:1,maxArgs:1},removePasswords:{minArgs:1,maxArgs:1},removePluginData:{minArgs:1,maxArgs:1},settings:{minArgs:0,maxArgs:0}},commands:{getAll:{minArgs:0,maxArgs:0}},contextMenus:{remove:{minArgs:1,maxArgs:1},removeAll:{minArgs:0,maxArgs:0},update:{minArgs:2,maxArgs:2}},cookies:{get:{minArgs:1,maxArgs:1},getAll:{minArgs:1,maxArgs:1},getAllCookieStores:{minArgs:0,maxArgs:0},remove:{minArgs:1,maxArgs:1},set:{minArgs:1,maxArgs:1}},devtools:{inspectedWindow:{eval:{minArgs:1,maxArgs:2,singleCallbackArg:!1}},panels:{create:{minArgs:3,maxArgs:3,singleCallbackArg:!0},elements:{createSidebarPane:{minArgs:1,maxArgs:1}}}},downloads:{cancel:{minArgs:1,maxArgs:1},download:{minArgs:1,maxArgs:1},erase:{minArgs:1,maxArgs:1},getFileIcon:{minArgs:1,maxArgs:2},open:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},pause:{minArgs:1,maxArgs:1},removeFile:{minArgs:1,maxArgs:1},resume:{minArgs:1,maxArgs:1},search:{minArgs:1,maxArgs:1},show:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0}},extension:{isAllowedFileSchemeAccess:{minArgs:0,maxArgs:0},isAllowedIncognitoAccess:{minArgs:0,maxArgs:0}},history:{addUrl:{minArgs:1,maxArgs:1},deleteAll:{minArgs:0,maxArgs:0},deleteRange:{minArgs:1,maxArgs:1},deleteUrl:{minArgs:1,maxArgs:1},getVisits:{minArgs:1,maxArgs:1},search:{minArgs:1,maxArgs:1}},i18n:{detectLanguage:{minArgs:1,maxArgs:1},getAcceptLanguages:{minArgs:0,maxArgs:0}},identity:{launchWebAuthFlow:{minArgs:1,maxArgs:1}},idle:{queryState:{minArgs:1,maxArgs:1}},management:{get:{minArgs:1,maxArgs:1},getAll:{minArgs:0,maxArgs:0},getSelf:{minArgs:0,maxArgs:0},setEnabled:{minArgs:2,maxArgs:2},uninstallSelf:{minArgs:0,maxArgs:1}},notifications:{clear:{minArgs:1,maxArgs:1},create:{minArgs:1,maxArgs:2},getAll:{minArgs:0,maxArgs:0},getPermissionLevel:{minArgs:0,maxArgs:0},update:{minArgs:2,maxArgs:2}},pageAction:{getPopup:{minArgs:1,maxArgs:1},getTitle:{minArgs:1,maxArgs:1},hide:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setIcon:{minArgs:1,maxArgs:1},setPopup:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setTitle:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},show:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0}},permissions:{contains:{minArgs:1,maxArgs:1},getAll:{minArgs:0,maxArgs:0},remove:{minArgs:1,maxArgs:1},request:{minArgs:1,maxArgs:1}},runtime:{getBackgroundPage:{minArgs:0,maxArgs:0},getPlatformInfo:{minArgs:0,maxArgs:0},openOptionsPage:{minArgs:0,maxArgs:0},requestUpdateCheck:{minArgs:0,maxArgs:0},sendMessage:{minArgs:1,maxArgs:3},sendNativeMessage:{minArgs:2,maxArgs:2},setUninstallURL:{minArgs:1,maxArgs:1}},sessions:{getDevices:{minArgs:0,maxArgs:1},getRecentlyClosed:{minArgs:0,maxArgs:1},restore:{minArgs:0,maxArgs:1}},storage:{local:{clear:{minArgs:0,maxArgs:0},get:{minArgs:0,maxArgs:1},getBytesInUse:{minArgs:0,maxArgs:1},remove:{minArgs:1,maxArgs:1},set:{minArgs:1,maxArgs:1}},managed:{get:{minArgs:0,maxArgs:1},getBytesInUse:{minArgs:0,maxArgs:1}},sync:{clear:{minArgs:0,maxArgs:0},get:{minArgs:0,maxArgs:1},getBytesInUse:{minArgs:0,maxArgs:1},remove:{minArgs:1,maxArgs:1},set:{minArgs:1,maxArgs:1}}},tabs:{captureVisibleTab:{minArgs:0,maxArgs:2},create:{minArgs:1,maxArgs:1},detectLanguage:{minArgs:0,maxArgs:1},discard:{minArgs:0,maxArgs:1},duplicate:{minArgs:1,maxArgs:1},executeScript:{minArgs:1,maxArgs:2},get:{minArgs:1,maxArgs:1},getCurrent:{minArgs:0,maxArgs:0},getZoom:{minArgs:0,maxArgs:1},getZoomSettings:{minArgs:0,maxArgs:1},goBack:{minArgs:0,maxArgs:1},goForward:{minArgs:0,maxArgs:1},highlight:{minArgs:1,maxArgs:1},insertCSS:{minArgs:1,maxArgs:2},move:{minArgs:2,maxArgs:2},query:{minArgs:1,maxArgs:1},reload:{minArgs:0,maxArgs:2},remove:{minArgs:1,maxArgs:1},removeCSS:{minArgs:1,maxArgs:2},sendMessage:{minArgs:2,maxArgs:3},setZoom:{minArgs:1,maxArgs:2},setZoomSettings:{minArgs:1,maxArgs:2},update:{minArgs:1,maxArgs:2}},topSites:{get:{minArgs:0,maxArgs:0}},webNavigation:{getAllFrames:{minArgs:1,maxArgs:1},getFrame:{minArgs:1,maxArgs:1}},webRequest:{handlerBehaviorChanged:{minArgs:0,maxArgs:0}},windows:{create:{minArgs:0,maxArgs:1},get:{minArgs:1,maxArgs:2},getAll:{minArgs:0,maxArgs:1},getCurrent:{minArgs:0,maxArgs:1},getLastFocused:{minArgs:0,maxArgs:1},remove:{minArgs:1,maxArgs:1},update:{minArgs:2,maxArgs:2}}};if(0===Object.keys(r).length)throw new Error(\"api-metadata.json has not been included in browser-polyfill\");class a extends WeakMap{constructor(e,t=void 0){super(t),this.createItem=e}get(e){return this.has(e)||this.set(e,this.createItem(e)),super.get(e)}}const s=e=>e&&\"object\"==typeof e&&\"function\"==typeof e.then,n=(t,r)=>(...a)=>{e.runtime.lastError?t.reject(new Error(e.runtime.lastError.message)):r.singleCallbackArg||a.length<=1&&!1!==r.singleCallbackArg?t.resolve(a[0]):t.resolve(a)},o=e=>1==e?\"argument\":\"arguments\",i=(e,t)=>function(r,...a){if(a.length<t.minArgs)throw new Error(`Expected at least ${t.minArgs} ${o(t.minArgs)} for ${e}(), got ${a.length}`);if(a.length>t.maxArgs)throw new Error(`Expected at most ${t.maxArgs} ${o(t.maxArgs)} for ${e}(), got ${a.length}`);return new Promise(((s,o)=>{if(t.fallbackToNoCallback)try{r[e](...a,n({resolve:s,reject:o},t))}catch(n){console.warn(`${e} API method doesn't seem to support the callback parameter, falling back to call it without a callback: `,n),r[e](...a),t.fallbackToNoCallback=!1,t.noCallback=!0,s()}else t.noCallback?(r[e](...a),s()):r[e](...a,n({resolve:s,reject:o},t))}))},d=(e,t,r)=>new Proxy(t,{apply:(t,a,s)=>r.call(a,e,...s)});let l=Function.call.bind(Object.prototype.hasOwnProperty);const c=(e,t={},r={})=>{let a=Object.create(null),s={has:(t,r)=>r in e||r in a,get(s,n,o){if(n in a)return a[n];if(!(n in e))return;let h=e[n];if(\"function\"==typeof h)if(\"function\"==typeof t[n])h=d(e,e[n],t[n]);else if(l(r,n)){let t=i(n,r[n]);h=d(e,e[n],t)}else h=h.bind(e);else if(\"object\"==typeof h&&null!==h&&(l(t,n)||l(r,n)))h=c(h,t[n],r[n]);else{if(!l(r,\"*\"))return Object.defineProperty(a,n,{configurable:!0,enumerable:!0,get:()=>e[n],set(t){e[n]=t}}),h;h=c(h,t[n],r[\"*\"])}return a[n]=h,h},set:(t,r,s,n)=>(r in a?a[r]=s:e[r]=s,!0),defineProperty:(e,t,r)=>Reflect.defineProperty(a,t,r),deleteProperty:(e,t)=>Reflect.deleteProperty(a,t)},n=Object.create(e);return new Proxy(n,s)},h=e=>({addListener(t,r,...a){t.addListener(e.get(r),...a)},hasListener:(t,r)=>t.hasListener(e.get(r)),removeListener(t,r){t.removeListener(e.get(r))}}),u=new a((e=>\"function\"!=typeof e?e:function(t){const r=c(t,{},{getContent:{minArgs:0,maxArgs:0}});e(r)})),g=new a((e=>\"function\"!=typeof e?e:function(t,r,a){let n,o,i=!1,d=new Promise((e=>{n=function(t){i=!0,e(t)}}));try{o=e(t,r,n)}catch(e){o=Promise.reject(e)}const l=!0!==o&&s(o);if(!0!==o&&!l&&!i)return!1;const c=e=>{e.then((e=>{a(e)}),(e=>{let t;t=e&&(e instanceof Error||\"string\"==typeof e.message)?e.message:\"An unexpected error occurred\",a({__mozWebExtensionPolyfillReject__:!0,message:t})})).catch((e=>{console.error(\"Failed to send onMessage rejected reply\",e)}))};return c(l?o:d),!0})),m=({reject:r,resolve:a},s)=>{e.runtime.lastError?e.runtime.lastError.message===t?a():r(new Error(e.runtime.lastError.message)):s&&s.__mozWebExtensionPolyfillReject__?r(new Error(s.message)):a(s)},p=(e,t,r,...a)=>{if(a.length<t.minArgs)throw new Error(`Expected at least ${t.minArgs} ${o(t.minArgs)} for ${e}(), got ${a.length}`);if(a.length>t.maxArgs)throw new Error(`Expected at most ${t.maxArgs} ${o(t.maxArgs)} for ${e}(), got ${a.length}`);return new Promise(((e,t)=>{const s=m.bind(null,{resolve:e,reject:t});a.push(s),r.sendMessage(...a)}))},f={devtools:{network:{onRequestFinished:h(u)}},runtime:{onMessage:h(g),onMessageExternal:h(g),sendMessage:p.bind(null,\"sendMessage\",{minArgs:1,maxArgs:3})},tabs:{sendMessage:p.bind(null,\"sendMessage\",{minArgs:2,maxArgs:3})}},E={clear:{minArgs:1,maxArgs:1},get:{minArgs:1,maxArgs:1},set:{minArgs:1,maxArgs:1}};return r.privacy={network:{\"*\":E},services:{\"*\":E},websites:{\"*\":E}},c(e,f,r)};e.exports=r(chrome)}else e.exports=globalThis.browser}(a);var s=r(a.exports);let n;const o=new Uint8Array(16);function i(){if(!n&&(n=\"undefined\"!=typeof crypto&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto),!n))throw new Error(\"crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported\");return n(o)}const d=[];for(let e=0;e<256;++e)d.push((e+256).toString(16).slice(1));var l={randomUUID:\"undefined\"!=typeof crypto&&crypto.randomUUID&&crypto.randomUUID.bind(crypto)};function c(e,t,r){if(l.randomUUID&&!e)return l.randomUUID();const a=(e=e||{}).random||(e.rng||i)();return a[6]=15&a[6]|64,a[8]=63&a[8]|128,function(e,t=0){return d[e[t+0]]+d[e[t+1]]+d[e[t+2]]+d[e[t+3]]+\"-\"+d[e[t+4]]+d[e[t+5]]+\"-\"+d[e[t+6]]+d[e[t+7]]+\"-\"+d[e[t+8]]+d[e[t+9]]+\"-\"+d[e[t+10]]+d[e[t+11]]+d[e[t+12]]+d[e[t+13]]+d[e[t+14]]+d[e[t+15]]}(a)}class h{constructor(){this.baseUrl=\"\",this.defaultModel=\"\"}async sendMessage(r,a={onEvent:()=>{}}){try{let e=\"\";return await this.doSendMessage({prompt:r,images:a.images,signal:a.signal,mode:a.mode,model:a.model,style_key:a.style_key,searchFocus:a.searchFocus,searchSources:a.searchSources,searchEnabled:a.searchEnabled,onEvent:t=>{\"UPDATE_ANSWER\"===t.type&&(e=t.data.text,a.onEvent({type:\"UPDATE_ANSWER\",data:t.data})),\"DONE\"===t.type&&a.onEvent({type:\"DONE\",data:t.data}),\"SUGGESTED_RESPONSES\"===t.type&&a.onEvent({type:\"SUGGESTED_RESPONSES\",data:t.data}),\"TITLE_UPDATE\"===t.type&&a.onEvent({type:\"TITLE_UPDATE\",data:t.data}),\"ERROR\"===t.type&&a.onEvent({type:\"ERROR\",error:t.error})}}),e}catch(r){a.onEvent({type:\"ERROR\",error:r instanceof t?r:new t(r instanceof Error?r.message:String(r),r instanceof t?r.code:e.UNKNOWN_ERROR)}),this.handleModelError(\"Error sending message\",r instanceof t?r.code:void 0,a,r)}}async getAllThreads(){try{return(await s.storage.local.get(h.THREADS_STORAGE_KEY))[h.THREADS_STORAGE_KEY]||[]}catch(e){return console.error(\"Failed to load threads from storage:\",e),[]}}async saveThreadsToStorage(e){try{await s.storage.local.set({[h.THREADS_STORAGE_KEY]:e})}catch(e){console.error(\"Failed to save threads to storage:\",e)}}getCurrentThread(){return this.currentThread}async loadThread(t){const r=(await this.getAllThreads()).find((e=>e.id===t));if(!r)return this.handleModelError(\"Thread not found\",e.INVALID_THREAD_ID);this.currentThread=r}async saveThread(){if(!this.currentThread)return this.handleModelError(\"No active thread to save\",e.INVALID_REQUEST);const t=await this.getAllThreads(),r=t.findIndex((e=>e.id===this.currentThread.id));-1!==r?t[r]=this.currentThread:t.push(this.currentThread),await this.saveThreadsToStorage(t)}async deleteThread(e,t=!0){const r=await this.getAllThreads();await this.saveThreadsToStorage(r.filter((t=>t.id!==e))),this.currentThread?.id===e&&t&&this.initNewThread()}createMessage(e,t){return{id:c(),role:e,content:t,timestamp:Date.now()}}getBaseUrl(){return this.baseUrl}handleModelError(r,a,s,n){!a&&n instanceof t?a=n.code:a||(n instanceof Error&&(\"AbortError\"===n.name?a=e.RATE_LIMIT_EXCEEDED:n.message.includes(\"network\")||n.message.includes(\"connection\")?a=e.NETWORK_ERROR:n.message.includes(\"permission\")||n.message.includes(\"unauthorized\")?a=e.UNAUTHORIZED:n.message.includes(\"timeout\")&&(a=e.SERVICE_UNAVAILABLE)),a=a||e.UNKNOWN_ERROR);const o=n?n instanceof Error?n.message:String(n):\"\",i=new t(o?`${o} - ${r}`:r,a);throw n&&\"cause\"in Error&&Object.assign(i,{cause:n}),s?.onEvent&&s.onEvent({type:\"ERROR\",error:i}),console.error(\"AI model error:\",i),i}async shareConversation(t){return this.handleModelError(`Sharing is not supported by the ${this.getName()} model`,e.FEATURE_NOT_SUPPORTED)}}async function*u(e){const t=e.getReader();try{for(;;){const{done:e,value:r}=await t.read();if(e)return;yield r}}finally{t.releaseLock()}}async function g(e,t){const r=e.body.getReader(),a=new TextDecoder;let s=\"\";for(;;){const{done:e,value:n}=await r.read();if(e)break;s+=a.decode(n,{stream:!0});const o=s.split(\"\\n\");s=o.pop()||\"\";for(const e of o){const r=e.trim();if(r&&r.startsWith(\"data: \")){t(r.slice(6))}}}if(s.trim()&&s.startsWith(\"data: \")){t(s.slice(6))}}function m(e,t=!1){return new Promise(((r,a)=>{const s=new FileReader;s.readAsDataURL(e),s.onload=()=>{const e=s.result;r(t?e:e.split(\",\")[1])},s.onerror=a}))}h.THREADS_STORAGE_KEY=\"chat_threads\";const p={TOKEN_REFRESH_START:\"token_refresh_start\",TOKEN_REFRESH_COMPLETE:\"token_refresh_complete\",TOKEN_REFRESH_ERROR:\"token_refresh_error\"},f=\"auth_token_cache_\";async function E(e){const t={origins:[e]};try{return!!await s.permissions.contains(t)||(console.log(`Requesting host permission for: ${e}`),await s.permissions.request(t))}catch(e){return console.error(\"Error requesting permissions:\",e),!1}}function A(e){void 0!==e.notifyTokenRefresh&&e.notifyTokenRefresh}async function y(e){try{const t=await s.cookies.getAll({domain:e}),r={};for(const e of t)r[e.name]=e.value;return r}catch(t){return console.error(`Error getting cookies for ${e}:`,t),{}}}async function T(e){const t=e?`${f}${e}`:null;try{if(t)await s.storage.local.remove(t),console.log(`[${e} Auth] Cache cleared.`);else{const e=await s.storage.local.get(null),t=Object.keys(e).filter((e=>e.startsWith(f)));t.length>0&&(await s.storage.local.remove(t),console.log(\"[Auth] All token caches cleared.\"))}}catch(t){console.warn(`[Auth] Error clearing cache for ${e||\"all\"}:`,t)}}function _(){try{const e=localStorage.getItem(\"userToken\");if(e){const t=JSON.parse(e);if(t?.value&&\"string\"==typeof t.value&&t.value.length>0)return t.value;console.warn(\"Deepseek 'userToken' found but userToken structure not matched:\",t)}else console.warn(\"Deepseek 'userToken' key not found in localStorage.\")}catch(e){console.error(\"Error executing injected DeepSeek token extractor:\",e)}return null}function w(){try{for(let e=0;e<localStorage.length;e++)try{const t=localStorage.key(e);if(!t)continue;const r=JSON.parse(localStorage.getItem(t)||\"\");if(r&&\"AccessToken\"===r.credentialType&&r.expiresOn>Math.floor(Date.now()/1e3)&&r.target?.includes(\"ChatAI\"))return r.secret}catch(e){}}catch(e){console.error(\"Error executing injected Copilot token extractor:\",e)}return null}async function v(e,t,r,a,n){let o=null;if(console.log(`[${e} Auth Internal] Attempting to retrieve token...`),void 0===s||!s.tabs||!s.scripting||!s.permissions)return console.error(`[${e} Auth Internal] Browser extension APIs (tabs, scripting, permissions) not available.`),null;let i=!1;try{i=await s.permissions.contains({origins:[r]})}catch(t){return console.error(`[${e} Auth Internal] Error checking permissions for ${r}:`,t),null}if(!i)return console.warn(`[${e} Auth Internal] Missing host permissions for ${r}. Cannot inject scripts or open tabs.`),null;if(n)console.log(`[${e} Auth Internal] Skipping existing tab check because forceNewTab is true.`);else try{const t=await s.tabs.query({url:r});console.log(`[${e} Auth Internal] Found ${t.length} potential tabs matching ${r}.`);for(const r of t)if(!r.id||r.url?.startsWith(\"chrome://\")||r.url?.startsWith(\"about:\"))console.log(`[${e} Auth Internal] Skipping tab ID: ${r.id} (missing ID or restricted URL: ${r.url})`);else{console.log(`[${e} Auth Internal] Attempting to inject script into tab ID: ${r.id} (URL: ${r.url})`);try{const t=await s.scripting.executeScript({target:{tabId:r.id},func:a,world:\"MAIN\"});if(t&&t[0]&&t[0].result){o=t[0].result,console.log(`[${e} Auth Internal] Successfully extracted token from existing tab: ${r.id}`);break}console.log(`[${e} Auth Internal] Script executed on tab ${r.id}, but no token found in result.`,t)}catch(t){t instanceof Error&&t.message.includes(\"No window matching\")?console.warn(`[${e} Auth Internal] Could not inject into tab ${r.id} (window likely closed).`):t instanceof Error&&t.message.includes(\"Could not establish connection\")?console.warn(`[${e} Auth Internal] Connection error injecting into tab ${r.id} (possibly devtools?).`):t instanceof Error&&t.message.includes(\"Cannot access contents of the page\")?console.warn(`[${e} Auth Internal] Cannot access contents of tab ${r.id} (URL: ${r.url}). Might be restricted page.`):console.warn(`[${e} Auth Internal] Failed to inject script or extract token from tab ${r.id}:`,t)}}}catch(t){console.error(`[${e} Auth Internal] Error querying for tabs matching ${r}:`,t)}if(!o){if(console.log(`[${e} Auth Internal] No token from existing tabs. Proceeding with temporary window method...`),!s.windows)return console.error(`[${e} Auth Internal] Browser.windows API is not available. Cannot open temporary window.`),null;let r=null;try{console.log(`[${e} Auth Internal] Creating temporary window for ${t}...`),r=await s.windows.create({url:t,focused:!1,type:\"popup\",width:150,height:150}),console.log(`[${e} Auth Internal] Temporary window created (ID: ${r?.id})`);const n=r?.tabs?.[0]?.id;if(!n)throw new Error(\"Failed to get tab ID from temporary window.\");console.log(`[${e} Auth Internal] Temporary tab ID: ${n}`),console.log(`[${e} Auth Internal] Waiting for temporary tab to load...`),await new Promise((e=>setTimeout(e,4e3))),console.log(`[${e} Auth Internal] Attempting REAL extractor injection into temp tab ID: ${n}`);try{const t=await s.scripting.executeScript({target:{tabId:n},func:a,world:\"MAIN\"});console.log(`[${e} Auth Internal] REAL extractor script executed. Results:`,t),t&&t[0]&&t[0].result?(o=t[0].result,console.log(`[${e} Auth Internal] Successfully extracted REAL token via temporary tab.`)):console.warn(`[${e} Auth Internal] Failed to extract REAL token via temporary tab.`,t)}catch(t){console.error(`[${e} Auth Internal] Error injecting REAL extractor script:`,t)}}catch(t){console.error(`[${e} Auth Internal] Error creating/accessing temporary tab:`,t)}finally{if(console.log(`[${e} Auth Internal] Entering finally block for temporary window.`),r?.id){console.log(`[${e} Auth Internal] Attempting to close temporary window ID: ${r.id}`);try{await s.windows.remove(r.id),console.log(`[${e} Auth Internal] Successfully closed temporary auth window.`)}catch(t){t instanceof Error&&t.message.includes(\"No window with id\")?console.log(`[${e} Auth Internal] Temporary window already closed.`):console.warn(`[${e} Auth Internal] Error closing temporary auth window:`,t)}}else console.log(`[${e} Auth Internal] No temporary window ID found to close in finally block.`)}}return o?console.log(`[${e} Auth Internal] Token retrieval successful.`):console.warn(`[${e} Auth Internal] Failed to retrieve token after all attempts.`),o}async function S(e,t,r,a,n=!1){console.log(`[${e} Auth Wrapper] Requesting token from background (forceRefresh: ${n})...`);try{const o=await s.runtime.sendMessage({type:\"GET_AUTH_TOKEN_FROM_WEBSITE\",payload:{serviceName:e,targetUrl:t,urlPattern:r,extractorName:a,forceNewTab:n}});if(console.log(`[${e} Auth Wrapper] Received response from background:`,o),o?.success)return o.token||null;{const t=o?.error||`Unknown error from background script for ${e}`;throw console.error(`[${e} Auth Wrapper] Background script failed: ${t}`),new Error(t)}}catch(t){if(console.error(`[${e} Auth Wrapper] Error communicating with background script:`,t),t instanceof Error&&(t.message.includes(\"Could not establish connection\")||t.message.includes(\"Receiving end does not exist\"))){const e=\"Background service communication error. Is the extension enabled/reloaded? Check background script logs.\";throw console.error(e),new Error(e)}throw t}}function I(e,t,r,a){var s,n=arguments.length,o=n<3?t:null===a?a=Object.getOwnPropertyDescriptor(t,r):a;if(\"object\"==typeof Reflect&&\"function\"==typeof Reflect.decorate)o=Reflect.decorate(e,t,r,a);else for(var i=e.length-1;i>=0;i--)(s=e[i])&&(o=(n<3?s(o):n>3?s(t,r,o):s(t,r))||o);return n>3&&o&&Object.defineProperty(t,r,o),o}function b(e,t){if(\"object\"==typeof Reflect&&\"function\"==typeof Reflect.metadata)return Reflect.metadata(e,t)}\"function\"==typeof SuppressedError&&SuppressedError;const R=/\"(?:_|\\\\u0{2}5[Ff]){2}(?:p|\\\\u0{2}70)(?:r|\\\\u0{2}72)(?:o|\\\\u0{2}6[Ff])(?:t|\\\\u0{2}74)(?:o|\\\\u0{2}6[Ff])(?:_|\\\\u0{2}5[Ff]){2}\"\\s*:/,N=/\"(?:c|\\\\u0063)(?:o|\\\\u006[Ff])(?:n|\\\\u006[Ee])(?:s|\\\\u0073)(?:t|\\\\u0074)(?:r|\\\\u0072)(?:u|\\\\u0075)(?:c|\\\\u0063)(?:t|\\\\u0074)(?:o|\\\\u006[Ff])(?:r|\\\\u0072)\"\\s*:/,x=/^\\s*[\"[{]|^\\s*-?\\d{1,16}(\\.\\d{1,17})?([Ee][+-]?\\d+)?\\s*$/;function M(e,t){if(!(\"__proto__\"===e||\"constructor\"===e&&t&&\"object\"==typeof t&&\"prototype\"in t))return t;!function(e){console.warn(`[destr] Dropping \"${e}\" key to prevent prototype pollution.`)}(e)}function U(e,t={}){if(\"string\"!=typeof e)return e;const r=e.trim();if('\"'===e[0]&&e.endsWith('\"')&&!e.includes(\"\\\\\"))return r.slice(1,-1);if(r.length<=9){const e=r.toLowerCase();if(\"true\"===e)return!0;if(\"false\"===e)return!1;if(\"undefined\"===e)return;if(\"null\"===e)return null;if(\"nan\"===e)return Number.NaN;if(\"infinity\"===e)return Number.POSITIVE_INFINITY;if(\"-infinity\"===e)return Number.NEGATIVE_INFINITY}if(!x.test(e)){if(t.strict)throw new SyntaxError(\"[destr] Invalid JSON\");return e}try{if(R.test(e)||N.test(e)){if(t.strict)throw new Error(\"[destr] Possible prototype pollution\");return JSON.parse(e,M)}return JSON.parse(e)}catch(r){if(t.strict)throw r;return e}}const D=/#/g,k=/&/g,P=/\\//g,O=/=/g,L=/\\+/g,C=/%5e/gi,$=/%60/gi,V=/%7c/gi,F=/%20/gi;function W(e){return(t=\"string\"==typeof e?e:JSON.stringify(e),encodeURI(\"\"+t).replace(V,\"|\")).replace(L,\"%2B\").replace(F,\"+\").replace(D,\"%23\").replace(k,\"%26\").replace($,\"`\").replace(C,\"^\").replace(P,\"%2F\");var t}function z(e){return W(e).replace(O,\"%3D\")}function B(e=\"\"){try{return decodeURIComponent(\"\"+e)}catch{return\"\"+e}}function q(e){return B(e.replace(L,\" \"))}function j(e=\"\"){const t={};\"?\"===e[0]&&(e=e.slice(1));for(const r of e.split(\"&\")){const e=r.match(/([^=]+)=?(.*)/)||[];if(e.length<2)continue;const a=B(e[1].replace(L,\" \"));if(\"__proto__\"===a||\"constructor\"===a)continue;const s=q(e[2]||\"\");void 0===t[a]?t[a]=s:Array.isArray(t[a])?t[a].push(s):t[a]=[t[a],s]}return t}function H(e){return Object.keys(e).filter((t=>void 0!==e[t])).map((t=>{return r=t,\"number\"!=typeof(a=e[t])&&\"boolean\"!=typeof a||(a=String(a)),a?Array.isArray(a)?a.map((e=>`${z(r)}=${W(e)}`)).join(\"&\"):`${z(r)}=${W(a)}`:z(r);var r,a})).filter(Boolean).join(\"&\")}const G=/^[\\s\\w\\0+.-]{2,}:([/\\\\]{1,2})/,J=/^[\\s\\w\\0+.-]{2,}:([/\\\\]{2})?/,K=/^([/\\\\]\\s*){2,}[^/\\\\]/,Q=/^\\.?\\//;function Z(e,t={}){return\"boolean\"==typeof t&&(t={acceptRelative:t}),t.strict?G.test(e):J.test(e)||!!t.acceptRelative&&K.test(e)}function X(e=\"\",t){return e.endsWith(\"/\")?e:e+\"/\"}function Y(e,t){if(!(r=t)||\"/\"===r||Z(e))return e;var r;const a=function(e=\"\"){return(function(e=\"\"){return e.endsWith(\"/\")}(e)?e.slice(0,-1):e)||\"/\"}(t);return e.startsWith(a)?e:function(e,...t){let r=e||\"\";for(const e of t.filter((e=>function(e){return e&&\"/\"!==e}(e))))if(r){const t=e.replace(Q,\"\");r=X(r)+t}else r=e;return r}(a,e)}function ee(e,t){const r=function(e=\"\"){const t=e.match(/^[\\s\\0]*(blob:|data:|javascript:|vbscript:)(.*)/i);if(t){const[,e,r=\"\"]=t;return{protocol:e.toLowerCase(),pathname:r,href:e+r,auth:\"\",host:\"\",search:\"\",hash:\"\"}}if(!Z(e,{acceptRelative:!0}))return re(e);const[,r=\"\",a,s=\"\"]=e.replace(/\\\\/g,\"/\").match(/^[\\s\\0]*([\\w+.-]{2,}:)?\\/\\/([^/@]+@)?(.*)/)||[];let[,n=\"\",o=\"\"]=s.match(/([^#/?]*)(.*)?/)||[];\"file:\"===r&&(o=o.replace(/\\/(?=[A-Za-z]:)/,\"\"));const{pathname:i,search:d,hash:l}=re(o);return{protocol:r.toLowerCase(),auth:a?a.slice(0,Math.max(0,a.length-1)):\"\",host:n,pathname:i,search:d,hash:l,[te]:!r}}(e),a={...j(r.search),...t};return r.search=H(a),function(e){const t=e.pathname||\"\",r=e.search?(e.search.startsWith(\"?\")?\"\":\"?\")+e.search:\"\",a=e.hash||\"\",s=e.auth?e.auth+\"@\":\"\",n=e.host||\"\",o=e.protocol||e[te]?(e.protocol||\"\")+\"//\":\"\";return o+s+n+t+r+a}(r)}const te=Symbol.for(\"ufo:protocolRelative\");function re(e=\"\"){const[t=\"\",r=\"\",a=\"\"]=(e.match(/([^#?]*)(\\?[^#]*)?(#.*)?/)||[]).splice(1);return{pathname:t,search:r,hash:a}}class ae extends Error{constructor(e,t){super(e,t),this.name=\"FetchError\",t?.cause&&!this.cause&&(this.cause=t.cause)}}const se=new Set(Object.freeze([\"PATCH\",\"POST\",\"PUT\",\"DELETE\"]));function ne(e=\"GET\"){return se.has(e.toUpperCase())}const oe=new Set([\"image/svg\",\"application/xml\",\"application/xhtml\",\"application/html\"]),ie=/^application\\/(?:[\\w!#$%&*.^`~-]*\\+)?json(;.+)?$/i;function de(e,t,r,a){const s=function(e,t,r){if(!t)return new r(e);const a=new r(t);if(e)for(const[t,s]of Symbol.iterator in e||Array.isArray(e)?e:new r(e))a.set(t,s);return a}(t?.headers??e?.headers,r?.headers,a);let n;return(r?.query||r?.params||t?.params||t?.query)&&(n={...r?.params,...r?.query,...t?.params,...t?.query}),{...r,...t,query:n,params:n,headers:s}}async function le(e,t){if(t)if(Array.isArray(t))for(const r of t)await r(e);else await t(e)}const ce=new Set([408,409,425,429,500,502,503,504]),he=new Set([101,204,205,304]);const ue=function(){if(\"undefined\"!=typeof globalThis)return globalThis;if(\"undefined\"!=typeof self)return self;if(\"undefined\"!=typeof window)return window;if(\"undefined\"!=typeof global)return global;throw new Error(\"unable to locate global object\")}(),ge=function e(t={}){const{fetch:r=globalThis.fetch,Headers:a=globalThis.Headers,AbortController:s=globalThis.AbortController}=t;async function n(e){const t=e.error&&\"AbortError\"===e.error.name&&!e.options.timeout||!1;if(!1!==e.options.retry&&!t){let t;t=\"number\"==typeof e.options.retry?e.options.retry:ne(e.options.method)?0:1;const r=e.response&&e.response.status||500;if(t>0&&(Array.isArray(e.options.retryStatusCodes)?e.options.retryStatusCodes.includes(r):ce.has(r))){const r=\"function\"==typeof e.options.retryDelay?e.options.retryDelay(e):e.options.retryDelay||0;return r>0&&await new Promise((e=>setTimeout(e,r))),o(e.request,{...e.options,retry:t-1})}}const r=function(e){const t=e.error?.message||e.error?.toString()||\"\",r=e.request?.method||e.options?.method||\"GET\",a=e.request?.url||String(e.request)||\"/\",s=`[${r}] ${JSON.stringify(a)}`,n=e.response?`${e.response.status} ${e.response.statusText}`:\"<no response>\",o=new ae(`${s}: ${n}${t?` ${t}`:\"\"}`,e.error?{cause:e.error}:void 0);for(const t of[\"request\",\"options\",\"response\"])Object.defineProperty(o,t,{get:()=>e[t]});for(const[t,r]of[[\"data\",\"_data\"],[\"status\",\"status\"],[\"statusCode\",\"status\"],[\"statusText\",\"statusText\"],[\"statusMessage\",\"statusText\"]])Object.defineProperty(o,t,{get:()=>e.response&&e.response[r]});return o}(e);throw Error.captureStackTrace&&Error.captureStackTrace(r,o),r}const o=async function(e,o={}){const i={request:e,options:de(e,o,t.defaults,a),response:void 0,error:void 0};let d;if(i.options.method&&(i.options.method=i.options.method.toUpperCase()),i.options.onRequest&&await le(i,i.options.onRequest),\"string\"==typeof i.request&&(i.options.baseURL&&(i.request=Y(i.request,i.options.baseURL)),i.options.query&&(i.request=ee(i.request,i.options.query),delete i.options.query),\"query\"in i.options&&delete i.options.query,\"params\"in i.options&&delete i.options.params),i.options.body&&ne(i.options.method)&&(!function(e){if(void 0===e)return!1;const t=typeof e;return\"string\"===t||\"number\"===t||\"boolean\"===t||null===t||\"object\"===t&&(!!Array.isArray(e)||!e.buffer&&(e.constructor&&\"Object\"===e.constructor.name||\"function\"==typeof e.toJSON))}(i.options.body)?(\"pipeTo\"in i.options.body&&\"function\"==typeof i.options.body.pipeTo||\"function\"==typeof i.options.body.pipe)&&(\"duplex\"in i.options||(i.options.duplex=\"half\")):(i.options.body=\"string\"==typeof i.options.body?i.options.body:JSON.stringify(i.options.body),i.options.headers=new a(i.options.headers||{}),i.options.headers.has(\"content-type\")||i.options.headers.set(\"content-type\",\"application/json\"),i.options.headers.has(\"accept\")||i.options.headers.set(\"accept\",\"application/json\"))),!i.options.signal&&i.options.timeout){const e=new s;d=setTimeout((()=>{const t=new Error(\"[TimeoutError]: The operation was aborted due to timeout\");t.name=\"TimeoutError\",t.code=23,e.abort(t)}),i.options.timeout),i.options.signal=e.signal}try{i.response=await r(i.request,i.options)}catch(e){return i.error=e,i.options.onRequestError&&await le(i,i.options.onRequestError),await n(i)}finally{d&&clearTimeout(d)}if((i.response.body||i.response._bodyInit)&&!he.has(i.response.status)&&\"HEAD\"!==i.options.method){const e=(i.options.parseResponse?\"json\":i.options.responseType)||function(e=\"\"){if(!e)return\"json\";const t=e.split(\";\").shift()||\"\";return ie.test(t)?\"json\":oe.has(t)||t.startsWith(\"text/\")?\"text\":\"blob\"}(i.response.headers.get(\"content-type\")||\"\");switch(e){case\"json\":{const e=await i.response.text(),t=i.options.parseResponse||U;i.response._data=t(e);break}case\"stream\":i.response._data=i.response.body||i.response._bodyInit;break;default:i.response._data=await i.response[e]()}}return i.options.onResponse&&await le(i,i.options.onResponse),!i.options.ignoreResponseError&&i.response.status>=400&&i.response.status<600?(i.options.onResponseError&&await le(i,i.options.onResponseError),await n(i)):i.response},i=async function(e,t){return(await o(e,t))._data};return i.raw=o,i.native=(...e)=>r(...e),i.create=(r={},a={})=>e({...t,...a,defaults:{...t.defaults,...a.defaults,...r}}),i}({fetch:ue.fetch?(...e)=>ue.fetch(...e):()=>Promise.reject(new Error(\"[ofetch] global.fetch is not supported!\")),Headers:ue.Headers,AbortController:ue.AbortController});function me(e,t,r){return r}function pe(){return Math.floor(9e5*Math.random())+1e5}function fe(e,t){const r=new RegExp(`\"${e}\":\"([^\"]+)\"`).exec(t);return r?.[1]}class Ee extends h{constructor(){super(),this.models={\"gemini-2.0-flash\":{\"x-goog-ext-525001261-jspb\":'[null,null,null,null,\"f299729663a2343f\"]'},\"gemini-2.0-flash-exp\":{\"x-goog-ext-525001261-jspb\":'[null,null,null,null,\"f299729663a2343f\"]'},\"gemini-2.0-flash-thinking\":{\"x-goog-ext-525001261-jspb\":'[null,null,null,null,\"9c17b1863f581b8a\"]'},\"gemini-2.0-flash-thinking-with-apps\":{\"x-goog-ext-525001261-jspb\":'[null,null,null,null,\"f8f8f5ea629f5d37\"]'},\"gemini-2.0-exp-advanced\":{\"x-goog-ext-525001261-jspb\":'[null,null,null,null,\"b1e46a6037e6aa9f\"]'},\"gemini-1.5-flash\":{\"x-goog-ext-525001261-jspb\":'[null,null,null,null,\"418ab5ea040b5c43\"]'},\"gemini-1.5-pro\":{\"x-goog-ext-525001261-jspb\":'[null,null,null,null,\"9d60dfae93c9ff1f\"]'},\"gemini-1.5-pro-research\":{\"x-goog-ext-525001261-jspb\":'[null,null,null,null,\"e5a44cb1dae2b489\"]'}},this.defaultModel=\"gemini-2.0-flash\",this.initializeStorage().catch(console.error)}async initializeStorage(){(await this.getAllThreads()).length||await this.saveThreadsToStorage([]),await this.validateExistingThreads()}async validateExistingThreads(){const e=await this.getAllThreads();let t=!1;for(const r of e)r.modelName!==this.getName()||this.isValidGeminiMetadata(r.metadata)||(await this.deleteThread(r.id),t=!0);t&&await this.saveThreadsToStorage(e.filter((e=>e.modelName!==this.getName()||this.isValidGeminiMetadata(e.metadata))))}isValidGeminiMetadata(e){return void 0!==e?.conversationId&&e?.contextIds&&Array.isArray(e.contextIds)&&3===e.contextIds.length&&e?.requestParams?.atValue&&e.requestParams?.blValue&&e.requestParams?.sid&&\"string\"==typeof e?.emoji&&\"string\"==typeof e?.defaultLang&&e.defaultLang&&\"string\"==typeof e?.defaultModel&&e.defaultModel&&\"string\"==typeof e?.shareUrl}getGeminiMetadata(){const t=this.getCurrentThreadSafe();if(!t.metadata)return this.handleModelError(\"No thread metadata available\",e.INVALID_REQUEST);const r=t.metadata;return this.isValidGeminiMetadata(r)?r:this.handleModelError(\"Invalid thread metadata\",e.INVALID_REQUEST)}getName(){return\"Google Bard\"}supportsImageInput(){return!0}async initNewThread(){this.currentThread={id:c(),title:\"New Conversation\",messages:[],createdAt:Date.now(),updatedAt:Date.now(),modelName:this.getName(),metadata:{conversationId:\"\",contextIds:[\"\",\"\",\"\"],requestParams:await this.fetchRequestParams(),emoji:\"\",defaultLang:\"en\",defaultModel:\"2.0 Flash\",shareUrl:\"\"}},await this.saveThread()}async fetchRequestParams(){try{const t=await ge(\"https://gemini.google.com/\",{responseType:\"text\"}),r=fe(\"SNlM0e\",t),a=fe(\"cfb2h\",t),s=fe(\"FdrFJe\",t);return r&&a&&s?{atValue:r,blValue:a,sid:s}:this.handleModelError(\"Failed to extract Bard parameters\",e.UNAUTHORIZED)}catch(t){return this.handleModelError(\"Failed to initialize Bard session\",e.UNAUTHORIZED,void 0,t)}}parseBardResponse(t){try{const r=t.split(\"\\n\").find((e=>e.includes('\"rc_')));if(!r)return this.handleModelError(\"Could not find primary data line in response text for fallback parsing.\",e.RESPONSE_PARSING_ERROR);let a;try{a=JSON.parse(r)}catch(t){return this.handleModelError(`Failed to parse data line JSON: ${t}`,e.RESPONSE_PARSING_ERROR,void 0,t)}if(!Array.isArray(a)||!a[0]||!a[0][2])return this.handleModelError(\"Unexpected structure in parsed data line.\",e.RESPONSE_PARSING_ERROR);const s=a[0][2],n=JSON.parse(s);if(!n)return this.handleModelError(\"Empty response data\",e.RESPONSE_PARSING_ERROR);const o=n[4]?.[0]?.[1]?.[0]??\"\",i=[n[1]?.[0]??\"\",n[1]?.[1]??\"\",n[4]?.[0]?.[0]??\"\"],d=n[4]?.[0]?.[4]||[];let l=o;for(const e of d){const[t,r,a]=e;t&&r&&a&&t[0]?.[0]&&r[0]?.[0]&&t[4]&&(l=l.replace(a,`[![${t[4]}](${t[0][0]})](${r[0][0]})`))}return{text:l,ids:i}}catch(t){return this.handleModelError(`Failed to parse Bard response (fallback): ${t}`,e.RESPONSE_PARSING_ERROR,void 0,t)}}async uploadImage(t){try{const r={\"content-type\":\"application/x-www-form-urlencoded;charset=UTF-8\",\"push-id\":\"feeds/mcudyrk2a4khkz\",\"x-goog-upload-header-content-length\":t.size.toString(),\"x-goog-upload-protocol\":\"resumable\",\"x-tenant-id\":\"bard-storage\"},a=(await ge.raw(\"https://content-push.googleapis.com/upload/\",{method:\"POST\",headers:{...r,\"x-goog-upload-command\":\"start\"},body:new URLSearchParams({[`File name: ${t.name}`]:\"\"})})).headers.get(\"x-goog-upload-url\");if(!a)return this.handleModelError(\"Failed to get upload URL for image\",e.SERVICE_UNAVAILABLE);return await ge(a,{method:\"POST\",headers:{...r,\"x-goog-upload-command\":\"upload, finalize\",\"x-goog-upload-offset\":\"0\"},body:t})}catch(r){return this.handleModelError(`Failed to finalize image upload: ${t.name}`,e.UPLOAD_FAILED,void 0,r)}}async ensureThreadLoaded(){if(!this.currentThread){const e=(await this.getAllThreads()).filter((e=>e.modelName===this.getName()&&this.isValidGeminiMetadata(e.metadata)));if(e.length>0){const t=e.sort(((e,t)=>t.updatedAt-e.updatedAt))[0];this.currentThread=t,console.log(\"Loaded existing thread from storage:\",this.currentThread.id)}else await this.initNewThread()}}async doSendMessage(r){let a;r.images&&r.images.length>1&&this.handleModelError(\"Gemini Web only supports one image per message.\",e.UPLOAD_AMOUNT_EXCEEDED,r);try{r.onEvent({type:\"UPDATE_ANSWER\",data:{text:\"\"}}),await this.ensureThreadLoaded(),a=this.getCurrentThreadSafe();const t=this.createMessage(\"user\",r.prompt);a.messages.push(t);const s=this.getBardMetadata();let n,o;if(console.log(\"Current context IDs before request:\",s.contextIds),r.images&&r.images.length>0){r.images.length>1&&console.warn(\"GeminiWebModel only supports one image per message. Using the first image.\"),o=r.images[0];try{n=await this.uploadImage(o),t.metadata={...t.metadata||{},attachmentUrl:n}}catch(t){this.handleModelError(`Failed to upload image: ${o.name}`,e.UPLOAD_FAILED,r,t)}}const i=[null,JSON.stringify([[r.prompt,0,null,n&&o?[[[n,1],o.name]]:[]],null,s.contextIds])],d=this.models[r.model||this.defaultModel],l=await fetch(\"https://gemini.google.com/_/BardChatUi/data/assistant.lamda.BardFrontendService/StreamGenerate\",{method:\"POST\",signal:r.signal,headers:{\"Content-Type\":\"application/x-www-form-urlencoded;charset=UTF-8\",...d||{}},body:new URLSearchParams({at:s.requestParams.atValue,\"f.req\":JSON.stringify(i),bl:s.requestParams.blValue,_reqid:String(pe()),rt:\"c\"})});if(!l.ok||!l.body){const t=await l.text();return this.handleModelError(`Gemini API error: ${l.status} - ${t.substring(0,200)}`,e.SERVICE_UNAVAILABLE,r)}const c=l.body.getReader(),h=new TextDecoder;let u=\"\",g=\"\",m=null,p=!1;for(;;){const{done:e,value:t}=await c.read();if(e)break;let s;for(u+=h.decode(t,{stream:!0});(s=u.indexOf(\"\\n\"))>=0;){const e=u.slice(0,s).trim();if(u=u.slice(s+1),e&&!/^\\d+$/.test(e)&&\")]}'\"!==e)try{const t=JSON.parse(e);if(Array.isArray(t)&&t.length>0&&Array.isArray(t[0])&&\"wrb.fr\"===t[0][0]){const e=t[0][2];if(\"string\"==typeof e){const t=JSON.parse(e);if(!p&&a.messages.length<=1&&t&&t[10]&&Array.isArray(t[10])&&\"string\"==typeof t[10][0]){let e=t[10][0];e.endsWith(\"\\n\")&&(e=e.slice(0,-1)),e&&(r.onEvent({type:\"TITLE_UPDATE\",data:{title:e,threadId:a.id}}),a.title=e,p=!0)}if(t&&t[4]?.[0]?.[1]?.[0]){const e=t[4][0][1][0];e!==g&&(g=e,r.onEvent({type:\"UPDATE_ANSWER\",data:{text:g}}))}if(t&&t[1]?.[0]&&t[1]?.[1]&&t[4]?.[0]?.[0]&&(m=[t[1][0],t[1][1],t[4][0][0]],a.metadata)){const e=a.metadata;JSON.stringify(e.contextIds)!==JSON.stringify(m)&&(e.contextIds=m,console.log(\"Updated context IDs mid-stream:\",m)),m[0]&&e.conversationId!==m[0]&&(e.conversationId=m[0],console.log(\"Updated conversationId mid-stream:\",m[0]))}}}}catch(t){console.warn(\"Error parsing Gemini stream line:\",e,t)}}}if(!m){console.warn(\"Final IDs not found in stream, attempting fallback parse.\");try{m=this.parseBardResponse(g).ids}catch(t){return console.error(\"Fallback parsing failed:\",t),this.handleModelError(\"Failed to extract final IDs from response stream\",e.RESPONSE_PARSING_ERROR,r,t)}}const f=this.createMessage(\"assistant\",g);if(f.metadata={messageId:m[1]},a.messages.push(f),a.metadata){const e=a.metadata;e.contextIds=m,e.conversationId=m[0]||\"\"}a.updatedAt=Date.now(),await this.saveThread(),r.onEvent({type:\"DONE\",data:{threadId:a.id}})}catch(a){this.handleModelError(\"Error during Gemini message sending or processing\",a instanceof t?a.code:e.NETWORK_ERROR,r,a)}}async loadThread(e){const t=await this.getAllThreads(),r=t.find((t=>t.id===e));if(r&&r.modelName===this.getName()){this.currentThread=r;this.currentThread.metadata.requestParams=await this.fetchRequestParams(),await this.saveThread(),await this.saveThreadsToStorage(t)}}getBardMetadata(){const t=this.getCurrentThreadSafe();if(!t.metadata)return this.handleModelError(\"No thread metadata available\",e.INVALID_REQUEST);if(!this.isValidGeminiMetadata(t.metadata))return this.handleModelError(\"Invalid or incomplete thread metadata\",e.INVALID_REQUEST);return t.metadata}getCurrentThreadSafe(){return this.currentThread?this.currentThread:this.handleModelError(\"No active thread\",e.INVALID_REQUEST)}async saveThread(){if(!this.currentThread)return;const e=await this.getAllThreads(),t=e.findIndex((e=>e.id===this.currentThread.id));-1!==t?e[t]=this.currentThread:e.push(this.currentThread),await this.saveThreadsToStorage(e)}async editTitle(t,r,a){try{let s,n=!1!==a?.loadThread,o=!1!==a?.tryUpdateThread;if(console.log(n,o),n)await this.ensureThreadLoaded(),s=this.getBardMetadata();else{if(!a?.metadata)return this.handleModelError(\"No thread loaded and no metadata provided for title edit\",e.INVALID_REQUEST);if(s=a.metadata,!this.isValidGeminiMetadata(s))return this.handleModelError(\"Invalid metadata provided for title edit\",e.INVALID_REQUEST)}const i=s.conversationId;if(!i){const t=s.contextIds[0];if(!t)return this.handleModelError(\"Missing conversation ID in metadata for editTitle\",e.INVALID_REQUEST);console.warn(\"Using fallback conversation ID from contextIds[0] for editTitle\"),s.conversationId=t,await this.saveThread()}const d=[i,t];r&&d.push(null,null,r,null,null,null,null,null,[1,r]);const l=JSON.stringify([null,[[\"title\",\"icon\",\"user_selected_icon\"]],d]),c=JSON.stringify([[\"MUAZcd\",l,null,\"generic\"]]),h=new URL(\"https://gemini.google.com/_/BardChatUi/data/batchexecute\");h.searchParams.set(\"rpcids\",\"MUAZcd\"),h.searchParams.set(\"source-path\",`/app/${i}`),h.searchParams.set(\"bl\",s.requestParams.blValue);const u=s.requestParams.sid??String(-Math.floor(9e18*Math.random()));h.searchParams.set(\"f.sid\",u),h.searchParams.set(\"hl\",\"en\"),h.searchParams.set(\"_reqid\",String(pe())),h.searchParams.set(\"rt\",\"c\");let g=new URLSearchParams({at:s.requestParams.atValue,\"f.req\":\"[\"+c+\"]\"});const m=await ge.raw(h.toString(),{method:\"POST\",headers:{\"Content-Type\":\"application/x-www-form-urlencoded;charset=UTF-8\"},body:g.toString()+\"&\",parseResponse:t=>{const r=t.substring(t.indexOf(\"\\n\")+1).split(\"\\n\").find((e=>e.trim().startsWith('[[\"wrb.fr\"')));return r?JSON.parse(r):(console.error(\"Raw editTitle response:\",t),this.handleModelError(\"Could not find data line in editTitle response\",e.RESPONSE_PARSING_ERROR))}});if(console.log(\"Raw editTitle response:\",m),console.log(\"Parsed e response:\",m?._data[0][0]),console.log(\"Parersed e response:\",m?._data[0][1]),!m||\"object\"!=typeof m||\"wrb.fr\"!==m?._data[0][0]||\"MUAZcd\"!==m?._data[0][1]||!m?._data[0][2])return this.handleModelError(\"Title update failed. Server response did not indicate success.\",e.UNKNOWN_ERROR);console.log(\"Title updated successfully on server.\");try{const e=JSON.parse(m?._data[0][2]),t=e[1][1],r=e[1][4];console.log(\"Server response after title update:\",e),console.log(\"Server title change confirmation received:\",t),console.log(this.currentThread),o&&this.currentThread&&this.currentThread.metadata&&(console.log(this.currentThread),this.currentThread.title=t,this.currentThread.metadata.emoji=r||\"\",await this.saveThread(),console.log(\"Thread updated locally after title change confirmation.\"))}catch(t){return console.warn(\"Could not parse success response details:\",t),console.error(\"Unexpected response structure after title update. Could not parse response:\",m),this.handleModelError(\"Title update succeeded but response format unexpected\",e.RESPONSE_PARSING_ERROR,void 0,t)}}catch(t){this.handleModelError(\"Error updating conversation title\",e.SERVICE_UNAVAILABLE,void 0,t)}}async deleteServerThreads(t,r=!0,a=!0){try{const s=await this.getAllThreads();for(const n of t)try{const t=s.find((e=>e.metadata?.conversationId===n));if(!t){console.warn(`[deleteServerThreads] Thread ${n} not found locally.`);continue}if(t.modelName!==this.getName()){console.warn(`[deleteServerThreads] Thread ${n} has incorrect model name: ${t.modelName}. Skipping.`);continue}if(!this.isValidGeminiMetadata(t.metadata)){console.warn(`[deleteServerThreads] Thread ${n} has invalid or missing metadata. Cannot delete from server.`),r&&(console.warn(`[deleteServerThreads] Deleting thread ${n} locally due to invalid metadata.`),await this.deleteThread(t.id,a));continue}const o=t.metadata;let i=o.conversationId,d=o.requestParams.atValue,l=o.requestParams.blValue;if(!i){const e=o.contextIds[0];if(!e){console.warn(`[deleteServerThreads] Missing conversation ID in metadata for thread ${n}. Cannot delete from server.`),r&&(console.warn(`[deleteServerThreads] Deleting thread ${n} locally due to missing conversation ID.`),await this.deleteThread(t.id,a));continue}console.warn(`[deleteServerThreads] Using fallback conversation ID from contextIds[0] for thread ${n}.`),o.conversationId=e,i=e,await this.saveThread()}if(!d||!l){console.warn(`[deleteServerThreads] Missing 'at' or 'bl' value in requestParams for thread ${n}. Fetching fresh params.`);try{o.requestParams=await this.fetchRequestParams(),d=o.requestParams.atValue,l=o.requestParams.blValue,await this.saveThread()}catch(e){console.error(`[deleteServerThreads] Failed to refresh requestParams for thread ${n}. Skipping server delete.`,e),r&&(console.warn(`[deleteServerThreads] Deleting thread ${n} locally due to failed param refresh.`),await this.deleteThread(t.id,a));continue}}const c=\"https://gemini.google.com/_/BardChatUi/data/batchexecute\",h=JSON.stringify([[\"GzXR5e\",JSON.stringify([i]),null,\"generic\"]]),u=new URLSearchParams({rpcids:\"GzXR5e\",\"source-path\":\"/app\",bl:l,\"f.sid\":o.requestParams.sid??String(-Math.floor(9e18*Math.random())),hl:\"en\",_reqid:String(pe()),rt:\"c\"}),g=new URLSearchParams({at:d,\"f.req\":`[${h}]`});console.log(`[deleteServerThreads] Sending Request 1 for ${n} (ConvID: ${i})`);const m=await ge.raw(`${c}?${u.toString()}`,{method:\"POST\",headers:{\"Content-Type\":\"application/x-www-form-urlencoded;charset=UTF-8\"},body:g.toString()+\"&\",parseResponse:e=>e});if(200!==m.status)return console.error(`[deleteServerThreads] Request 1 failed for ${n}. Status: ${m.status}`,await m._data),this.handleModelError(`Request 1 failed with status ${m.status}`,e.SERVICE_UNAVAILABLE);console.log(`[deleteServerThreads] Request 1 successful for ${n}.`);const p=JSON.stringify([[\"qWymEb\",JSON.stringify([i,[1,null,0,1]]),null,\"generic\"]]),f=new URLSearchParams({rpcids:\"qWymEb\",\"source-path\":\"/app\",bl:l,\"f.sid\":o.requestParams.sid??String(-Math.floor(9e18*Math.random())),hl:\"en\",_reqid:String(pe()),rt:\"c\"}),E=new URLSearchParams({at:d,\"f.req\":`[${p}]`});console.log(`[deleteServerThreads] Sending Request 2 for ${n} (ConvID: ${i})`);const A=await ge.raw(`${c}?${f.toString()}`,{method:\"POST\",headers:{\"Content-Type\":\"application/x-www-form-urlencoded;charset=UTF-8\"},body:E.toString()+\"&\",parseResponse:e=>e});if(200!==A.status)return console.error(`[deleteServerThreads] Request 2 failed for ${n}. Status: ${A.status}`,await A._data),this.handleModelError(`Request 2 failed with status ${A.status}`,e.SERVICE_UNAVAILABLE);console.log(`[deleteServerThreads] Request 2 successful for ${n}.`),r&&(console.log(`[deleteServerThreads] Deleting thread ${n} locally.`),await this.deleteThread(t.id,a))}catch(e){console.error(`[deleteServerThreads] Failed to process thread ${n}:`,e)}}catch(t){return this.handleModelError(\"Error during server thread deletion process\",e.SERVICE_UNAVAILABLE,void 0,t)}}async getConversationData(r){try{let t,a=!1!==r?.loadThread;if(r?.metadata&&(a=!1),a)await this.ensureThreadLoaded(),t=this.getBardMetadata();else{if(!r?.metadata)return this.handleModelError(\"No thread loaded and no metadata provided for getting conversation data\",e.INVALID_REQUEST);if(t=r.metadata,!this.isValidGeminiMetadata(t))return this.handleModelError(\"Invalid metadata provided for getting conversation data\",e.INVALID_REQUEST)}const s=t.conversationId||t.contextIds[0];if(!s)return this.handleModelError(\"Missing conversation ID in metadata\",e.INVALID_REQUEST);const n=JSON.stringify([s,10,null,1,[1],null,null,1]),o=JSON.stringify([[\"hNvQHb\",n,null,\"generic\"]]),i=new URL(\"https://gemini.google.com/_/BardChatUi/data/batchexecute\");i.searchParams.set(\"rpcids\",\"hNvQHb\"),i.searchParams.set(\"source-path\",`/app/${s.substring(2)}`),i.searchParams.set(\"bl\",t.requestParams.blValue);const d=t.requestParams.sid??String(-Math.floor(9e18*Math.random()));i.searchParams.set(\"f.sid\",d),i.searchParams.set(\"hl\",\"en\"),i.searchParams.set(\"_reqid\",String(pe())),i.searchParams.set(\"rt\",\"c\");let l=new URLSearchParams({\"f.req\":`[${o}]`,at:t.requestParams.atValue});const c=await ge(i.toString(),{method:\"POST\",headers:{\"Content-Type\":\"application/x-www-form-urlencoded;charset=UTF-8\"},body:l+\"&\",parseResponse:t=>{const r=t.substring(t.indexOf(\"\\n\")+1).split(\"\\n\").find((e=>e.trim().startsWith('[[\"wrb.fr\"')));return r?JSON.parse(r):(console.error(\"Raw getConversationData response:\",t),this.handleModelError(\"Could not find data line in getConversationData response\",e.RESPONSE_PARSING_ERROR))}});if(console.log(c),!c||!Array.isArray(c)||0===c.length||!Array.isArray(c[0])||c[0].length<3)return console.error(\"Unexpected response structure in getConversationData:\",c),this.handleModelError(\"Unexpected response structure in getConversationData\",e.RESPONSE_PARSING_ERROR);const h=c[0][2],u=JSON.parse(h),g=[];if(Array.isArray(u?.[0]))for(const e of u[0].slice().reverse()){if(!Array.isArray(e)||e.length<4){console.warn(\"Skipping invalid message pair structure:\",e);continue}const t=e[2],r=e[4];let a=null;if(Array.isArray(t)&&Array.isArray(t[0])&&\"string\"==typeof t[0][0]){const e=t[0][0],s=Array.isArray(r)&&\"number\"==typeof r[0]?r[0]:void 0;a=this.createMessage(\"user\",e),void 0!==s&&(a.timestamp=s),a.metadata={}}else console.warn(\"Could not extract user message text from:\",t);const s=e[3],n=e[4];let o=null;if(Array.isArray(s)&&Array.isArray(s[0])&&Array.isArray(s[0][0])){const e=s[0][0];if(Array.isArray(e)&&e.length>1&&Array.isArray(e[1])&&\"string\"==typeof e[1][0]){const t=e[1][0],r=Array.isArray(n)&&\"number\"==typeof n[0]?n[0]:void 0;o=this.createMessage(\"assistant\",t),void 0!==r&&(o.timestamp=r),o.metadata={}}else console.warn(\"Could not extract assistant message text from:\",e)}else console.warn(\"Could not extract assistant message wrapper from:\",s);a&&g.push(a),o&&g.push(o)}else console.warn(\"No message pairs found in conversation payload:\",u);return g}catch(r){return console.error(\"Error getting conversation data:\",r),this.handleModelError(`Failed to get conversation data: ${r instanceof Error?r.message:String(r)}`,r instanceof t?r.code:e.SERVICE_UNAVAILABLE,void 0,r)}}async shareConversation(t){try{let r=!1!==t?.loadThread;t?.metadata&&(r=!1),t||(t={});let a,s=t?.title||\"\",n=t?.modelName||\"\",o=t?.language||\"\";if(r)await this.ensureThreadLoaded(),a=this.getGeminiMetadata(),s||(s=this.currentThread?.title||\"\"),n||(n=this.currentThread?.metadata?.defaultModel||\"\"),o||(o=this.currentThread?.metadata?.defaultLang||\"\");else{if(!t?.metadata)return this.handleModelError(\"No thread loaded and no metadata provided for sharing\",e.INVALID_REQUEST);if(!this.isValidGeminiMetadata(t?.metadata))return this.handleModelError(\"Invalid metadata provided for sharing\",e.INVALID_REQUEST);a=t.metadata,n=a.defaultModel||\"\",o=a.defaultLang||\"\"}s||(console.warn('Title is required when sharing a conversation, but not provided or is blank. Using \"Untitled Conversation\" as title.'),s=\"Untitled Conversation\"),n||(console.warn('Model name is required when sharing a conversation, but not provided. Using default model name \"2.0 Flash\".'),n=\"2.0 Flash\"),o||(console.warn('Language is required when sharing a conversation, but not provided. Using default language \"en\".'),o=\"en\");const i=[[[\"fuVx7\",JSON.stringify([null,a.contextIds[0],null,a.contextIds[2],[1,s+\"\\n\",null,null,null,[\"\",\"\",\"\"],null,[null,null,n]],[o],0]),null,\"generic\"]]],d=new URLSearchParams({\"f.req\":JSON.stringify(i),at:a.requestParams.atValue}),l=`https://gemini.google.com/_/BardChatUi/data/batchexecute?${new URLSearchParams({rpcids:\"fuVx7\",\"source-path\":`/app/${a.contextIds[0].substring(2)}`,bl:a.requestParams.blValue,\"f.sid\":a.requestParams.sid,hl:t.language||\"en\",_reqid:String(pe()),rt:\"c\"})}`,c=await ge(l,{method:\"POST\",headers:{\"Content-Type\":\"application/x-www-form-urlencoded;charset=UTF-8\"},body:d+\"&\",parseResponse:e=>e,onResponse:({response:t})=>{if(!t.ok)return this.handleModelError(`Failed to share conversation: ${t.status}`,e.SERVICE_UNAVAILABLE,void 0,t.statusText)}}),h=c.split(\"\\n\").find((e=>e.includes('\"wrb.fr\"')));if(!h)return this.handleModelError(\"Failed to parse Gemini share response\",e.RESPONSE_PARSING_ERROR);console.log(h);let u=\"\";try{const e=JSON.parse(h);if(console.log(e),e[0]&&Array.isArray(e[0])&&e[0][2]){const t=JSON.parse(e[0][2]);Array.isArray(t)&&t[2]&&(u=t[2])}}catch(t){return this.handleModelError(\"Error extracting share ID from Gemini response\",e.RESPONSE_PARSING_ERROR,void 0,t)}if(!u)return this.handleModelError(\"No share ID found in Gemini response\",e.RESPONSE_PARSING_ERROR);const g=`https://g.co/gemini/share/${u}`;return r&&this.currentThread&&this.currentThread.metadata&&(this.currentThread.metadata.shareUrl=g,await this.saveThread()),g}catch(t){return this.handleModelError(\"Error sharing conversation\",e.SERVICE_UNAVAILABLE,void 0,t)}}async unShareConversation(t){try{let r,a=!1,s=!1!==t?.loadThread,n=!1!==t?.updateThread;if(t?.metadata&&(s=!1),t||(t={}),s)await this.ensureThreadLoaded(),r=this.getGeminiMetadata();else{if(!t?.metadata)return this.handleModelError(\"No thread loaded and no metadata provided for sharing\",e.INVALID_REQUEST);if(n)return this.handleModelError(\"Cannot update thread when LoadThread option is false (updateThread option is not supported)\",e.INVALID_REQUEST);if(!this.isValidGeminiMetadata(t?.metadata))return this.handleModelError(\"Invalid metadata provided for sharing\",e.INVALID_REQUEST);r=t.metadata}if(!r.shareUrl||!r?.shareUrl.includes(\"https://g.co/gemini/share/\"))return this.handleModelError(\"No share URL found in metadata\",e.INVALID_REQUEST);const o=[[[\"SgORbf\",JSON.stringify([null,r.shareUrl.replace(\"https://g.co/gemini/share/\",\"\")]),null,\"generic\"]]],i=new URLSearchParams({\"f.req\":JSON.stringify(o),at:r.requestParams.atValue}),d=`https://gemini.google.com/_/BardChatUi/data/batchexecute?${new URLSearchParams({rpcids:\"fuVx7\",\"source-path\":`/app/${r.shareUrl.replace(\"https://g.co/gemini/share/\",\"\")}`,bl:r.requestParams.blValue,\"f.sid\":r.requestParams.sid,hl:r.defaultLang||\"en\",_reqid:String(pe()),rt:\"c\"})}`,l=await ge(d,{method:\"POST\",headers:{\"Content-Type\":\"application/x-www-form-urlencoded;charset=UTF-8\"},body:i+\"&\",parseResponse:e=>e,onResponse:({response:t})=>{if(!t.ok)return this.handleModelError(`Failed to un-share conversation: ${t.status}`,e.SERVICE_UNAVAILABLE,void 0,t.statusText)}}),c=l.split(\"\\n\").find((e=>e.includes('\"wrb.fr\"')));if(!c)return this.handleModelError(\"Failed to parse Gemini un-share response\",e.RESPONSE_PARSING_ERROR);console.log(c);try{const e=JSON.parse(c);if(console.log(e),e[0]&&Array.isArray(e[0])&&e[0][2])return a=\"[]\"===e[0][2],a&&n&&this.currentThread&&this.currentThread.metadata&&(this.currentThread.metadata.shareUrl=\"\",await this.saveThread()),a}catch(t){return this.handleModelError(\"Failed to parse Gemini un-share response\",e.RESPONSE_PARSING_ERROR,void 0,t)}return a}catch(t){return this.handleModelError(\"Error sharing conversation\",e.SERVICE_UNAVAILABLE,void 0,t)}}}I([me,b(\"design:type\",Function),b(\"design:paramtypes\",[File]),b(\"design:returntype\",Promise)],Ee.prototype,\"uploadImage\",null),I([me,b(\"design:type\",Function),b(\"design:paramtypes\",[String,String,Object]),b(\"design:returntype\",Promise)],Ee.prototype,\"editTitle\",null),I([me,b(\"design:type\",Function),b(\"design:paramtypes\",[Array,Boolean,Boolean]),b(\"design:returntype\",Promise)],Ee.prototype,\"deleteServerThreads\",null),I([me,b(\"design:type\",Function),b(\"design:paramtypes\",[Object]),b(\"design:returntype\",Promise)],Ee.prototype,\"getConversationData\",null),I([me,b(\"design:type\",Function),b(\"design:paramtypes\",[Object]),b(\"design:returntype\",Promise)],Ee.prototype,\"shareConversation\",null),I([me,b(\"design:type\",Function),b(\"design:paramtypes\",[Object]),b(\"design:returntype\",Promise)],Ee.prototype,\"unShareConversation\",null);var Ae=null;\"undefined\"!=typeof WebSocket?Ae=WebSocket:\"undefined\"!=typeof MozWebSocket?Ae=MozWebSocket:\"undefined\"!=typeof global?Ae=global.WebSocket||global.MozWebSocket:\"undefined\"!=typeof window?Ae=window.WebSocket||window.MozWebSocket:\"undefined\"!=typeof self&&(Ae=self.WebSocket||self.MozWebSocket);var ye=Ae;class Te extends h{constructor(){super(),this.baseUrl=\"https://copilot.microsoft.com\",this.initializeStorage().catch(console.error)}async initializeStorage(){(await this.getAllThreads()).length||await this.saveThreadsToStorage([]),await this.validateExistingThreads()}async validateExistingThreads(){const e=await this.getAllThreads();let t=!1;for(const r of e)r.modelName!==this.getName()||this.isValidBingMetadata(r.metadata)||(await this.deleteThread(r.id),t=!0);t&&await this.saveThreadsToStorage(e.filter((e=>e.modelName!==this.getName()||this.isValidBingMetadata(e.metadata))))}isValidBingMetadata(e){return e?.conversationId}getName(){return\"Bing Copilot\"}supportsImageInput(){return!0}static injectedCopilotTokenExtractor(){for(let e=0;e<localStorage.length;e++)try{const t=localStorage.key(e);if(!t)continue;const r=JSON.parse(localStorage.getItem(t)||\"\");if(r&&\"AccessToken\"===r.credentialType&&r.expiresOn>Math.floor(Date.now()/1e3)&&r.target?.includes(\"ChatAI\"))return console.log(\"[Copilot Inject] Token found:\",r.secret),r.secret}catch(e){}return console.log(\"[Copilot Inject] No valid token found in localStorage.\"),null}async getCopilotToken(e=!1){return await S(\"Copilot\",this.baseUrl,`${this.baseUrl}/*`,\"copilotExtractor\",e)}async ensureAuthToken(){if(!this.authToken){console.log(\"[Copilot ensureAuthToken] Token missing, retrieving...\");const t=await this.getCopilotToken(!0);if(!t)return console.error(\"[Copilot ensureAuthToken] Failed to retrieve token.\"),this.handleModelError(\"Failed to get Copilot authorization token\",e.UNAUTHORIZED);this.authToken=`Bearer ${t}`,console.log(\"[Copilot ensureAuthToken] Token retrieved and set.\")}return this.authToken}async createConversation(){try{if(!await E(`${this.baseUrl}/`))return this.handleModelError(`Missing ${this.baseUrl} permission`,e.MISSING_HOST_PERMISSION);const t=await this.ensureAuthToken(),r=await fetch(`${this.baseUrl}/c/api/start`,{method:\"POST\",headers:{accept:\"*/*\",\"content-type\":\"application/json\",authorization:t},body:JSON.stringify({timeZone:Intl.DateTimeFormat().resolvedOptions().timeZone,startNewConversation:!0}),credentials:\"same-origin\"});if(!r.ok){if(401===r.status)return console.log(\"Copilot createConversation returned 401. Token might be invalid/expired.\"),this.authToken=void 0,this.handleModelError(\"Authorization failed (401). Please try again.\",e.UNAUTHORIZED);if(!r.ok)return this.handleModelError(`Failed to create conversation: ${r.status}`,e.SERVICE_UNAVAILABLE,void 0,await r.text())}const a=await r.json();return console.log(\"Copilot create conversation response:\",a),a.currentConversationId?{conversationId:a.currentConversationId}:this.handleModelError(\"Failed to create Copilot conversation\",e.SERVICE_UNAVAILABLE)}catch(t){return console.error(\"Error initializing Copilot session:\",t),this.handleModelError(\"Failed to initialize Copilot session\",e.NETWORK_ERROR,void 0,t)}}async doSendMessage(t){t.images&&t.images.length>1&&this.handleModelError(\"Bing Copilot only supports one image per message.\",e.UPLOAD_AMOUNT_EXCEEDED,t);let r=null,a=null;try{if(!await E(`wss://${new URL(this.baseUrl).hostname}/`))return this.handleModelError(`Missing ${this.baseUrl} permission`,e.MISSING_HOST_PERMISSION);console.log(\"Full params received in doSendMessage:\",JSON.stringify({prompt:t.prompt,hasImage:!!(t.images&&t.images.length>0),hasSignal:!!t.signal,hasOnEvent:!!t.onEvent,mode:t.mode})),console.log(\"Received mode:\",t.mode);let s=\"chat\";t.mode?\"chat\"===t.mode||\"reasoning\"===t.mode?(s=t.mode,console.log(\"Using validated mode:\",s)):console.warn(`Invalid mode \"${t.mode}\" provided. Using default mode \"chat\" instead.`):console.log('No mode provided, using default \"chat\" mode'),t.onEvent({type:\"UPDATE_ANSWER\",data:{text:\"\"}}),await this.ensureThreadLoaded();const n=this.getCurrentThreadSafe(),o=this.createMessage(\"user\",t.prompt);let i;if(t.images&&t.images.length>0){t.images.length>1&&console.warn(\"BingCopilotWebModel currently only supports one image per message. Using the first image.\"),i=t.images[0];const r=await new Promise(((r,a)=>{const s=new FileReader;s.onload=e=>r(e.target?.result),s.onerror=r=>{this.handleModelError(\"Failed to read image file\",e.UPLOAD_FAILED,t,r),a(new Error(\"Failed to read image file\"))},s.readAsDataURL(i)}));o.metadata={...o.metadata,imageDataUrl:r}}n.messages.push(o),await this.saveThread();const d=await this.getBingMetadata();let l,c;if(i)try{l=await this.uploadImage(i),c=this.baseUrl+l,console.log(\"Image uploaded successfully:\",c),o.metadata={...o.metadata,fullUrl:c},await this.saveThread()}catch(r){this.handleModelError(`Failed to upload image: ${i.name}`,e.UPLOAD_FAILED,t,r)}let h=\"\",u=[],g=!1,m=!1;const p=n.messages.length<=1;let f=!p;const A=await this.ensureAuthToken(),y=A.startsWith(\"Bearer \")?A.substring(7):A;if(!y)return this.handleModelError(\"Could not extract raw token for WebSocket\",e.UNAUTHORIZED,t);r=new ye(`wss://${new URL(this.baseUrl).hostname}/c/api/chat?api-version=2&accessToken=${encodeURIComponent(y)}`);const T=setTimeout((()=>{r&&r.readyState!==ye.OPEN&&(console.error(\"WebSocket connection timeout\"),r.close(),this.handleModelError(\"Connection timeout\",e.NETWORK_ERROR,t))}),7e3),_=()=>{if(a&&(clearTimeout(a),a=null),r&&(r.readyState===ye.OPEN||r.readyState===ye.CONNECTING)){console.log(\"Safely closing WebSocket connection\");try{r.close()}catch(e){console.error(\"Error closing WebSocket:\",e)}}r=null},w=()=>{g&&(f&&m||!a)&&_()};r.onopen=()=>{console.log(\"WebSocket connection opened\"),clearTimeout(T);try{const e=[];l&&e.push({type:\"image\",url:l}),e.push({type:\"text\",text:t.prompt});const a={event:\"send\",mode:s,conversationId:d.conversationId,content:e};console.log(\"Sending message:\",JSON.stringify(a)),r.send(JSON.stringify(a))}catch(r){console.error(\"Error sending message:\",r),_(),this.handleModelError(\"Failed to send message\",e.NETWORK_ERROR,t,r)}},r.onmessage=r=>{try{const e=JSON.parse(r.data);if(console.log(\"WebSocket message:\",e),\"received\"===e.event)console.log(\"Message received by server:\",e.messageId);else if(\"startMessage\"===e.event)console.log(\"Assistant starting response:\",e.messageId);else if(\"appendText\"===e.event)h+=e.text||\"\",t.onEvent({type:\"UPDATE_ANSWER\",data:{text:h}});else if(\"done\"===e.event){const e=this.createMessage(\"assistant\",h);n.messages.push(e),n.updatedAt=Date.now(),this.saveThread(),t.onEvent({type:\"DONE\",data:{threadId:n.id}}),g=!0,a=setTimeout((()=>{console.log(\"Closing connection after timeout - did not receive all expected events\"),_()}),5e3)}else if(\"suggestedFollowups\"===e.event&&e.suggestions){if(u=e.suggestions.map((e=>e)),n.messages.length>0){const e=n.messages[n.messages.length-1];\"assistant\"===e.role&&(e.metadata={suggestedResponses:u},this.saveThread(),t.onEvent({type:\"SUGGESTED_RESPONSES\",data:{suggestions:u}}))}m=!0,w()}else p&&\"titleUpdate\"===e.event&&e.title&&(console.log(\"Received title update:\",e.title),n.title=e.title,this.saveThread(),t.onEvent({type:\"TITLE_UPDATE\",data:{title:e.title,threadId:n.id}}),f=!0,w())}catch(r){this.handleModelError(\"Error parsing Copilot response\",e.RESPONSE_PARSING_ERROR,t,r)}},r.onerror=r=>{console.error(\"WebSocket error:\",r),_(),this.handleModelError(\"WebSocket connection error\",e.NETWORK_ERROR,t,r)},r.onclose=r=>{console.log(`WebSocket connection closed with code ${r.code}`,r.reason),clearTimeout(T),a&&(clearTimeout(a),a=null),g||this.handleModelError(`Connection closed unexpectedly (${r.code})`,e.NETWORK_ERROR,t)},t.signal&&t.signal.addEventListener(\"abort\",(()=>{console.log(\"Request aborted by user\"),_()}))}catch(s){if(a&&clearTimeout(a),r)try{r.close()}catch(e){console.error(\"Error closing WebSocket during error handling:\",e)}this.handleModelError(\"Error in WebSocket communication\",e.NETWORK_ERROR,t,s)}}async ensureThreadLoaded(){if(!this.currentThread){const e=(await this.getAllThreads()).filter((e=>e.modelName===this.getName()&&this.isValidBingMetadata(e.metadata)));if(e.length>0){const t=e.sort(((e,t)=>t.updatedAt-e.updatedAt))[0];this.currentThread=t,console.log(\"Loaded existing thread from storage:\",this.currentThread.id)}else await this.initNewThread()}}getCurrentThreadSafe(){return this.currentThread?this.currentThread:this.handleModelError(\"No active thread\",e.INVALID_REQUEST)}async initNewThread(){const e=await this.createConversation();this.currentThread={id:c(),title:\"New Conversation\",messages:[],createdAt:Date.now(),updatedAt:Date.now(),modelName:this.getName(),metadata:e},await this.saveThread()}async loadThread(t){const r=(await this.getAllThreads()).find((e=>e.id===t));if(!r||r.modelName!==this.getName())return this.handleModelError(\"Thread not found\",e.INVALID_THREAD_ID);this.currentThread=r,await this.saveThread()}async getBingMetadata(){const t=this.getCurrentThreadSafe();if(!t.metadata)return this.handleModelError(\"No thread metadata available\",e.INVALID_REQUEST);const r=t.metadata;return this.isValidBingMetadata(r)?r:await this.createConversation()}async saveThread(){if(!this.currentThread)return this.handleModelError(\"No active thread\",e.INVALID_REQUEST);await super.saveThread()}async uploadImage(t){try{const r=await this.ensureAuthToken(),a=await t.arrayBuffer();let s=\"image/jpeg\";if(t.type)s=t.type;else{const e=t.name.toLowerCase();e.endsWith(\".png\")?s=\"image/png\":e.endsWith(\".gif\")?s=\"image/gif\":e.endsWith(\".webp\")?s=\"image/webp\":e.endsWith(\".bmp\")?s=\"image/bmp\":e.endsWith(\".svg\")&&(s=\"image/svg+xml\")}const n={\"content-type\":s,authorization:r},o=await fetch(`${this.baseUrl}/c/api/attachments`,{method:\"POST\",headers:n,body:new Blob([a],{type:s}),credentials:\"include\",mode:\"cors\"});if(!o.ok){console.error(\"Image upload failed with status:\",o.status);const t=await o.text();return console.error(\"Error response:\",t),this.handleModelError(`Failed to upload image: ${o.status}`,e.SERVICE_UNAVAILABLE)}const i=await o.json();return console.log(\"Image upload response:\",i),i.url?i.url:this.handleModelError(\"Invalid image upload response\",e.SERVICE_UNAVAILABLE)}catch(t){return console.error(\"Error uploading image:\",t),this.handleModelError(\"Failed to upload image to Copilot\",e.UPLOAD_FAILED,void 0,t)}}}function _e(e,t,r){return r}class we extends h{constructor(e={}){super(),this.sessionKey=e.sessionKey,this.initializeStorage().catch(console.error)}async initializeStorage(){(await this.getAllThreads()).length||await this.saveThreadsToStorage([]),await this.validateExistingThreads()}async validateExistingThreads(){const e=await this.getAllThreads();let t=!1;for(const r of e)r.modelName!==this.getName()||this.isValidClaudeMetadata(r.metadata)||(await this.deleteThread(r.id),t=!0);t&&await this.saveThreadsToStorage(e.filter((e=>e.modelName!==this.getName()||this.isValidClaudeMetadata(e.metadata))))}isValidClaudeMetadata(e){return e?.organizationId&&e?.conversationId}getName(){return\"Claude Web\"}supportsImageInput(){return!0}async createConversation(t){const r=c();try{await ge(`https://claude.ai/api/organizations/${t}/chat_conversations`,{method:\"POST\",headers:this.getHeaders(),credentials:\"include\",body:{name:\"\",uuid:r}});return r}catch(t){return t instanceof ae&&403===t.status?this.handleModelError(\"There is no logged-in Claude account in this browser.\",e.UNAUTHORIZED,void 0,t):this.handleModelError(\"Failed to create conversation\",e.SERVICE_UNAVAILABLE,void 0,t)}}async generateChatTitle(e,t,r){try{const a=await ge(`https://claude.ai/api/organizations/${e}/chat_conversations/${t}/title`,{method:\"POST\",headers:this.getHeaders(),credentials:\"include\",body:{message_content:r,recent_titles:[]}});return a.title?a.title:\"New Conversation\"}catch(e){return console.error(\"Failed to generate chat title:\",e),\"New Conversation\"}}getHeaders(){const e={\"Content-Type\":\"application/json\",Accept:\"application/json\"};return this.sessionKey&&(e.Cookie=`sessionKey=${this.sessionKey}`),e}async ensureThreadLoaded(){if(!this.currentThread){const e=(await this.getAllThreads()).filter((e=>e.modelName===this.getName()&&this.isValidClaudeMetadata(e.metadata)));if(e.length>0){const t=e.sort(((e,t)=>t.updatedAt-e.updatedAt))[0];this.currentThread=t,console.log(\"Loaded existing thread from storage:\",this.currentThread.id)}else await this.initNewThread()}}getClaudeMetadata(){const t=this.getCurrentThreadSafe();if(!t.metadata)return this.handleModelError(\"No thread metadata available\",e.INVALID_REQUEST);const r=t.metadata;return r.organizationId&&r.conversationId?r:this.handleModelError(\"Invalid thread metadata\",e.INVALID_REQUEST)}getCurrentThreadSafe(){return this.currentThread?this.currentThread:this.handleModelError(\"No active thread\",e.INVALID_REQUEST)}async initNewThread(){try{const t=await this.getOrganizationId();if(!t)return this.handleModelError(\"Organization ID is required\",e.INVALID_REQUEST);const r=await this.createConversation(t);this.currentThread={id:r,title:\"New Conversation\",modelName:this.getName(),messages:[],createdAt:Date.now(),updatedAt:Date.now(),metadata:{organizationId:t,conversationId:r}},await this.saveThread()}catch(t){return this.handleModelError(\"Error initializing new thread\",e.METADATA_INITIALIZATION_ERROR,void 0,t)}}async loadThread(t){const r=(await this.getAllThreads()).find((e=>e.id===t));if(!r||r.modelName!==this.getName())return this.handleModelError(`Thread ${t} not found`,e.INVALID_THREAD_ID);this.currentThread=r,!this.organizationId&&r.metadata&&(this.organizationId=r.metadata.organizationId)}async doSendMessage(t){t.images&&t.images.length>1&&this.handleModelError(\"Claude Web only supports one image per message.\",e.UPLOAD_AMOUNT_EXCEEDED,t);try{t.onEvent({type:\"UPDATE_ANSWER\",data:{text:\"\"}}),await this.ensureThreadLoaded();const r=this.getCurrentThreadSafe(),a=this.getClaudeMetadata();let s;t.images&&t.images.length>0&&(t.images.length>1&&console.warn(\"ClaudeWebModel currently only supports one image per message. Using the first image.\"),s=t.images[0]);const n=this.createMessage(\"user\",t.prompt);s&&(n.metadata={...n.metadata||{},imageDataUrl:await this.fileToDataUrl(s)}),r.messages.push(n);let o={},i=[];if(s){const r=new FormData;r.append(\"file\",s);try{const s=await fetch(`https://claude.ai/api/${a.organizationId}/upload`,{method:\"POST\",credentials:\"include\",body:r});if(!s.ok){const r=await s.text();return this.handleModelError(`Failed to upload image: ${r}`,e.UPLOAD_FAILED,t,r)}if(o=await s.json(),!o||!o.file_uuid)return this.handleModelError(\"Invalid upload response format\",e.UPLOAD_FAILED,t);i=[{file_name:o.file_name,file_size:o.file_size,file_type:o.file_type,file_uuid:o.file_uuid,source:\"file_upload\"}]}catch(r){this.handleModelError(`Failed to upload image: ${s.name}`,e.UPLOAD_FAILED,t,r)}}let d=await this.getStyles(a.organizationId),l=this.findStyleByKey(d,t.style_key||\"\");t.style_key&&!l&&console.warn(`Style key '${t.style_key}' not found, using default style.`);const c=await fetch(`https://claude.ai/api/organizations/${a.organizationId}/chat_conversations/${a.conversationId}/completion`,{method:\"POST\",headers:this.getHeaders(),credentials:\"include\",signal:t.signal,body:JSON.stringify({attachments:i,files:[],locale:navigator.language||\"en-US\",personalized_styles:l?[l]:[],prompt:t.prompt,timezone:Intl.DateTimeFormat().resolvedOptions().timeZone})});if(!c.ok){const r=await c.text();try{const a=JSON.parse(r);if(429===c.status&&\"error\"===a.type&&\"rate_limit_error\"===a.error?.type)try{a.error.message=JSON.parse(a.error.message);let r=\"\";if(a.error.resetsAt){const e=new Date(1e3*a.error.resetsAt);a.error.resetsAt.resetsAtReadable=e.toLocaleString(),r=` Rate limit resets at ${a.error.resetsAt.resetsAtReadable}`}return this.handleModelError(`Claude rate limit exceeded.${r}`,e.RATE_LIMIT_EXCEEDED,t,a)}catch(r){return this.handleModelError(`Claude rate limit exceeded: ${a.error.message}`,e.RATE_LIMIT_EXCEEDED,t,a)}return this.handleModelError(`Claude API error: ${JSON.stringify(a)}`,e.SERVICE_UNAVAILABLE,t)}catch(a){return 429===c.status?this.handleModelError(\"Claude rate limit exceeded. Please try again later.\",e.RATE_LIMIT_EXCEEDED,t):this.handleModelError(`Claude API error: ${c.status} - ${r.substring(0,200)}`,e.SERVICE_UNAVAILABLE,t)}}if(!c.body)return this.handleModelError(\"Response body is null\",e.SERVICE_UNAVAILABLE,t);const h=c.body.getReader(),u=new TextDecoder;let g=\"\",m=\"\",p=\"\",f=\"\";for(;;){const{done:e,value:r}=await h.read();if(e)break;g+=u.decode(r,{stream:!0});const a=g.split(\"\\n\");g=a.pop()||\"\";for(const e of a)if(e.trim())e.startsWith(\"event:\")?p=e.substring(6).trim():e.startsWith(\"data:\")&&(f=e.substring(5).trim());else if(p&&f){const e=this.processEvent(p,f,m);null!==e&&(m=e,t.onEvent({type:\"UPDATE_ANSWER\",data:{text:m}})),p=\"\",f=\"\"}}if(g.trim()){const e=g.split(\"\\n\");for(const r of e)if(r.startsWith(\"event:\"))p=r.substring(6).trim();else if(r.startsWith(\"data:\")&&(f=r.substring(5).trim(),p&&f)){const e=this.processEvent(p,f,m);null!==e&&(m=e,t.onEvent({type:\"UPDATE_ANSWER\",data:{text:m}}))}}const E=this.createMessage(\"assistant\",m);if(r.messages.push(E),r.updatedAt=Date.now(),\"New Conversation\"===r.title&&r.messages.length<=2){const e=await this.generateChatTitle(a.organizationId,a.conversationId,t.prompt);r.title=e,t.onEvent({type:\"TITLE_UPDATE\",data:{title:e,threadId:r.id}})}await this.saveThread(),t.onEvent({type:\"DONE\",data:{threadId:r.id}})}catch(r){this.handleModelError(\"Error sending message\",e.SERVICE_UNAVAILABLE,t,r)}}processEvent(t,r,a,s){if(!t||!r)return null;try{switch(t){case\"completion\":const n=JSON.parse(r);if(n.completion)return a+n.completion;break;case\"error\":const o=JSON.parse(r);if(\"rate_limit_error\"!==o.type)return this.handleModelError(o.error||o.message||\"Unknown Claude error\",o.error||o.message?e.SERVICE_UNAVAILABLE:e.UNKNOWN_ERROR,s||void 0,o);try{o.message=JSON.parse(o.message);let t=\"\";if(o.resetsAt){const e=new Date(1e3*o.resetsAt);o.resetsAt.resetsAtReadable=e.toLocaleString(),t=` Rate limit resets at ${o.resetsAt.resetsAtReadable}`}return this.handleModelError(`Claude rate limit exceeded.${t}`,e.RATE_LIMIT_EXCEEDED,s||void 0,o)}catch(t){return this.handleModelError(`Claude rate limit exceeded: ${o.message}`,e.RATE_LIMIT_EXCEEDED,s||void 0,o)}case\"ping\":return null;default:return console.log(`Unhandled event type: ${t}`,r),null}}catch(r){console.warn(`Error processing ${t} event:`,r),this.handleModelError(`Error processing ${t} event`,e.UNKNOWN_ERROR,s,r)}return null}async fileToDataUrl(e){return new Promise(((t,r)=>{const a=new FileReader;a.onload=()=>t(a.result),a.onerror=r,a.readAsDataURL(e)}))}async editTitle(t,r){try{let a,s=!1!==r?.loadThread,n=!1!==r?.tryUpdateThread;if(r?.metadata&&(s=!1),s)await this.ensureThreadLoaded(),a=this.getClaudeMetadata();else{if(!r?.metadata)return this.handleModelError(\"No thread loaded and no metadata provided for sharing\",e.INVALID_REQUEST);if(a=r.metadata,!a.organizationId||!a.conversationId)return this.handleModelError(\"Invalid metadata provided for sharing\",e.INVALID_REQUEST)}const o=await fetch(`https://claude.ai/api/organizations/${a.organizationId}/chat_conversations/${a.conversationId}`,{method:\"PUT\",headers:this.getHeaders(),credentials:\"include\",body:JSON.stringify({name:t})});if(!o.ok)return this.handleModelError(`Failed to update title: ${o.status}`,e.SERVICE_UNAVAILABLE,void 0,await o.text());n&&this.currentThread&&(this.currentThread.title=t,await this.saveThread())}catch(t){return this.handleModelError(\"Error updating conversation title\",e.SERVICE_UNAVAILABLE,void 0,t)}}async deleteServerThreads(t,r=!0,a=!0,s){try{if(s||(s=await this.getOrganizationId()),!s||!t)return this.handleModelError(\"Invalid metadata provided for request\",e.INVALID_REQUEST);const n=await fetch(`https://claude.ai/api/organizations/${s}/chat_conversations/delete_many`,{method:\"POST\",headers:this.getHeaders(),credentials:\"include\",body:JSON.stringify({conversation_uuids:t})});if(!n.ok)return this.handleModelError(`Failed to get conversation: ${n.status}`,e.SERVICE_UNAVAILABLE,void 0,await n.text());let o=await n.json();if(r)for(let e of t)await this.deleteThread(e,a);return o}catch(t){return this.handleModelError(\"Error deleting conversation(s)\",e.SERVICE_UNAVAILABLE,void 0,t)}}async shareConversation(t){try{let r,a=!1!==t?.loadThread;if(t?.metadata&&(a=!1),a)await this.ensureThreadLoaded(),r=this.getClaudeMetadata();else{if(!t?.metadata)return this.handleModelError(\"No thread loaded and no metadata provided for sharing\",e.INVALID_REQUEST);if(r=t.metadata,!r.organizationId||!r.conversationId)return this.handleModelError(\"Invalid metadata provided for sharing\",e.INVALID_REQUEST)}const s=await fetch(`https://claude.ai/api/organizations/${r.organizationId}/chat_conversations/${r.conversationId}/share`,{method:\"POST\",headers:this.getHeaders(),credentials:\"include\",body:JSON.stringify({})});if(!s.ok){const t=await s.text();return this.handleModelError(`Failed to share conversation: ${s.status}`,e.SERVICE_UNAVAILABLE,void 0,t)}const n=await s.json();if(!n.uuid)return this.handleModelError(\"Share response did not contain a URL\",e.SERVICE_UNAVAILABLE);const o=`https://claude.ai/share/${n.uuid}`;return a&&this.currentThread&&this.currentThread.metadata&&(this.currentThread.metadata.shareUrl=o,await this.saveThread()),o}catch(t){return this.handleModelError(\"Error sharing conversation\",e.SERVICE_UNAVAILABLE,void 0,t)}}findStyleByKey(e,t){if(e&&Array.isArray(e.defaultStyles))for(let r=0;r<e.defaultStyles.length;r++)if(e.defaultStyles[r]&&e.defaultStyles[r].key===t)return e.defaultStyles[r];if(e&&Array.isArray(e.customStyles))for(let r=0;r<e.customStyles.length;r++)if(e.customStyles[r]&&e.customStyles[r].key===t)return e.customStyles[r]}async getStyles(t){t||(t=await this.getOrganizationId());const r=await fetch(`https://claude.ai/api/organizations/${t}/list_styles`,{method:\"GET\",headers:this.getHeaders(),credentials:\"include\"});return r.ok?await r.json():this.handleModelError(`Failed to get styles: ${r.status}`,e.SERVICE_UNAVAILABLE,void 0,await r.text())}async getConversationData(t){try{let r,a=!1!==t?.loadThread;if(t?.metadata&&(a=!1),a)await this.ensureThreadLoaded(),r=this.getClaudeMetadata();else{if(!t?.metadata)return this.handleModelError(\"No thread loaded and no metadata provided for getting conversation\",e.INVALID_REQUEST);if(r=t.metadata,!r.organizationId||!r.conversationId)return this.handleModelError(\"Invalid metadata provided for getting conversation\",e.INVALID_REQUEST)}const s=await fetch(`https://claude.ai/api/organizations/${r.organizationId}/chat_conversations/${r.conversationId}`,{method:\"GET\",headers:this.getHeaders(),credentials:\"include\"});return s.ok?await s.json():this.handleModelError(`Failed to get conversation: ${s.status}`,e.SERVICE_UNAVAILABLE,void 0,await s.text())}catch(t){return this.handleModelError(\"Error getting conversation\",e.SERVICE_UNAVAILABLE,void 0,t)}}async getAllConversationsData(t){t||(t=await this.getOrganizationId());try{const r=await fetch(`https://claude.ai/api/organizations/${t}/chat_conversations`,{method:\"GET\",headers:this.getHeaders(),credentials:\"include\",redirect:\"error\",cache:\"no-cache\"});return r.ok?await r.json():this.handleModelError(`Failed to get conversations data: ${r.status}`,e.SERVICE_UNAVAILABLE,void 0,await r.text())}catch(t){return this.handleModelError(\"Error getting conversations\",e.SERVICE_UNAVAILABLE,void 0,t)}}async getOrganizationData(t){t||(t=await this.getOrganizationId());try{const r=await fetch(`https://claude.ai/api/organizations/${t}`,{method:\"GET\",headers:this.getHeaders(),credentials:\"include\"});return r.ok?await r.json():this.handleModelError(`Failed to get organization data: ${r.status}`,e.SERVICE_UNAVAILABLE,void 0,await r.text())}catch(t){return this.handleModelError(\"Error getting organization\",e.SERVICE_UNAVAILABLE,void 0,t)}}async getAllOrganizationsData(){try{const t=await fetch(\"https://claude.ai/api/organizations\",{method:\"GET\",headers:this.getHeaders(),credentials:\"include\",redirect:\"error\",cache:\"no-cache\"});return 403===t.status?this.handleModelError(\"There is no logged-in Claude account in this browser.\",e.UNAUTHORIZED):t.ok?await t.json():this.handleModelError(`Failed to get organization data: ${t.status}`,e.SERVICE_UNAVAILABLE,void 0,await t.text())}catch(t){return this.handleModelError(\"Error getting organization\",e.SERVICE_UNAVAILABLE,void 0,t)}}async getOrganizationId(){if(this.organizationId)return this.organizationId;try{const t=await this.getAllOrganizationsData();return t&&t.length?(this.organizationId=t[0].uuid,this.organizationId):this.handleModelError(\"No organizations found for Claude account\",e.UNAUTHORIZED)}catch(t){this.handleModelError(\"Claude webapp not available in your country or region\",e.SERVICE_UNAVAILABLE,void 0,t)}}}function ve(e,t,r){return r}I([_e,b(\"design:type\",Function),b(\"design:paramtypes\",[String,Object]),b(\"design:returntype\",Promise)],we.prototype,\"editTitle\",null),I([_e,b(\"design:type\",Function),b(\"design:paramtypes\",[Array,Boolean,Boolean,Object]),b(\"design:returntype\",Promise)],we.prototype,\"deleteServerThreads\",null),I([_e,b(\"design:type\",Function),b(\"design:paramtypes\",[Object]),b(\"design:returntype\",Promise)],we.prototype,\"shareConversation\",null),I([_e,b(\"design:type\",Function),b(\"design:paramtypes\",[String]),b(\"design:returntype\",Promise)],we.prototype,\"getStyles\",null),I([_e,b(\"design:type\",Function),b(\"design:paramtypes\",[Object]),b(\"design:returntype\",Promise)],we.prototype,\"getConversationData\",null),I([_e,b(\"design:type\",Function),b(\"design:paramtypes\",[String]),b(\"design:returntype\",Promise)],we.prototype,\"getAllConversationsData\",null),I([_e,b(\"design:type\",Function),b(\"design:paramtypes\",[String]),b(\"design:returntype\",Promise)],we.prototype,\"getOrganizationData\",null),I([_e,b(\"design:type\",Function),b(\"design:paramtypes\",[]),b(\"design:returntype\",Promise)],we.prototype,\"getAllOrganizationsData\",null),I([_e,b(\"design:type\",Function),b(\"design:paramtypes\",[]),b(\"design:returntype\",Promise)],we.prototype,\"getOrganizationId\",null);class Se extends h{constructor(e={}){super(),this.sessionKey=e.sessionKey,this.baseUrl=\"https://www.perplexity.ai\",this.visitorId=c(),this.models={\"Perplexity Sonar\":[\"turbo\",\"concise\",\"non-reasoning\",\"non-pro\"],\"Perplexity Pro Auto\":[\"pplx_pro\",\"copilot\",\"non-reasoning\",\"pro-limited\"],\"Perplexity Sonar Pro\":[\"experimental\",\"copilot\",\"non-reasoning\",\"pro-account\"],\"GPT-4.1\":[\"gpt4o\",\"copilot\",\"non-reasoning\",\"pro-account\"],\"Claude 3.7 Sonnet\":[\"claude2\",\"copilot\",\"non-reasoning\",\"pro-account\"],\"Gemini 2.5 Pro\":[\"gemini2flash\",\"copilot\",\"non-reasoning\",\"pro-account\"],\"Grok 3 Beta\":[\"grok\",\"copilot\",\"non-reasoning\",\"pro-account\"],\"Perplexity R1 1776\":[\"r1\",\"copilot\",\"reasoning\",\"pro-account\"],\"GPT-o4-mini\":[\"o3mini\",\"copilot\",\"reasoning\",\"pro-account\"],\"Claude 3.7 Sonnet Thinking\":[\"claude37sonnetthinking\",\"copilot\",\"reasoning\",\"pro-account\"],\"Perplexity Deep Research\":[\"pplx_alpha\",\"copilot\",\"reasoning\",\"pro-limited\"]},this.defaultModel=\"Perplexity Sonar\",this.initializeStorage().catch(console.error)}async initializeStorage(){(await this.getAllThreads()).length||await this.saveThreadsToStorage([]),await this.validateExistingThreads()}async validateExistingThreads(){const e=await this.getAllThreads();let t=!1;for(const r of e)r.modelName!==this.getName()||this.isValidPerplexityMetadata(r.metadata)||(await this.deleteThread(r.id),t=!0);t&&await this.saveThreadsToStorage(e.filter((e=>e.modelName!==this.getName()||this.isValidPerplexityMetadata(e.metadata))))}isValidPerplexityMetadata(e){return e?.conversationId}getName(){return\"Perplexity Web\"}getModels(){return this.models}getSearchSources(){return[\"web\",\"scholar\",\"social\"]}supportsImageInput(){return!0}async uploadImage(t){try{const r=`${this.baseUrl}/rest/uploads/create_upload_url`,a={filename:t.name,content_type:t.type,source:\"default\",file_size:t.size,force_image:!1};if(!this.csrfToken&&(await this.checkAuth(),!this.csrfToken))return this.handleModelError(\"Failed to obtain CSRF token for upload\",e.UNAUTHORIZED);const s=await ge(r,{method:\"POST\",headers:this.getHeaders(!0),body:JSON.stringify(a)});if(!s||!s.s3_bucket_url||!s.fields)return this.handleModelError(\"Failed to get upload parameters from Perplexity\",e.UPLOAD_FAILED);const n=s.s3_bucket_url,o=new FormData;for(const e in s.fields)o.append(e,s.fields[e]);o.append(\"file\",t);const i=await ge(n,{method:\"POST\",body:o});return i&&i.secure_url?(console.log(\"Image uploaded successfully:\",i.secure_url),i.secure_url):this.handleModelError(\"Failed to upload image to Cloudinary or parse response\",e.UPLOAD_FAILED)}catch(t){return console.error(\"Perplexity image upload error:\",t),this.handleModelError(`Image upload failed: ${t instanceof Error?t.message:String(t)}`,e.UPLOAD_FAILED,void 0,t)}}getHeaders(e=!1){const t={\"Content-Type\":\"application/json\",Accept:\"application/json\",\"User-Agent\":\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36\"};return this.sessionKey&&(t.Cookie=`__Secure-next-auth.session-token=${this.sessionKey}`),e&&this.csrfToken&&(t[\"x-csrf-token\"]=this.csrfToken),t}async checkAuth(){try{if(!await E(`${this.baseUrl}/`))return this.handleModelError(`Missing ${this.baseUrl} permission`,e.MISSING_HOST_PERMISSION);const t=await ge(`${this.baseUrl}/api/auth/csrf`,{method:\"GET\",headers:this.getHeaders()});t&&t.csrfToken&&(this.csrfToken=t.csrfToken);const r=await ge(`${this.baseUrl}/api/auth/session`,{method:\"GET\",headers:this.getHeaders()});if(r&&r.user){this.userInfo={id:r.user.id,username:r.user.name,image:r.user.image,subscriptionStatus:r.user.subscription_status||\"unknown\"};try{const e=await ge(`${this.baseUrl}/rest/user/settings`,{method:\"GET\",headers:this.getHeaders(!0)});e&&(this.userSettings=e)}catch(e){console.warn(\"Failed to get user settings:\",e)}return this.userInfo}return null}catch(t){return t instanceof ae&&401===t.status?null:this.handleModelError(\"Failed to check authentication with Perplexity\",e.SERVICE_UNAVAILABLE,void 0,t)}}async checkRateLimit(){try{return(await ge(`${this.baseUrl}/rest/rate-limit`,{method:\"GET\",headers:this.getHeaders(!0)})).remaining}catch(t){return this.handleModelError(\"Failed to check rate limit\",e.SERVICE_UNAVAILABLE,void 0,t)}}async getRecentThreads(){try{return(await ge(`${this.baseUrl}/rest/thread/list_recent`,{method:\"GET\",headers:this.getHeaders(!0)})).entries||[]}catch(t){return this.handleModelError(\"Failed to get recent threads\",e.SERVICE_UNAVAILABLE,void 0,t)}}async ensureThreadLoaded(){if(!this.currentThread){const e=(await this.getAllThreads()).filter((e=>e.modelName===this.getName()&&this.isValidPerplexityMetadata(e.metadata)));if(e.length>0){const t=e.sort(((e,t)=>t.updatedAt-e.updatedAt))[0];this.currentThread=t,console.log(\"Loaded existing thread from storage:\",this.currentThread.id)}else await this.initNewThread()}}getPerplexityMetadata(){const t=this.getCurrentThreadSafe();if(!t.metadata)return this.handleModelError(\"No thread metadata available\",e.INVALID_REQUEST);const r=t.metadata;return r.conversationId?r:this.handleModelError(\"Invalid thread metadata\",e.INVALID_REQUEST)}getCurrentThreadSafe(){return this.currentThread?this.currentThread:this.handleModelError(\"No active thread\",e.INVALID_REQUEST)}async initNewThread(){try{await this.checkAuth();const e=c(),t=c();this.currentThread={id:e,title:\"New Conversation\",modelName:this.getName(),messages:[],createdAt:Date.now(),updatedAt:Date.now(),metadata:{conversationId:e,frontendContextUuid:t}},await this.saveThread()}catch(t){return this.handleModelError(\"Error initializing new thread\",e.METADATA_INITIALIZATION_ERROR,void 0,t)}}async loadThread(t){const r=(await this.getAllThreads()).find((e=>e.id===t));if(!r||r.modelName!==this.getName())return this.handleModelError(`Thread ${t} not found`,e.INVALID_THREAD_ID);this.currentThread=r}async doSendMessage(t){let r=[],a=this.defaultModel;t.model?this.models[t.model]?a=t.model:console.warn(`Invalid model \"${t.model}\" provided. Using default model \"${a}\" instead.`):console.log(`No model provided, using default model \"${a}\".`);let s=\"internet\";t.searchFocus?\"internet\"===t.searchFocus||\"writing\"===t.searchFocus?s=t.searchFocus:console.warn(`Invalid search focus \"${t.searchFocus}\" provided. Using default mode \"internet\" instead.`):console.log('No search focus provided, using default \"internet\" mode'),\"internet\"===t.searchFocus?t.searchSources?t.searchSources.every((e=>this.getSearchSources().includes(e)))?r=t.searchSources:console.warn(`Invalid search source(s) \"${t.searchSources}\" provided. Using default source \"web\" instead.`):(console.log('No search source provided, using default source \"web\".'),r=[\"web\"]):(t.searchSources||\"\"!=t.searchSources)&&console.warn(`Invalid search source(s) \"${t.searchSources}\" provided for a no internet search response. Ignoring search sources.`);try{t.onEvent({type:\"UPDATE_ANSWER\",data:{text:\"\"}}),await this.ensureThreadLoaded();const n=this.getCurrentThreadSafe();let o=[];if(t.images&&t.images.length>0){if(t.images.length>4)return this.handleModelError(\"Maximum of 4 images allowed per message.\",e.UPLOAD_AMOUNT_EXCEEDED,t);for(const r of t.images)try{const e=await this.uploadImage(r);o.push(e)}catch(a){return this.handleModelError(`Failed to upload image: ${r.name}`,e.UPLOAD_FAILED,t,a)}}const i=this.createMessage(\"user\",t.prompt);let d;o.length>0&&(i.metadata={...i.metadata||{},attachmentUrls:o}),n.messages.push(i);try{d=this.getPerplexityMetadata()}catch(r){if(!this.currentThread?.metadata)return this.handleModelError(\"Failed to initialize thread metadata\",e.METADATA_INITIALIZATION_ERROR,t,r);d=this.currentThread.metadata}const l=await this.checkAuth();if(await this.checkRateLimit()<=0)return this.handleModelError(\"You have reached your rate limit for Perplexity queries\",e.RATE_LIMIT_EXCEEDED);const h=c(),u=n.messages.length>1&&d.backendUuid&&d.readWriteToken,g={attachments:o,browser_history_summary:[],client_coordinates:null,frontend_uuid:h,is_incognito:!1,is_nav_suggestions_disabled:!1,is_related_query:!1,is_sponsored:!1,language:navigator.language||\"en-US\",mode:this.models[a][1],model_preference:this.models[a][0],prompt_source:\"user\",search_focus:s,search_recency_filter:null,send_back_text_in_streaming_api:!1,sources:r,supported_block_use_cases:[\"answer_modes\",\"media_items\",\"knowledge_cards\",\"inline_entity_cards\",\"place_widgets\",\"finance_widgets\",\"sports_widgets\",\"shopping_widgets\",\"jobs_widgets\",\"search_result_widgets\",\"entity_list_answer\",\"todo_list\"],timezone:Intl.DateTimeFormat().resolvedOptions().timeZone,use_schematized_api:!0,user_nextauth_id:l?.id,version:\"2.18\",visitor_id:this.visitorId};let m;u?(m={last_backend_uuid:d.backendUuid,read_write_token:d.readWriteToken,query_source:\"followup\"},console.log(\"Sending follow-up request with backendUuid:\",d.backendUuid)):(m={frontend_context_uuid:d.frontendContextUuid||c(),query_source:\"home\"},console.log(\"Sending first request with frontendContextUuid:\",m.frontend_context_uuid));const p={params:{...g,...m},query_str:t.prompt},f=await fetch(`${this.baseUrl}/rest/sse/perplexity_ask`,{method:\"POST\",headers:this.getHeaders(!0),body:JSON.stringify(p),signal:t.signal});if(!f.ok){const r=await f.text();return 429===f.status?this.handleModelError(\"Perplexity rate limit exceeded. Please try again later.\",e.RATE_LIMIT_EXCEEDED,t):this.handleModelError(`Perplexity API error: ${f.status} - ${r.substring(0,200)}`,e.SERVICE_UNAVAILABLE,t)}if(!f.body)return this.handleModelError(\"Response body is null\",e.SERVICE_UNAVAILABLE,t);const E=f.body.getReader(),A=new TextDecoder;let y=\"\",T=\"\",_=d.threadUrlSlug||\"\",w=d.backendUuid||\"\",v=d.contextUuid||\"\",S=d.readWriteToken||\"\";for(;;){const{done:e,value:r}=await E.read();if(e)break;const a=A.decode(r,{stream:!0});let s;for(y+=a,console.log(\"Perplexity chunk received:\",a.length);(s=y.indexOf(\"\\n\"))>=0;){const e=y.slice(0,s).trim();if(y=y.slice(s+1),e&&e.startsWith(\"event:\")){const r=e.substring(6).trim();let a=y.indexOf(\"\\n\");if(!(a>=0)){y=e+\"\\n\"+y;break}{const e=y.slice(0,a).trim();if(y=y.slice(a+1),e.startsWith(\"data:\")){const a=e.substring(5).trim();if(\"end_of_stream\"===r){console.log(\"Perplexity end_of_stream received\");continue}if(\"message\"===r)try{const e=JSON.parse(a);let r=!1;if(e.thread_url_slug&&!_&&(_=e.thread_url_slug,d.threadUrlSlug=_),e.backend_uuid&&!w&&(w=e.backend_uuid,d.backendUuid=w),e.context_uuid&&(v=e.context_uuid,d.contextUuid=v),e.blocks&&Array.isArray(e.blocks))for(const t of e.blocks)if(\"ask_text\"===t.intended_usage&&t.markdown_block){const e=t.markdown_block;Array.isArray(e.chunks)&&1==e.chunks.length&&(T+=e.chunks[0],r=!0)}r&&(t.onEvent({type:\"UPDATE_ANSWER\",data:{text:T}}),console.log(\"Perplexity text update:\",T.length)),!0!==e.final&&!0!==e.final_sse_message||(e.thread_title&&e.thread_title!==n.title&&(n.title=e.thread_title,t.onEvent({type:\"TITLE_UPDATE\",data:{title:e.thread_title,threadId:n.id}})),e.read_write_token&&(S=e.read_write_token,d.readWriteToken=S),e.related_queries&&Array.isArray(e.related_queries)&&t.onEvent({type:\"SUGGESTED_RESPONSES\",data:{suggestions:e.related_queries.map((e=>\"string\"==typeof e?e:e.text)).filter(Boolean)}}))}catch(e){console.warn(\"Error parsing Perplexity message data JSON:\",a,e)}}}}}}console.log(\"Perplexity stream finished, final text length:\",T.length);const I=this.createMessage(\"assistant\",T);n.messages.push(I),n.updatedAt=Date.now(),await this.saveThread(),t.onEvent({type:\"DONE\",data:{threadId:n.id}})}catch(r){return this.handleModelError(\"Error sending message\",e.SERVICE_UNAVAILABLE,t,r)}}async getModelVersion(){try{const t=await ge(`${this.baseUrl}/rest/version`,{method:\"GET\",headers:this.getHeaders(!0)});return t&&t.version?t.version:this.handleModelError(\"Invalid response from Perplexity version endpoint\",e.SERVICE_UNAVAILABLE)}catch(t){return this.handleModelError(\"Error fetching Perplexity version\",e.SERVICE_UNAVAILABLE,void 0,t)}}async deleteServerThreads(t,r=!0,a=!0){try{if(!await this.checkAuth())return this.handleModelError(\"You must be logged in to delete conversations\",e.UNAUTHORIZED);const s=await this.getAllThreads();for(const n of t)try{const t=s.find((e=>e.id===n));if(!t){console.warn(`Thread ${n} not found in storage.`);continue}if(t.modelName!==this.getName()){console.warn(`Thread ${n} has incorrect model name: ${t.modelName}`);continue}const o=t.metadata;if(!o||!o.backendUuid||!o.readWriteToken){r?(console.warn(`Thread ${n} has incomplete metadata. Cannot delete from server, deleting only from local storage.`),await this.deleteThread(n)):console.warn(`Thread ${n} has incomplete metadata. Cannot delete from server, skipping thread.`);continue}let i=await ge(`${this.baseUrl}/rest/thread/delete_thread_by_entry_uuid`,{method:\"DELETE\",headers:this.getHeaders(!0),body:JSON.stringify({entry_uuid:o.backendUuid,read_write_token:o.readWriteToken})});if(!i||\"success\"!==i.status)return this.handleModelError(`Failed to delete thread ${n} from Perplexity`,e.SERVICE_UNAVAILABLE,void 0,i?.detail||\"Unknown error\");r&&await this.deleteThread(n,a)}catch(e){console.error(`Error deleting thread ${n}:`,e)}}catch(t){return this.handleModelError(\"Error deleting conversations\",e.SERVICE_UNAVAILABLE,void 0,t)}}async shareConversation(){try{await this.ensureThreadLoaded();const t=this.getPerplexityMetadata();if(!await this.checkAuth())return this.handleModelError(\"You must be logged in to share conversations\",e.UNAUTHORIZED);if(!t.contextUuid||!t.threadUrlSlug)return this.handleModelError(\"This conversation cannot be shared\",e.FEATURE_NOT_SUPPORTED);const r=await ge(`${this.baseUrl}/rest/thread/update_thread_access`,{method:\"POST\",headers:this.getHeaders(!0),body:JSON.stringify({context_uuid:t.contextUuid,updated_access:2,read_write_token:t.readWriteToken})});if(\"success\"!==r.status||2!==r.access)return this.handleModelError(\"Failed to make conversation shareable\",e.SERVICE_UNAVAILABLE);return`${this.baseUrl}/search/${t.threadUrlSlug}`}catch(t){return this.handleModelError(\"Error sharing conversation\",e.SERVICE_UNAVAILABLE,void 0,t)}}async unShareConversation(){try{await this.ensureThreadLoaded();const t=this.getPerplexityMetadata();if(!await this.checkAuth())return this.handleModelError(\"You must be logged in to change conversation visibility\",e.UNAUTHORIZED);if(!t.contextUuid)return this.handleModelError(\"This conversation cannot be modified\",e.FEATURE_NOT_SUPPORTED);const r=await ge(`${this.baseUrl}/rest/thread/update_thread_access`,{method:\"POST\",headers:this.getHeaders(!0),body:JSON.stringify({context_uuid:t.contextUuid,updated_access:1,read_write_token:t.readWriteToken})});return\"success\"===r.status&&1===r.access}catch(t){return this.handleModelError(\"Error setting conversation to private\",e.SERVICE_UNAVAILABLE,void 0,t)}}async editTitle(t){try{await this.ensureThreadLoaded();if(!await this.checkAuth())return this.handleModelError(\"You must be logged in to edit conversation titles\",e.UNAUTHORIZED);const r=this.getPerplexityMetadata();if(!r.contextUuid)return void(this.currentThread&&(this.currentThread.title=t,await this.saveThread()));await ge(`${this.baseUrl}/rest/thread/set_thread_title`,{method:\"POST\",headers:this.getHeaders(!0),body:JSON.stringify({context_uuid:r.contextUuid,title:t,read_write_token:r.contextUuid})}),this.currentThread&&(this.currentThread.title=t,await this.saveThread())}catch(t){return this.handleModelError(\"Error updating conversation title\",e.SERVICE_UNAVAILABLE,void 0,t)}}}I([ve,b(\"design:type\",Function),b(\"design:paramtypes\",[File]),b(\"design:returntype\",Promise)],Se.prototype,\"uploadImage\",null),I([ve,b(\"design:type\",Function),b(\"design:paramtypes\",[]),b(\"design:returntype\",Promise)],Se.prototype,\"checkAuth\",null),I([ve,b(\"design:type\",Function),b(\"design:paramtypes\",[]),b(\"design:returntype\",Promise)],Se.prototype,\"checkRateLimit\",null),I([ve,b(\"design:type\",Function),b(\"design:paramtypes\",[]),b(\"design:returntype\",Promise)],Se.prototype,\"getRecentThreads\",null),I([ve,b(\"design:type\",Function),b(\"design:paramtypes\",[Array,Boolean,Boolean]),b(\"design:returntype\",Promise)],Se.prototype,\"deleteServerThreads\",null),I([ve,b(\"design:type\",Function),b(\"design:paramtypes\",[]),b(\"design:returntype\",Promise)],Se.prototype,\"shareConversation\",null),I([ve,b(\"design:type\",Function),b(\"design:paramtypes\",[]),b(\"design:returntype\",Promise)],Se.prototype,\"unShareConversation\",null),I([ve,b(\"design:type\",Function),b(\"design:paramtypes\",[String]),b(\"design:returntype\",Promise)],Se.prototype,\"editTitle\",null);let Ie=null,be=null,Re=null,Ne=new Uint8Array(0),xe=new DataView(new ArrayBuffer(0));function Me(){if(!be)throw new Error(\"WASM Memory not initialized for view update\");Ne.buffer!==be.buffer&&(Ne=new Uint8Array(be.buffer),xe=new DataView(be.buffer))}const Ue=new TextDecoder(\"utf-8\",{ignoreBOM:!0,fatal:!0}),De=new TextEncoder;function ke(e,t){return 0===t?\"\":(Me(),Ue.decode(Ne.subarray(e,e+t)))}let Pe=[void 0,null,!0,!1],Oe=Pe.length;function Le(e){Oe===Pe.length&&Pe.push(Pe.length+1);const t=Oe;return Oe=Pe[t],Pe[t]=e,t}function Ce(e){return Pe[e]}function $e(e){if(e<4)return;void 0!==Pe[e]&&(Pe[e]=Oe,Oe=e)}let Ve=0;function Fe(e,t){const r=De.encode(e),a=t(r.length,1)>>>0;return Me(),Ne.subarray(a,a+r.length).set(r),Ve=r.length,a}const We={__wbindgen_string_new:(e,t)=>Le(ke(e,t)),__wbindgen_object_clone_ref:e=>Le(Ce(e)),__wbindgen_object_drop_ref:$e,__wbindgen_throw:(e,t)=>{throw new Error(ke(e,t))},__wbindgen_number_new:e=>Le(e),__wbindgen_number_get:(e,t)=>{if(!be)throw new Error(\"WASM memory not available\");Me();const r=Ce(e);\"number\"==typeof r?(xe.setFloat64(t,r,!0),xe.setInt32(t+8,0,!0)):(xe.setFloat64(t,NaN,!0),xe.setInt32(t+8,1,!0))},__wbindgen_is_undefined:e=>void 0===Ce(e),__wbindgen_boolean_get:e=>{const t=Ce(e);return\"boolean\"==typeof t?t?1:0:2},__wbindgen_string_get:(e,t)=>{if(!be||!Ie?.__wbindgen_export_0)return console.error(\"WASM memory or malloc (__wbindgen_export_0) not available for __wbindgen_string_get\"),void(be&&(Me(),xe.setInt32(t,0,!0),xe.setInt32(t+4,0,!0)));Me();const r=Ce(e);if(\"string\"==typeof r){const e=Fe(r,Ie.__wbindgen_export_0),a=Ve;xe.setInt32(t,e,!0),xe.setInt32(t+4,a,!0)}else xe.setInt32(t,0,!0),xe.setInt32(t+4,0,!0)},__wbindgen_error_new:(e,t)=>Le(new Error(ke(e,t))),__wbindgen_jsval_loose_eq:(e,t)=>Ce(e)==Ce(t),crypto_getRandomValues:(e,t)=>{Me(),crypto.getRandomValues(Ne.subarray(e,e+t))},performance_now:()=>performance.now(),__wbindgen_thread_destroy:()=>{console.warn(\"STUB: __wbindgen_thread_destroy called\")},__wbindgen_current_thread_destroy:()=>{console.warn(\"STUB: __wbindgen_current_thread_destroy called\")},__wbindgen_thread_spawn:e=>(console.warn(\"STUB: __wbindgen_thread_spawn called with ptr:\",e),0),__wbindgen_cb_drop:e=>\"function\"==typeof Ce(e)&&($e(e),!0)};async function ze(e){if(console.log(\"Starting PoW solve using WASM (Ported JS Logic):\",e),\"DeepSeekHashV1\"!==e.algorithm)throw new Error(\"Unsupported PoW algorithm: \"+e.algorithm);if(\"number\"!=typeof e.difficulty)throw new Error(\"Missing difficulty in challenge object\");if(\"number\"!=typeof e.expire_at)throw new Error(\"Missing expire_at in challenge object\");const t=await async function(){return Re||(Re=(async()=>{if(Ie)return Ie;const e=s.runtime.getURL(\"assets/sha3_wasm_bg.7b9ca65ddd.wasm\");console.log(\"Initializing DeepSeek WASM from:\",e);try{const t=fetch(e);be=new WebAssembly.Memory({initial:17,maximum:16384,shared:!1}),Me();const r={env:{...We,memory:be}};console.log(\"Attempting WASM instantiation with imports:\",Object.keys(r.env));const{instance:a}=await WebAssembly.instantiateStreaming(t,r);return a.exports.memory instanceof WebAssembly.Memory&&a.exports.memory!==be?(console.log(\"WASM module exported its own memory instance. Updating reference.\"),be=a.exports.memory,Me()):console.log(\"WASM using provided memory instance.\"),Ie=a.exports,console.log(\"DeepSeek WASM module loaded and initialized successfully.\"),Pe=[void 0,null,!0,!1],Oe=Pe.length,Ie}catch(e){throw console.error(\"Failed to load or instantiate WASM module:\",e),be=null,Ie=null,Re=null,Pe=[void 0,null,!0,!1],Oe=Pe.length,e}})(),Re)}();if(!t||!be)throw new Error(\"WASM module is not initialized.\");const{__wbindgen_export_0:r,__wbindgen_export_1:a,__wbindgen_add_to_stack_pointer:n,wasm_solve:o}=t;if(!(r&&a&&n&&o))throw console.error(\"Available WASM exports:\",Object.keys(t)),new Error(\"Required WASM exports not found after loading.\");Me();const{challenge:i,salt:d,difficulty:l,signature:c,target_path:h,expire_at:u}=e,g=i,m=`${d}_${u}_`;let p=0,f=0,E=0,A=0,y=0,T=!1,_=null;try{if(p=Fe(g,r),E=Ve,f=Fe(m,r),A=Ve,0===p||0===f){if(a){if(0!==p)try{a(p,E,1,1)}catch(e){}if(0!==f)try{a(f,A,1,1)}catch(e){}}throw new Error(`WASM malloc failed. Pointers: C=${p}, P=${f}`)}if(y=n(-16),y<=0)throw new Error(`WASM failed to adjust stack pointer correctly (returned ${y}).`);y%8!=0?console.warn(`Result stack pointer ${y} is not 8-byte aligned. Reading f64 might be problematic.`):y%4!=0&&console.warn(`Result stack pointer ${y} is not 4-byte aligned. Reading i32 might be problematic.`),console.log(`Calling wasm_solve with:\\n          arg0 (resultStackPtr): ${y}\\n          arg1 (challengePtr):   ${p} (len: ${E}) -> \"${g}\"\\n          arg2 (challengeLen):   ${E}\\n          arg3 (prefixPtr):      ${f} (len: ${A}) -> \"${m}\"\\n          arg4 (prefixLen):      ${A}\\n          arg5 (difficulty):     ${Number(l)}`);const t=performance.now();o(y,p,E,f,A,Number(l));const s=performance.now();console.log(`wasm_solve execution time: ${s-t} ms`);const u=(w=y,Me(),w<=0||w+4>xe.byteLength?(console.error(`readInt32FromMemory: Invalid pointer ${w}. Buffer size: ${xe.byteLength}`),0):(w%4!=0&&console.warn(`readInt32FromMemory: Pointer ${w} is not 4-byte aligned.`),xe.getInt32(w,!0)));if(console.log(`WASM solve completed. Read status from stack [${y}]: ${u}`),0===u)throw console.error(\"WASM solve function indicated failure (status code 0).\"),_=null,new Error(\"WASM solver failed internally (returned status 0).\");{const e=function(e){return Me(),e<=0||e+8>xe.byteLength?(console.error(`readFloat64FromMemory: Invalid pointer ${e}. Buffer size: ${xe.byteLength}`),NaN):(e%8!=0&&console.warn(`readFloat64FromMemory: Pointer ${e} is not 8-byte aligned.`),xe.getFloat64(e,!0))}(y+8);console.log(`WASM solve success (status ${u}). Read f64 nonce from stack [${y+8}]: ${e}`),_=Math.floor(e)}if(console.log(`Final calculated nonce: ${_}`),n(16),T=!0,null===_||\"number\"!=typeof _||isNaN(_))throw new Error(`PoW solver did not produce a valid number answer (got: ${_}).`);const v={algorithm:e.algorithm,challenge:i,salt:d,answer:_,signature:c,target_path:h||\"/api/v0/chat/completion\"},S=JSON.stringify(v);return btoa(S)}catch(e){if(console.error(\"Error caught during WASM PoW solve:\",e),n&&y>0&&!T)try{n(16),console.log(\"Restored stack pointer in error handler.\")}catch(e){console.error(\"Error restoring stack pointer after error:\",e)}throw e}var w}function Be(e,t,r){return r}class qe extends h{constructor(){super(),this.DEEPSEEK_HOST=\"https://chat.deepseek.com\",this.initialize().catch((e=>{console.error(\"Failed to initialize DeepseekWebModel:\",e)}))}async initialize(){console.log(\"Initializing DeepSeek model...\");try{const e=await S(\"Deepseek\",this.DEEPSEEK_HOST,`${this.DEEPSEEK_HOST}/*`,\"deepseekExtractor\",!1);e?(this.authToken=`Bearer ${e}`,console.log(\"[Deepseek Init] Auth token loaded.\")):console.warn(\"[Deepseek Init] Auth token not found during initial load.\"),await this.initializeStorage(),console.log(\"DeepSeek model ready.\")}catch(t){this.handleModelError(\"Deepseek Initialization failed\",e.METADATA_INITIALIZATION_ERROR,void 0,t)}}async ensureAuthToken(){if(!this.authToken){console.log(\"[Deepseek ensureAuthToken] Auth token missing, attempting to retrieve...\");const t=await S(\"Deepseek\",this.DEEPSEEK_HOST,`${this.DEEPSEEK_HOST}/*`,\"deepseekExtractor\",!0);if(!t)return console.error(\"[Deepseek ensureAuthToken] Failed to retrieve auth token even after forcing.\"),this.handleModelError(\"DeepSeek authentication token is missing. Please log in to https://chat.deepseek.com.\",e.UNAUTHORIZED);this.authToken=`Bearer ${t}`}return this.authToken}async initializeStorage(){const e=await this.getAllThreads();e?0===e.length&&await this.saveThreadsToStorage([]):(console.warn(\"Could not retrieve threads from storage, starting fresh.\"),await this.saveThreadsToStorage([])),await this.validateExistingThreads()}async validateExistingThreads(){const e=await this.getAllThreads();if(!e)return;let t=!1;const r=[];for(const a of e)a.modelName===this.getName()?this.isValidDeepseekMetadata(a.metadata)?r.push(a):(console.warn(`Removing Deepseek thread ${a.id} due to invalid metadata.`),t=!0):r.push(a);t&&(await this.saveThreadsToStorage(r),console.log(\"Removed threads with invalid metadata.\"))}isValidDeepseekMetadata(e){return\"string\"==typeof e?.conversationId&&e.conversationId.length>0}getDeepseekMetadata(){const t=this.getCurrentThreadSafe();return t.metadata&&this.isValidDeepseekMetadata(t.metadata)?t.metadata:this.handleModelError(\"Invalid or missing Deepseek thread metadata.\",e.INVALID_METADATA)}async ensureThreadLoaded(){if(!this.currentThread){const e=(await this.getAllThreads()).filter((e=>e.modelName===this.getName()&&this.isValidDeepseekMetadata(e.metadata)));if(e.length>0){const t=e.sort(((e,t)=>t.updatedAt-e.updatedAt))[0];this.currentThread=t,console.log(\"Loaded existing DeepSeek thread from storage:\",this.currentThread.id)}else await this.initNewThread()}}getCurrentThreadSafe(){return this.currentThread?this.currentThread:this.handleModelError(\"No active thread\",e.INVALID_REQUEST)}getName(){return\"DeepSeek Web\"}supportsImageInput(){return!1}async getHeaders(t=!0){let r={},a=\"x86\",n=\"Windows\",o=\"64\";try{const e=await s.runtime.getPlatformInfo();a=e.arch||a,n=\"win\"===e.os?\"Windows\":e.os,o=\"x86-64\"===e.arch?\"64\":\"32\"}catch(e){}try{const e=navigator.userAgent,t=navigator.language||\"en-US\";r={Accept:\"*/*\",\"Accept-Encoding\":\"gzip, deflate, br, zstd\",\"Accept-Language\":navigator.languages?navigator.languages.join(\",\"):t,\"Cache-Control\":\"no-cache\",\"Content-Type\":\"application/json\",DNT:\"1\",Priority:\"u=1, i\",\"Sec-CH-UA\":void 0!==navigator.userAgentData&&navigator.userAgentData.brands?navigator.userAgentData.brands.map((e=>`\"${e.brand}\";v=\"${e.version}\"`)).join(\", \"):'\"Google Chrome\";v=\"135\", \"Not-A.Brand\";v=\"8\", \"Chromium\";v=\"135\"',\"Sec-CH-UA-Arch\":`\"${a}\"`,\"Sec-CH-UA-Bitness\":`\"${o}\"`,\"Sec-CH-UA-Full-Version\":void 0!==navigator.userAgentData&&navigator.userAgentData.uaFullVersion?`\"${navigator.userAgentData.uaFullVersion}\"`:'\"135.0.7049.116\"',\"Sec-CH-UA-Full-Version-List\":void 0!==navigator.userAgentData&&navigator.userAgentData.brands?navigator.userAgentData.brands.map((e=>`\"${e.brand}\";v=\"${e.version}\"`)).join(\", \"):'\"Google Chrome\";v=\"135.0.7049.116\", \"Not-A.Brand\";v=\"*******\", \"Chromium\";v=\"135.0.7049.116\"',\"Sec-CH-UA-Mobile\":void 0!==navigator.userAgentData&&navigator.userAgentData.mobile?\"?1\":\"?0\",\"Sec-CH-UA-Model\":'\"\"',\"Sec-CH-UA-Platform\":`\"${n}\"`,\"Sec-CH-UA-Platform-Version\":'\"19.0.0\"',\"Sec-Fetch-Dest\":\"empty\",\"Sec-Fetch-Mode\":\"cors\",\"Sec-Fetch-Site\":\"same-origin\",\"User-Agent\":e,\"X-App-Version\":`${(await this.getPlatformData())[0]}`,\"X-Client-Locale\":t.replace(\"-\",\"_\"),\"X-Client-Platform\":\"web\",\"X-Client-Version\":\"1.1.0-new-sse\"}}catch(t){this.handleModelError(\"Failed to get platform info during header creation\",e.SERVICE_UNAVAILABLE,void 0,t)}if(t){if(!this.authToken)return this.handleModelError(\"Authentication token is missing when creating headers.\",e.UNAUTHORIZED);r.Authorization=this.authToken}return r}async getPowSolutionForCompletion(t){const r=`${this.DEEPSEEK_HOST}/api/v0/chat/create_pow_challenge`,a={target_path:t||\"/api/v0/chat/completion\"};let s;try{const t=(await this.makeDeepseekRequest(r,{method:\"POST\",headers:await this.getHeaders(),body:a,responseType:\"json\"}))._data;if(0!==t?.code||0!==t?.data?.biz_code||!t?.data?.biz_data?.challenge)return this.handleModelError(\"Invalid PoW challenge response structure.\",e.RESPONSE_PARSING_ERROR,void 0,t);s=t.data.biz_data.challenge}catch(t){return this.handleModelError(\"Failed to fetch PoW challenge.\",e.SERVICE_UNAVAILABLE,void 0,t)}try{return await ze(s)}catch(t){return this.handleModelError(\"Failed to solve PoW challenge.\",e.POW_CHALLENGE_FAILED,void 0,t)}}async makeDeepseekRequest(t,r,a=1){try{if(!r.headers||!r.headers.Authorization){const e=await this.ensureAuthToken();r.headers={...r.headers,Authorization:e}}const e=await this.getHeaders(!1);r.headers={...e,...r.headers},console.debug(`Making Deepseek request: ${r.method||\"GET\"} ${t}`);const a=await ge.raw(t,r);return console.debug(`Deepseek request to ${t} successful.`),a}catch(r){if(r instanceof ae&&r.response){const a=r.response.status,s=r.response.text();try{const t=await s;if(403===a&&(t.includes(\"cf-challenge-running\")||t.includes(\"Cloudflare\")||t.includes(\"Checking if the site connection is secure\")))return this.handleModelError(\"Cloudflare challenge detected. Please ensure you can access https://chat.deepseek.com in your browser.\",e.NETWORK_ERROR,void 0,r)}catch(e){console.warn(`Error reading response body for ${t} after initial failure:`,e)}}return this.handleModelError(`Deepseek API request to ${t} failed`,e.NETWORK_ERROR,void 0,r)}}async getUserInfo(){const t=`${this.DEEPSEEK_HOST}/api/v0/users/current`;try{return(await this.makeDeepseekRequest(t,{method:\"GET\"}))._data}catch(t){return this.handleModelError(\"Failed to get user info\",e.NETWORK_ERROR,void 0,t)}}async getAllConversationsData(t=100){const r=`${this.DEEPSEEK_HOST}/api/v0/chat_session/fetch_page?count=${t}`;try{const t=await this.makeDeepseekRequest(r,{method:\"GET\"});if(0===t._data?.code&&0===t._data?.data?.biz_code&&t._data?.data?.biz_data?.chat_sessions)return t._data;{const r=t._data?.msg||t._data?.data?.biz_msg||\"Unknown error fetching conversations\";return this.handleModelError(`API returned unexpected structure or error: ${r}`,e.RESPONSE_PARSING_ERROR,void 0,t._data)}}catch(t){return this.handleModelError(\"Failed to get conversations data\",e.NETWORK_ERROR,void 0,t)}}async getPlatformData(){let e=null,t=null;const r=`${this.DEEPSEEK_HOST}/version.txt`,a=`${this.DEEPSEEK_HOST}/downloads/status.json`;try{const t=await fetch(r,{method:\"GET\"});t.ok?e=await t.text():console.warn(\"Failed to fetch DeepSeek version:\",t.status)}catch(e){console.warn(\"Failed to fetch DeepSeek version:\",e)}try{const e=await fetch(a,{method:\"GET\"});e.ok?t=await e.json():console.warn(\"Failed to fetch DeepSeek status:\",e.status)}catch(e){console.warn(\"Failed to fetch DeepSeek status:\",e)}return[e,t]}async createConversation(){console.log(\"Crefrerefefr..\");const t=`${this.DEEPSEEK_HOST}/api/v0/chat_session/create`;try{const r=(await this.makeDeepseekRequest(t,{method:\"POST\",body:{character_id:null}}))._data;if(0===r?.code&&0===r.data?.biz_code&&r.data?.biz_data?.id)return console.log(`Created new Deepseek conversation: ${r.data.biz_data.id}`),r.data.biz_data.id;{const t=r?.msg||r?.data?.biz_msg||\"Unknown error creating conversation\";return this.handleModelError(`Failed to create DeepSeek conversation: ${t}`,e.SERVICE_UNAVAILABLE,void 0,r)}}catch(t){return this.handleModelError(\"Failed to create conversation\",e.SERVICE_UNAVAILABLE,void 0,t)}}async initNewThread(){console.log(\"ffffffffffffff..\");try{await this.ensureAuthToken();const e=await this.createConversation();console.log(`Created new Deepseek conversation: ${e}`),this.currentThread={id:e,title:\"New DeepSeek Chat\",messages:[],createdAt:Date.now(),updatedAt:Date.now(),modelName:this.getName(),metadata:{conversationId:e,lastMessageId:null}},await this.saveThread(),console.log(`Initialized and saved new DeepSeek thread: ${this.currentThread.id} (Conv ID: ${e})`)}catch(t){return this.handleModelError(\"Failed to initialize new thread\",e.METADATA_INITIALIZATION_ERROR,void 0,t)}}async doSendMessage(r){let a,s=[];if(r.images&&r.images.length>0){if(r.images.length>4)return this.handleModelError(\"A maximum of 4 files can be uploaded at once.\",e.UPLOAD_AMOUNT_EXCEEDED,r);for(const e of r.images)try{const t=await this.uploadFile(e);s.push(t)}catch(e){return}}try{r.onEvent({type:\"UPDATE_ANSWER\",data:{text:\"\"}}),await this.ensureThreadLoaded(),a=this.getCurrentThreadSafe();const t=this.getDeepseekMetadata(),n=this.createMessage(\"user\",r.prompt);s.length>0&&(n.metadata={...n.metadata||{},uploadedFileIds:s}),a.messages.push(n),a.updatedAt=Date.now(),await this.saveThread();const o=await this.getPowSolutionForCompletion(\"/api/v0/chat/completion\"),i={chat_session_id:t.conversationId,parent_message_id:t.lastMessageId||null,prompt:r.prompt,ref_file_ids:s,thinking_enabled:\"reasoning\"===r.mode,search_enabled:!0===r.searchEnabled};if(i.search_enabled&&s.length>0)return this.handleModelError(\"search mode and files attachments can not be used together\",e.INVALID_REQUEST,r);const d=`${this.DEEPSEEK_HOST}/api/v0/chat/completion`,l=await this.makeDeepseekRequest(d,{method:\"POST\",headers:{...await this.getHeaders(),Accept:\"*/*\",\"x-ds-pow-response\":o},body:i,responseType:\"stream\",signal:r.signal});if(!l.body)return this.handleModelError(\"No response body received in stream.\",e.NETWORK_ERROR,r);const c=l.body.getReader(),h=new TextDecoder;let u=\"\";const g={text:\"\",reasoningContent:\"\",tokensUsed:0,title:void 0,updatedAt:void 0,messageId:void 0,parentId:void 0,model:void 0,role:\"ASSISTANT\",thinkingEnabled:void 0,banEdit:void 0,banRegenerate:void 0,status:void 0,files:[],tips:[],insertedAt:void 0,searchEnabled:void 0,searchStatus:void 0,searchResults:void 0,reasoningElapsedSecs:void 0};let m=!1,p=null;for(;!m;){const{done:e,value:t}=await c.read();if(e)break;let s;for(u+=h.decode(t,{stream:!0});-1!==(s=u.indexOf(\"\\n\\n\"));){const e=u.substring(0,s);u=u.substring(s+2);let t=null,n=null;const o=e.split(\"\\n\");for(const e of o)if(e.startsWith(\"event: \"))t=e.substring(7).trim();else if(e.startsWith(\"data: \")){const t=e.substring(5).trim();if(t&&\"[DONE]\"!==t)try{n=JSON.parse(t)}catch(e){continue}}if(\"title\"===t&&n?.content)g.title=n.content,r.onEvent({type:\"TITLE_UPDATE\",data:{title:g.title||\"\",threadId:a.id}});else if(\"update_session\"===t&&n?.updated_at)g.updatedAt=1e3*n.updated_at;else if(\"close\"===t)m=!0;else if(\"ready\"===t);else if(!t&&n?.v&&\"object\"==typeof n.v&&n.v.response){const e=n.v.response;g.messageId=e.message_id,g.parentId=e.parent_id,g.model=e.model,g.role=e.role,g.thinkingEnabled=e.thinking_enabled,g.banEdit=e.ban_edit,g.banRegenerate=e.ban_regenerate,g.status=e.status,g.files=e.files||[],g.tips=e.tips||[],g.insertedAt=1e3*e.inserted_at,g.searchEnabled=e.search_enabled,g.searchStatus=e.search_status,g.searchResults=e.search_results,g.tokensUsed=e.accumulated_token_usage,g.reasoningContent=e.thinking_content||\"\",g.reasoningElapsedSecs=e.thinking_elapsed_secs||0}else if(!t&&n?.v&&\"string\"==typeof n.v){const e=n.p,t=n.v;let a=!1;\"response/thinking_content\"===e?(p=\"reasoning\",g.reasoningContent=t,a=!0):\"response/content\"===e?(p=\"content\",g.text=t,a=!0):e||null==t?e&&\"response/status\"!==e&&console.warn(`Received string data with unhandled path '${e}':`,n):\"reasoning\"===p?(g.reasoningContent+=t,a=!0):\"content\"===p?(g.text+=t,a=!0):g.thinkingEnabled?(g.reasoningContent+=t,p=\"reasoning\",a=!0):(g.text+=t,p=\"content\",a=!0),a&&r.onEvent({type:\"UPDATE_ANSWER\",data:{text:g.text,reasoningContent:g.reasoningContent,reasoningElapsedSecs:g.reasoningElapsedSecs}})}else if(!t&&n?.v&&\"number\"==typeof n.v){const e=n.p,t=n.v;\"response/accumulated_token_usage\"===e?g.tokensUsed=t:\"response/thinking_elapsed_secs\"===e&&(g.reasoningElapsedSecs=t,r.onEvent({type:\"UPDATE_ANSWER\",data:{text:g.text,reasoningContent:g.reasoningContent,reasoningElapsedSecs:g.reasoningElapsedSecs}}))}else!t&&n?.v&&\"string\"==typeof n.v&&\"response/status\"===n.p&&(g.status=n.v,g.status)}}const f=this.createMessage(\"assistant\",g.text);g.reasoningContent&&g.reasoningContent.trim()&&(f.reasoningContent=g.reasoningContent.trim()),f.metadata={...f.metadata||{},responseTokens:g.tokensUsed,serverMessageId:g.messageId,serverParentId:g.parentId,modelUsed:g.model,finalStatus:g.status,reasoningTimeSecs:g.reasoningElapsedSecs,serverInsertedAt:g.insertedAt,thinkingEnabled:g.thinkingEnabled,searchEnabled:g.searchEnabled,searchStatus:g.searchStatus,searchResults:g.searchResults,banEdit:g.banEdit,banRegenerate:g.banRegenerate,files:g.files,tips:g.tips},Object.keys(f.metadata).forEach((e=>{void 0===f.metadata[e]&&delete f.metadata[e]})),a.messages.push(f),f.metadata?.serverMessageId&&(a.metadata||(a.metadata={conversationId:this.getDeepseekMetadata().conversationId}),a.metadata.lastMessageId=f.metadata.serverMessageId),g.title&&(a.title=g.title),g.updatedAt?a.updatedAt=g.updatedAt:a.updatedAt=Date.now(),await this.saveThread(),r.onEvent({type:\"DONE\",data:{threadId:a.id}})}catch(a){this.handleModelError(\"Error during message sending or processing\",a instanceof t?a.code:e.NETWORK_ERROR,r,a)}}async editTitle(t,r){try{let a,s=!1!==r?.loadThread,n=!1!==r?.tryUpdateThread;if(s)await this.ensureThreadLoaded(),a=this.getDeepseekMetadata();else{if(!r?.metadata)return this.handleModelError(\"No thread loaded and no metadata provided for title edit\",e.INVALID_REQUEST);if(a=r.metadata,!this.isValidDeepseekMetadata(a))return this.handleModelError(\"Invalid metadata provided for title edit\",e.INVALID_REQUEST)}const o=a.conversationId;if(!o)return this.handleModelError(\"Missing chat_session_id for title update\",e.INVALID_REQUEST);const i=`${this.DEEPSEEK_HOST}/api/v0/chat_session/update_title`,d={chat_session_id:o,title:t},l=(await this.makeDeepseekRequest(i,{method:\"POST\",headers:await this.getHeaders(),body:d,responseType:\"json\"}))._data;if(0!==l?.code||0!==l?.data?.biz_code){const t=l?.msg||l?.data?.biz_msg||\"Unknown error updating title\";return this.handleModelError(`Failed to update DeepSeek conversation title: ${t}`,e.SERVICE_UNAVAILABLE,void 0,l)}n&&this.currentThread&&(this.currentThread.title=t,await this.saveThread())}catch(t){return this.handleModelError(\"Error updating DeepSeek conversation title\",e.SERVICE_UNAVAILABLE,void 0,t)}}async deleteServerThreads(t,r=!0,a=!0){try{const s=await this.getAllThreads();for(const n of t){const t=s.find((e=>e.metadata&&e.metadata.conversationId===n));if(!t){console.warn(`[deleteServerThreads] Thread ${n} not found locally.`);continue}if(t.modelName!==this.getName()){console.warn(`[deleteServerThreads] Thread ${n} has incorrect model name: ${t.modelName}. Skipping.`);continue}if(!this.isValidDeepseekMetadata(t.metadata)){r?(console.warn(`[deleteServerThreads] Thread ${n} has invalid or missing metadata. Cannot delete from server, deleting locally only.`),await this.deleteThread(t.id,a)):console.warn(`[deleteServerThreads] Thread ${n} has invalid or missing metadata. Cannot delete from server, skipping thread.`);continue}const o=t.metadata.conversationId,i=`${this.DEEPSEEK_HOST}/api/v0/chat_session/delete`,d={chat_session_id:o},l=(await this.makeDeepseekRequest(i,{method:\"POST\",headers:await this.getHeaders(),body:d,responseType:\"json\"}))._data;if(0!==l?.code||0!==l?.data?.biz_code){const t=l?.msg||l?.data?.biz_msg||\"Unknown error deleting conversation\";return this.handleModelError(`Failed to delete DeepSeek conversation: ${t}`,e.SERVICE_UNAVAILABLE,void 0,l)}console.log(`[deleteServerThreads] Successfully deleted DeepSeek conversation: ${o} from server.`),r&&await this.deleteThread(t.id,a)}}catch(t){return this.handleModelError(\"Error deleting DeepSeek conversation(s)\",e.SERVICE_UNAVAILABLE,void 0,t)}}async uploadFile(t){await this.ensureAuthToken();const r=await this.getPowSolutionForCompletion(\"/api/v0/file/upload_file\"),a=`${this.DEEPSEEK_HOST}/api/v0/file/upload_file`,s=new FormData;s.append(\"file\",t);const n={};let o;this.authToken&&(n.Authorization=this.authToken),n[\"x-ds-pow-response\"]=r;try{const e=await fetch(a,{method:\"POST\",headers:n,body:s,credentials:\"include\"});o=await e.json()}catch(t){return this.handleModelError(\"Failed to upload file to Deepseek\",e.UPLOAD_FAILED,void 0,t)}if(0!==o.code||0!==o.data?.biz_code){const t=o.data?.biz_msg||o.msg||\"Unknown error uploading file\";return this.handleModelError(`Deepseek file upload failed: ${t}`,e.UPLOAD_FAILED,void 0,o)}let i=o.data?.biz_data;return i&&i.id?(\"PENDING\"!==i.status&&\"PARSING\"!==i.status||(i=await this.pollFileStatus(i.id)),\"CONTENT_EMPTY\"===i.status?this.handleModelError(\"No text could be extracted from the image.\",e.UPLOAD_FAILED,void 0,i):\"UNSUPPORTED\"===i.status?this.handleModelError(\"File type not supported for Deepseek\",e.UPLOAD_FAILED,void 0,i):\"SUCCESS\"!==i.status?this.handleModelError(`File upload failed or not supported (status: ${i.status})`,e.UPLOAD_FAILED,void 0,i):i.id):this.handleModelError(\"Deepseek file upload: missing file id in response\",e.UPLOAD_FAILED,void 0,o)}async pollFileStatus(t,r=10,a=1500){const s=`${this.DEEPSEEK_HOST}/api/v0/file/fetch_files?file_ids=${encodeURIComponent(t)}`;for(let e=0;e<r;++e){let e;await new Promise((e=>setTimeout(e,a)));try{const t=await fetch(s,{method:\"GET\",credentials:\"include\",headers:this.authToken?{Authorization:this.authToken}:{}});e=await t.json()}catch(e){continue}if(0===e.code&&0===e.data?.biz_code&&e.data?.biz_data?.files?.[0]){const t=e.data.biz_data.files[0];if([\"SUCCESS\",\"CONTENT_EMPTY\",\"FAILED\",\"UNSUPPORTED\"].includes(t.status))return t}}return this.handleModelError(\"File processing timed out on Deepseek\",e.UPLOAD_FAILED)}}I([Be,b(\"design:type\",Function),b(\"design:paramtypes\",[]),b(\"design:returntype\",Promise)],qe.prototype,\"getUserInfo\",null),I([Be,b(\"design:type\",Function),b(\"design:paramtypes\",[Number]),b(\"design:returntype\",Promise)],qe.prototype,\"getAllConversationsData\",null),I([Be,b(\"design:type\",Function),b(\"design:paramtypes\",[]),b(\"design:returntype\",Promise)],qe.prototype,\"getPlatformData\",null),I([Be,b(\"design:type\",Function),b(\"design:paramtypes\",[String,Object]),b(\"design:returntype\",Promise)],qe.prototype,\"editTitle\",null),I([Be,b(\"design:type\",Function),b(\"design:paramtypes\",[Array,Boolean,Boolean]),b(\"design:returntype\",Promise)],qe.prototype,\"deleteServerThreads\",null);export{t as AIModelError,p as AUTH_EVENTS,h as AbstractModel,Te as BingWebModel,we as ClaudeWebModel,qe as DeepseekWebModel,e as ErrorCode,Ee as GeminiWebModel,Se as PerplexityWebModel,T as clearAuthCache,A as configureAuth,w as copilotExtractor,_ as deepseekExtractor,v as executeTokenRetrievalLogic,m as file2base64,y as getCookiesForDomain,S as getTokenFromWebsite,g as parseSSEResponse,E as requestHostPermission,u as streamAsyncIterable};\r\n", "/**\n * Model Manager for WebAI Extension\n * Handles AI model interactions using the ai-models-bridge library\n */\n\nimport {\n  createModel,\n  getAvailableModels as getLibraryModels,\n  ModelError\n} from '../../lib/ai-models-bridge.esm.js';\n\nexport class ModelManager {\n  constructor() {\n    this.models = new Map(); // Cache for initialized models\n    this.conversations = new Map(); // Track conversation threads\n    this.isInitialized = false;\n  }\n\n  /**\n   * Initialize the model manager\n   */\n  async initialize() {\n    if (this.isInitialized) return;\n\n    console.log('[ModelManager] Initializing...');\n\n    try {\n      // Get available models from library\n      const availableModels = await getLibraryModels();\n      console.log('[ModelManager] Available models:', availableModels.length);\n\n      this.isInitialized = true;\n      console.log('[ModelManager] Initialized successfully');\n    } catch (error) {\n      console.error('[ModelManager] Failed to initialize:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Get available models from the library\n   */\n  async getAvailableModels() {\n    try {\n      const models = await getLibraryModels();\n\n      // Transform to include provider and model info\n      return models.map(model => ({\n        id: model.id,\n        name: model.name,\n        provider: model.provider,\n        description: model.description || '',\n        capabilities: model.capabilities || [],\n        requiresAuth: model.requiresAuth || false,\n        webBased: model.webBased || false\n      }));\n    } catch (error) {\n      console.error('[ModelManager] Error getting available models:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Get model status for all cached models\n   */\n  async getModelStatus() {\n    const status = {};\n\n    for (const [modelId, model] of this.models) {\n      try {\n        status[modelId] = {\n          initialized: true,\n          ready: await this.isModelReady(model),\n          conversations: this.getModelConversationCount(modelId)\n        };\n      } catch (error) {\n        status[modelId] = {\n          initialized: false,\n          error: error.message,\n          conversations: 0\n        };\n      }\n    }\n\n    return status;\n  }\n\n  /**\n   * Test a specific model\n   */\n  async testModel(provider, modelName) {\n    console.log(`[ModelManager] Testing model: ${provider}:${modelName}`);\n\n    try {\n      const model = await this.getOrCreateModel(provider, modelName);\n\n      // Send a simple test message\n      const testMessage = {\n        role: 'user',\n        content: 'Hello! Please respond with just \"Test successful\" to confirm you are working.'\n      };\n\n      const response = await model.sendMessage([testMessage]);\n\n      return {\n        success: true,\n        response: response.content,\n        provider,\n        model: modelName\n      };\n    } catch (error) {\n      console.error(`[ModelManager] Test failed for ${provider}:${modelName}:`, error);\n      return {\n        success: false,\n        error: error.message,\n        provider,\n        model: modelName\n      };\n    }\n  }\n\n  /**\n   * Send a message to a model\n   */\n  async sendMessage(request) {\n    const { id, model: modelId, messages, stream = false, ...options } = request;\n\n    console.log(`[ModelManager] Sending message to model: ${modelId}`);\n\n    try {\n      // Parse model ID (format: provider:model or just model)\n      const [provider, modelName] = this.parseModelId(modelId);\n\n      // Get or create model instance\n      const model = await this.getOrCreateModel(provider, modelName);\n\n      // Get or create conversation thread\n      const conversationId = options.conversationId || `${modelId}-${Date.now()}`;\n      let conversation = this.conversations.get(conversationId);\n\n      if (!conversation) {\n        conversation = {\n          id: conversationId,\n          model: modelId,\n          messages: [],\n          created: Date.now()\n        };\n        this.conversations.set(conversationId, conversation);\n      }\n\n      // Add new messages to conversation\n      conversation.messages.push(...messages);\n\n      if (stream) {\n        return await this.sendStreamingMessage(model, conversation, id, options);\n      } else {\n        return await this.sendSingleMessage(model, conversation, id, options);\n      }\n    } catch (error) {\n      console.error('[ModelManager] Error sending message:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Send a single (non-streaming) message\n   */\n  async sendSingleMessage(model, conversation, requestId, options) {\n    try {\n      const response = await model.sendMessage(conversation.messages, options);\n\n      // Add response to conversation\n      conversation.messages.push({\n        role: 'assistant',\n        content: response.content\n      });\n\n      return {\n        id: requestId,\n        choices: [{\n          index: 0,\n          message: {\n            role: 'assistant',\n            content: response.content\n          },\n          finish_reason: 'stop'\n        }],\n        usage: response.usage || {},\n        done: true\n      };\n    } catch (error) {\n      console.error('[ModelManager] Error in single message:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Send a streaming message\n   */\n  async sendStreamingMessage(model, conversation, requestId, options) {\n    try {\n      const stream = await model.sendMessageStream(conversation.messages, options);\n      let fullContent = '';\n\n      return new Promise((resolve, reject) => {\n        const chunks = [];\n\n        stream.on('data', (chunk) => {\n          fullContent += chunk.content || '';\n\n          const responseChunk = {\n            id: requestId,\n            choices: [{\n              index: 0,\n              delta: {\n                role: 'assistant',\n                content: chunk.content || ''\n              },\n              finish_reason: null\n            }],\n            done: false\n          };\n\n          chunks.push(responseChunk);\n        });\n\n        stream.on('end', () => {\n          // Add final response to conversation\n          conversation.messages.push({\n            role: 'assistant',\n            content: fullContent\n          });\n\n          // Send final chunk\n          const finalChunk = {\n            id: requestId,\n            choices: [{\n              index: 0,\n              delta: {},\n              finish_reason: 'stop'\n            }],\n            usage: stream.usage || {},\n            done: true\n          };\n\n          chunks.push(finalChunk);\n          resolve({ chunks, fullContent });\n        });\n\n        stream.on('error', (error) => {\n          console.error('[ModelManager] Stream error:', error);\n          reject(error);\n        });\n      });\n    } catch (error) {\n      console.error('[ModelManager] Error in streaming message:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Get or create a model instance\n   */\n  async getOrCreateModel(provider, modelName) {\n    const modelKey = `${provider}:${modelName}`;\n\n    if (this.models.has(modelKey)) {\n      return this.models.get(modelKey);\n    }\n\n    console.log(`[ModelManager] Creating new model instance: ${modelKey}`);\n\n    try {\n      const model = await createModel(provider, modelName);\n      this.models.set(modelKey, model);\n      return model;\n    } catch (error) {\n      console.error(`[ModelManager] Failed to create model ${modelKey}:`, error);\n      throw error;\n    }\n  }\n\n  /**\n   * Parse model ID into provider and model name\n   */\n  parseModelId(modelId) {\n    if (modelId.includes(':')) {\n      const [provider, ...modelParts] = modelId.split(':');\n      return [provider, modelParts.join(':')];\n    }\n\n    // Default provider mapping for common models\n    const defaultProviders = {\n      'gpt-4': 'openai',\n      'gpt-3.5-turbo': 'openai',\n      'claude-3-opus': 'anthropic',\n      'claude-3-sonnet': 'anthropic',\n      'claude-3-haiku': 'anthropic',\n      'gemini-pro': 'google',\n      'gemini-1.5-pro': 'google'\n    };\n\n    const provider = defaultProviders[modelId] || 'unknown';\n    return [provider, modelId];\n  }\n\n  /**\n   * Check if a model is ready\n   */\n  async isModelReady(model) {\n    try {\n      // Simple health check\n      return model && typeof model.sendMessage === 'function';\n    } catch (error) {\n      return false;\n    }\n  }\n\n  /**\n   * Get conversation count for a model\n   */\n  getModelConversationCount(modelId) {\n    let count = 0;\n    for (const conversation of this.conversations.values()) {\n      if (conversation.model === modelId) {\n        count++;\n      }\n    }\n    return count;\n  }\n\n  /**\n   * Clear old conversations (cleanup)\n   */\n  clearOldConversations(maxAge = 24 * 60 * 60 * 1000) { // 24 hours default\n    const now = Date.now();\n    const toDelete = [];\n\n    for (const [id, conversation] of this.conversations) {\n      if (now - conversation.created > maxAge) {\n        toDelete.push(id);\n      }\n    }\n\n    toDelete.forEach(id => this.conversations.delete(id));\n\n    if (toDelete.length > 0) {\n      console.log(`[ModelManager] Cleared ${toDelete.length} old conversations`);\n    }\n  }\n\n  /**\n   * Get conversation by ID\n   */\n  getConversation(conversationId) {\n    return this.conversations.get(conversationId);\n  }\n\n  /**\n   * Delete a conversation\n   */\n  deleteConversation(conversationId) {\n    return this.conversations.delete(conversationId);\n  }\n\n  /**\n   * Get all conversations for a model\n   */\n  getModelConversations(modelId) {\n    const conversations = [];\n    for (const conversation of this.conversations.values()) {\n      if (conversation.model === modelId) {\n        conversations.push(conversation);\n      }\n    }\n    return conversations;\n  }\n\n  /**\n   * Cleanup resources\n   */\n  async cleanup() {\n    console.log('[ModelManager] Cleaning up...');\n\n    // Clear all conversations\n    this.conversations.clear();\n\n    // Clear model cache\n    this.models.clear();\n\n    this.isInitialized = false;\n  }\n}\n", "/**\n * Storage Manager for WebAI Extension\n * Handles Chrome storage operations and configuration management\n */\n\nimport browser from 'webextension-polyfill';\n\nexport class StorageManager {\n  constructor() {\n    this.defaultConfig = {\n      wsUrl: 'ws://localhost:3001',\n      serverUrl: 'http://localhost:3000',\n      apiKey: '',\n      autoConnect: true,\n      enableLogging: true,\n      models: {\n        preferred: [],\n        disabled: []\n      },\n      auth: {\n        tokens: {},\n        lastUpdated: {}\n      }\n    };\n  }\n\n  /**\n   * Get configuration from storage\n   */\n  async getConfig() {\n    try {\n      const result = await browser.storage.sync.get('config');\n      return { ...this.defaultConfig, ...result.config };\n    } catch (error) {\n      console.error('[Storage] Error getting config:', error);\n      return this.defaultConfig;\n    }\n  }\n\n  /**\n   * Set configuration in storage\n   */\n  async setConfig(config) {\n    try {\n      const currentConfig = await this.getConfig();\n      const newConfig = { ...currentConfig, ...config };\n      await browser.storage.sync.set({ config: newConfig });\n      console.log('[Storage] Config updated');\n      return newConfig;\n    } catch (error) {\n      console.error('[Storage] Error setting config:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Get API key from storage\n   */\n  async getApiKey() {\n    try {\n      const config = await this.getConfig();\n      return config.apiKey || '';\n    } catch (error) {\n      console.error('[Storage] Error getting API key:', error);\n      return '';\n    }\n  }\n\n  /**\n   * Set API key in storage\n   */\n  async setApiKey(apiKey) {\n    try {\n      await this.setConfig({ apiKey });\n      console.log('[Storage] API key updated');\n    } catch (error) {\n      console.error('[Storage] Error setting API key:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Generate a new API key\n   */\n  async generateApiKey() {\n    try {\n      const apiKey = this.createRandomKey();\n      await this.setApiKey(apiKey);\n      return apiKey;\n    } catch (error) {\n      console.error('[Storage] Error generating API key:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Get auth token for a service\n   */\n  async getAuthToken(serviceName) {\n    try {\n      const config = await this.getConfig();\n      return config.auth.tokens[serviceName] || null;\n    } catch (error) {\n      console.error(`[Storage] Error getting auth token for ${serviceName}:`, error);\n      return null;\n    }\n  }\n\n  /**\n   * Set auth token for a service\n   */\n  async setAuthToken(serviceName, token) {\n    try {\n      const config = await this.getConfig();\n      config.auth.tokens[serviceName] = token;\n      config.auth.lastUpdated[serviceName] = Date.now();\n      await this.setConfig(config);\n      console.log(`[Storage] Auth token updated for ${serviceName}`);\n    } catch (error) {\n      console.error(`[Storage] Error setting auth token for ${serviceName}:`, error);\n      throw error;\n    }\n  }\n\n  /**\n   * Remove auth token for a service\n   */\n  async removeAuthToken(serviceName) {\n    try {\n      const config = await this.getConfig();\n      delete config.auth.tokens[serviceName];\n      delete config.auth.lastUpdated[serviceName];\n      await this.setConfig(config);\n      console.log(`[Storage] Auth token removed for ${serviceName}`);\n    } catch (error) {\n      console.error(`[Storage] Error removing auth token for ${serviceName}:`, error);\n      throw error;\n    }\n  }\n\n  /**\n   * Get all auth tokens\n   */\n  async getAllAuthTokens() {\n    try {\n      const config = await this.getConfig();\n      return config.auth.tokens || {};\n    } catch (error) {\n      console.error('[Storage] Error getting all auth tokens:', error);\n      return {};\n    }\n  }\n\n  /**\n   * Get preferred models\n   */\n  async getPreferredModels() {\n    try {\n      const config = await this.getConfig();\n      return config.models.preferred || [];\n    } catch (error) {\n      console.error('[Storage] Error getting preferred models:', error);\n      return [];\n    }\n  }\n\n  /**\n   * Set preferred models\n   */\n  async setPreferredModels(models) {\n    try {\n      const config = await this.getConfig();\n      config.models.preferred = models;\n      await this.setConfig(config);\n      console.log('[Storage] Preferred models updated');\n    } catch (error) {\n      console.error('[Storage] Error setting preferred models:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Get disabled models\n   */\n  async getDisabledModels() {\n    try {\n      const config = await this.getConfig();\n      return config.models.disabled || [];\n    } catch (error) {\n      console.error('[Storage] Error getting disabled models:', error);\n      return [];\n    }\n  }\n\n  /**\n   * Set disabled models\n   */\n  async setDisabledModels(models) {\n    try {\n      const config = await this.getConfig();\n      config.models.disabled = models;\n      await this.setConfig(config);\n      console.log('[Storage] Disabled models updated');\n    } catch (error) {\n      console.error('[Storage] Error setting disabled models:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Get extension statistics\n   */\n  async getStats() {\n    try {\n      const result = await browser.storage.local.get('stats');\n      return result.stats || {\n        messagesProcessed: 0,\n        modelsUsed: {},\n        lastUsed: null,\n        errors: 0\n      };\n    } catch (error) {\n      console.error('[Storage] Error getting stats:', error);\n      return {\n        messagesProcessed: 0,\n        modelsUsed: {},\n        lastUsed: null,\n        errors: 0\n      };\n    }\n  }\n\n  /**\n   * Update extension statistics\n   */\n  async updateStats(updates) {\n    try {\n      const currentStats = await this.getStats();\n      const newStats = { ...currentStats, ...updates };\n      await browser.storage.local.set({ stats: newStats });\n    } catch (error) {\n      console.error('[Storage] Error updating stats:', error);\n    }\n  }\n\n  /**\n   * Increment message count\n   */\n  async incrementMessageCount(modelId) {\n    try {\n      const stats = await this.getStats();\n      stats.messagesProcessed++;\n      stats.lastUsed = Date.now();\n      \n      if (!stats.modelsUsed[modelId]) {\n        stats.modelsUsed[modelId] = 0;\n      }\n      stats.modelsUsed[modelId]++;\n      \n      await this.updateStats(stats);\n    } catch (error) {\n      console.error('[Storage] Error incrementing message count:', error);\n    }\n  }\n\n  /**\n   * Increment error count\n   */\n  async incrementErrorCount() {\n    try {\n      const stats = await this.getStats();\n      stats.errors++;\n      await this.updateStats(stats);\n    } catch (error) {\n      console.error('[Storage] Error incrementing error count:', error);\n    }\n  }\n\n  /**\n   * Clear all data\n   */\n  async clearAll() {\n    try {\n      await browser.storage.sync.clear();\n      await browser.storage.local.clear();\n      console.log('[Storage] All data cleared');\n    } catch (error) {\n      console.error('[Storage] Error clearing data:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Export configuration\n   */\n  async exportConfig() {\n    try {\n      const config = await this.getConfig();\n      const stats = await this.getStats();\n      \n      return {\n        config,\n        stats,\n        exportedAt: new Date().toISOString(),\n        version: '1.0'\n      };\n    } catch (error) {\n      console.error('[Storage] Error exporting config:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Import configuration\n   */\n  async importConfig(data) {\n    try {\n      if (data.config) {\n        await this.setConfig(data.config);\n      }\n      \n      if (data.stats) {\n        await browser.storage.local.set({ stats: data.stats });\n      }\n      \n      console.log('[Storage] Configuration imported successfully');\n    } catch (error) {\n      console.error('[Storage] Error importing config:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Create a random API key\n   */\n  createRandomKey(length = 32) {\n    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';\n    let result = 'webai_';\n    \n    for (let i = 0; i < length; i++) {\n      result += chars.charAt(Math.floor(Math.random() * chars.length));\n    }\n    \n    return result;\n  }\n\n  /**\n   * Validate configuration\n   */\n  validateConfig(config) {\n    const errors = [];\n    \n    if (config.wsUrl && !this.isValidUrl(config.wsUrl)) {\n      errors.push('Invalid WebSocket URL');\n    }\n    \n    if (config.serverUrl && !this.isValidUrl(config.serverUrl)) {\n      errors.push('Invalid server URL');\n    }\n    \n    return errors;\n  }\n\n  /**\n   * Check if URL is valid\n   */\n  isValidUrl(string) {\n    try {\n      new URL(string);\n      return true;\n    } catch (_) {\n      return false;\n    }\n  }\n}\n", "/**\r\n * Background Service Worker for WebAI Extension\r\n * Handles WebSocket communication with server and manages AI model interactions\r\n */\r\n\r\nimport browser from 'webextension-polyfill';\r\nimport { WebSocketClient } from './websocket.js';\r\nimport { ModelManager } from './modelManager.js';\r\nimport { StorageManager } from '../shared/storage.js';\r\nimport { \r\n  executeTokenRetrievalLogic,\r\n  deepseekExtractor,\r\n  copilotExtractor \r\n} from '../../lib/ai-models-bridge.esm.js';\r\n\r\n// Initialize components\r\nconst storage = new StorageManager();\r\nconst modelManager = new ModelManager();\r\nconst wsClient = new WebSocketClient();\r\n\r\n// Track extension state\r\nlet isInitialized = false;\r\nlet connectionRetryCount = 0;\r\nconst MAX_RETRY_COUNT = 5;\r\n\r\n// Map extractor names to functions (for token retrieval)\r\nconst extractorFunctions = {\r\n  deepseekExtractor: deepseekExtractor,\r\n  copilotExtractor: copilotExtractor,\r\n};\r\n\r\n/**\r\n * Initialize the extension\r\n */\r\nasync function initialize() {\r\n  if (isInitialized) return;\r\n  \r\n  console.log('[WebAI] Initializing extension...');\r\n  \r\n  try {\r\n    // Load saved configuration\r\n    const config = await storage.getConfig();\r\n    \r\n    // Initialize WebSocket connection\r\n    const wsUrl = config.wsUrl || 'ws://localhost:3001';\r\n    await wsClient.connect(wsUrl);\r\n    \r\n    // Initialize model manager\r\n    await modelManager.initialize();\r\n    \r\n    isInitialized = true;\r\n    console.log('[WebAI] Extension initialized successfully');\r\n    \r\n    // Update extension badge\r\n    updateExtensionBadge('ready');\r\n    \r\n  } catch (error) {\r\n    console.error('[WebAI] Failed to initialize:', error);\r\n    updateExtensionBadge('error');\r\n    \r\n    // Retry connection\r\n    if (connectionRetryCount < MAX_RETRY_COUNT) {\r\n      connectionRetryCount++;\r\n      setTimeout(() => initialize(), 5000 * connectionRetryCount);\r\n    }\r\n  }\r\n}\r\n\r\n/**\r\n * Update extension badge to show status\r\n */\r\nfunction updateExtensionBadge(status) {\r\n  const badges = {\r\n    ready: { text: '✓', color: '#4CAF50' },\r\n    error: { text: '!', color: '#F44336' },\r\n    busy: { text: '...', color: '#FF9800' },\r\n    offline: { text: '×', color: '#9E9E9E' }\r\n  };\r\n  \r\n  const badge = badges[status] || badges.offline;\r\n  \r\n  browser.action.setBadgeText({ text: badge.text });\r\n  browser.action.setBadgeBackgroundColor({ color: badge.color });\r\n}\r\n\r\n/**\r\n * Handle messages from popup or settings page\r\n */\r\nbrowser.runtime.onMessage.addListener((message, sender, sendResponse) => {\r\n  console.log('[WebAI] Received message:', message.type);\r\n  \r\n  switch (message.type) {\r\n    case 'GET_STATUS':\r\n      handleGetStatus(sendResponse);\r\n      return true;\r\n      \r\n    case 'GET_MODELS':\r\n      handleGetModels(sendResponse);\r\n      return true;\r\n      \r\n    case 'TEST_MODEL':\r\n      handleTestModel(message.payload, sendResponse);\r\n      return true;\r\n      \r\n    case 'UPDATE_CONFIG':\r\n      handleUpdateConfig(message.payload, sendResponse);\r\n      return true;\r\n      \r\n    case 'GENERATE_API_KEY':\r\n      handleGenerateApiKey(sendResponse);\r\n      return true;\r\n      \r\n    case 'GET_AUTH_TOKEN_FROM_WEBSITE':\r\n      handleAuthTokenRetrieval(message.payload, sendResponse);\r\n      return true;\r\n      \r\n    case 'RECONNECT_WEBSOCKET':\r\n      handleReconnectWebSocket(sendResponse);\r\n      return true;\r\n      \r\n    default:\r\n      console.warn('[WebAI] Unknown message type:', message.type);\r\n      return false;\r\n  }\r\n});\r\n\r\n/**\r\n * Handle status request\r\n */\r\nasync function handleGetStatus(sendResponse) {\r\n  try {\r\n    const status = {\r\n      initialized: isInitialized,\r\n      websocket: {\r\n        connected: wsClient.isConnected(),\r\n        url: wsClient.getUrl()\r\n      },\r\n      models: await modelManager.getModelStatus(),\r\n      config: await storage.getConfig()\r\n    };\r\n    \r\n    sendResponse({ success: true, data: status });\r\n  } catch (error) {\r\n    console.error('[WebAI] Error getting status:', error);\r\n    sendResponse({ success: false, error: error.message });\r\n  }\r\n}\r\n\r\n/**\r\n * Handle get models request\r\n */\r\nasync function handleGetModels(sendResponse) {\r\n  try {\r\n    const models = await modelManager.getAvailableModels();\r\n    sendResponse({ success: true, data: models });\r\n  } catch (error) {\r\n    console.error('[WebAI] Error getting models:', error);\r\n    sendResponse({ success: false, error: error.message });\r\n  }\r\n}\r\n\r\n/**\r\n * Handle test model request\r\n */\r\nasync function handleTestModel(payload, sendResponse) {\r\n  try {\r\n    updateExtensionBadge('busy');\r\n    \r\n    const result = await modelManager.testModel(payload.provider, payload.model);\r\n    \r\n    updateExtensionBadge('ready');\r\n    sendResponse({ success: true, data: result });\r\n  } catch (error) {\r\n    console.error('[WebAI] Error testing model:', error);\r\n    updateExtensionBadge('error');\r\n    sendResponse({ success: false, error: error.message });\r\n  }\r\n}\r\n\r\n/**\r\n * Handle configuration update\r\n */\r\nasync function handleUpdateConfig(config, sendResponse) {\r\n  try {\r\n    await storage.setConfig(config);\r\n    \r\n    // Reinitialize if WebSocket URL changed\r\n    if (config.wsUrl && config.wsUrl !== wsClient.getUrl()) {\r\n      await wsClient.disconnect();\r\n      await wsClient.connect(config.wsUrl);\r\n    }\r\n    \r\n    sendResponse({ success: true });\r\n  } catch (error) {\r\n    console.error('[WebAI] Error updating config:', error);\r\n    sendResponse({ success: false, error: error.message });\r\n  }\r\n}\r\n\r\n/**\r\n * Handle API key generation\r\n */\r\nasync function handleGenerateApiKey(sendResponse) {\r\n  try {\r\n    const key = await storage.generateApiKey();\r\n    sendResponse({ success: true, data: key });\r\n  } catch (error) {\r\n    console.error('[WebAI] Error generating API key:', error);\r\n    sendResponse({ success: false, error: error.message });\r\n  }\r\n}\r\n\r\n/**\r\n * Handle auth token retrieval (for models that need web authentication)\r\n */\r\nasync function handleAuthTokenRetrieval(payload, sendResponse) {\r\n  const { serviceName, targetUrl, urlPattern, extractorName, forceNewTab } = payload || {};\r\n  \r\n  if (!serviceName || !targetUrl || !urlPattern || !extractorName) {\r\n    console.error('[WebAI] Invalid auth token retrieval payload:', payload);\r\n    sendResponse({ success: false, error: 'Invalid payload' });\r\n    return;\r\n  }\r\n  \r\n  const extractorFunc = extractorFunctions[extractorName];\r\n  if (!extractorFunc) {\r\n    console.error('[WebAI] Unknown extractor function:', extractorName);\r\n    sendResponse({ success: false, error: `Unknown extractor: ${extractorName}` });\r\n    return;\r\n  }\r\n  \r\n  try {\r\n    const token = await executeTokenRetrievalLogic(\r\n      serviceName,\r\n      targetUrl,\r\n      urlPattern,\r\n      extractorFunc,\r\n      forceNewTab ?? false\r\n    );\r\n    \r\n    console.log(`[WebAI] Token retrieved for ${serviceName}:`, token ? 'Success' : 'Failed');\r\n    sendResponse({ success: true, token: token || null });\r\n  } catch (error) {\r\n    console.error(`[WebAI] Error retrieving token for ${serviceName}:`, error);\r\n    sendResponse({ success: false, error: error.message });\r\n  }\r\n}\r\n\r\n/**\r\n * Handle WebSocket reconnection\r\n */\r\nasync function handleReconnectWebSocket(sendResponse) {\r\n  try {\r\n    await wsClient.disconnect();\r\n    const config = await storage.getConfig();\r\n    await wsClient.connect(config.wsUrl || 'ws://localhost:3001');\r\n    \r\n    connectionRetryCount = 0;\r\n    sendResponse({ success: true });\r\n  } catch (error) {\r\n    console.error('[WebAI] Error reconnecting WebSocket:', error);\r\n    sendResponse({ success: false, error: error.message });\r\n  }\r\n}\r\n\r\n/**\r\n * Handle WebSocket messages from server\r\n */\r\nwsClient.on('message', async (message) => {\r\n  console.log('[WebAI] WebSocket message:', message.type);\r\n  \r\n  switch (message.type) {\r\n    case 'request':\r\n      await handleServerRequest(message.data);\r\n      break;\r\n      \r\n    case 'status':\r\n      await handleServerStatusRequest(message.data);\r\n      break;\r\n      \r\n    case 'ping':\r\n      wsClient.send({ type: 'pong', timestamp: Date.now() });\r\n      break;\r\n      \r\n    default:\r\n      console.warn('[WebAI] Unknown WebSocket message type:', message.type);\r\n  }\r\n});\r\n\r\n/**\r\n * Handle chat request from server\r\n */\r\nasync function handleServerRequest(request) {\r\n  try {\r\n    updateExtensionBadge('busy');\r\n    \r\n    const response = await modelManager.sendMessage(request);\r\n    \r\n    wsClient.send({\r\n      type: 'response',\r\n      data: response\r\n    });\r\n    \r\n    updateExtensionBadge('ready');\r\n  } catch (error) {\r\n    console.error('[WebAI] Error handling server request:', error);\r\n    \r\n    wsClient.send({\r\n      type: 'response',\r\n      data: {\r\n        id: request.id,\r\n        error: error.message,\r\n        done: true\r\n      }\r\n    });\r\n    \r\n    updateExtensionBadge('error');\r\n  }\r\n}\r\n\r\n/**\r\n * Handle status request from server\r\n */\r\nasync function handleServerStatusRequest(data) {\r\n  try {\r\n    const status = await modelManager.getModelStatus();\r\n    \r\n    wsClient.send({\r\n      type: 'response',\r\n      data: {\r\n        requestId: data.requestId,\r\n        status\r\n      }\r\n    });\r\n  } catch (error) {\r\n    console.error('[WebAI] Error handling status request:', error);\r\n  }\r\n}\r\n\r\n/**\r\n * WebSocket event handlers\r\n */\r\nwsClient.on('connected', () => {\r\n  console.log('[WebAI] WebSocket connected');\r\n  updateExtensionBadge('ready');\r\n  connectionRetryCount = 0;\r\n});\r\n\r\nwsClient.on('disconnected', () => {\r\n  console.log('[WebAI] WebSocket disconnected');\r\n  updateExtensionBadge('offline');\r\n  \r\n  // Attempt to reconnect\r\n  if (connectionRetryCount < MAX_RETRY_COUNT) {\r\n    connectionRetryCount++;\r\n    setTimeout(() => initialize(), 5000 * connectionRetryCount);\r\n  }\r\n});\r\n\r\nwsClient.on('error', (error) => {\r\n  console.error('[WebAI] WebSocket error:', error);\r\n  updateExtensionBadge('error');\r\n});\r\n\r\n// Handle extension installation/update\r\nbrowser.runtime.onInstalled.addListener(async (details) => {\r\n  console.log('[WebAI] Extension installed/updated:', details.reason);\r\n  \r\n  if (details.reason === 'install') {\r\n    // Open settings page on first install\r\n    browser.runtime.openOptionsPage();\r\n  }\r\n  \r\n  // Initialize extension\r\n  await initialize();\r\n});\r\n\r\n// Handle extension startup\r\nbrowser.runtime.onStartup.addListener(async () => {\r\n  console.log('[WebAI] Extension started');\r\n  await initialize();\r\n});\r\n\r\n// Initialize on load\r\ninitialize();\r\n\r\nconsole.log('[WebAI] Background script loaded');\r\n"], "names": [], "sourceRoot": ""}