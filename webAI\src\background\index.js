/**
 * Background Service Worker for WebAI Extension
 * Handles WebSocket communication with server and manages AI model interactions
 */

import browser from 'webextension-polyfill';
import { WebSocketClient } from './websocket.js';
import { ModelManager } from './modelManager.js';
import { StorageManager } from '../shared/storage.js';
import { 
  executeTokenRetrievalLogic,
  deepseekExtractor,
  copilotExtractor 
} from '../../lib/ai-models-bridge.esm.js';

// Initialize components
const storage = new StorageManager();
const modelManager = new ModelManager();
const wsClient = new WebSocketClient();

// Track extension state
let isInitialized = false;
let connectionRetryCount = 0;
const MAX_RETRY_COUNT = 5;

// Map extractor names to functions (for token retrieval)
const extractorFunctions = {
  deepseekExtractor: deepseekExtractor,
  copilotExtractor: copilotExtractor,
};

/**
 * Initialize the extension
 */
async function initialize() {
  if (isInitialized) return;
  
  console.log('[WebAI] Initializing extension...');
  
  try {
    // Load saved configuration
    const config = await storage.getConfig();
    
    // Initialize WebSocket connection
    const wsUrl = config.wsUrl || 'ws://localhost:3001';
    await wsClient.connect(wsUrl);
    
    // Initialize model manager
    await modelManager.initialize();
    
    isInitialized = true;
    console.log('[WebAI] Extension initialized successfully');
    
    // Update extension badge
    updateExtensionBadge('ready');
    
  } catch (error) {
    console.error('[WebAI] Failed to initialize:', error);
    updateExtensionBadge('error');
    
    // Retry connection
    if (connectionRetryCount < MAX_RETRY_COUNT) {
      connectionRetryCount++;
      setTimeout(() => initialize(), 5000 * connectionRetryCount);
    }
  }
}

/**
 * Update extension badge to show status
 */
function updateExtensionBadge(status) {
  const badges = {
    ready: { text: '✓', color: '#4CAF50' },
    error: { text: '!', color: '#F44336' },
    busy: { text: '...', color: '#FF9800' },
    offline: { text: '×', color: '#9E9E9E' }
  };
  
  const badge = badges[status] || badges.offline;
  
  browser.action.setBadgeText({ text: badge.text });
  browser.action.setBadgeBackgroundColor({ color: badge.color });
}

/**
 * Handle messages from popup or settings page
 */
browser.runtime.onMessage.addListener((message, sender, sendResponse) => {
  console.log('[WebAI] Received message:', message.type);
  
  switch (message.type) {
    case 'GET_STATUS':
      handleGetStatus(sendResponse);
      return true;
      
    case 'GET_MODELS':
      handleGetModels(sendResponse);
      return true;
      
    case 'TEST_MODEL':
      handleTestModel(message.payload, sendResponse);
      return true;
      
    case 'UPDATE_CONFIG':
      handleUpdateConfig(message.payload, sendResponse);
      return true;
      
    case 'GENERATE_API_KEY':
      handleGenerateApiKey(sendResponse);
      return true;
      
    case 'GET_AUTH_TOKEN_FROM_WEBSITE':
      handleAuthTokenRetrieval(message.payload, sendResponse);
      return true;
      
    case 'RECONNECT_WEBSOCKET':
      handleReconnectWebSocket(sendResponse);
      return true;
      
    default:
      console.warn('[WebAI] Unknown message type:', message.type);
      return false;
  }
});

/**
 * Handle status request
 */
async function handleGetStatus(sendResponse) {
  try {
    const status = {
      initialized: isInitialized,
      websocket: {
        connected: wsClient.isConnected(),
        url: wsClient.getUrl()
      },
      models: await modelManager.getModelStatus(),
      config: await storage.getConfig()
    };
    
    sendResponse({ success: true, data: status });
  } catch (error) {
    console.error('[WebAI] Error getting status:', error);
    sendResponse({ success: false, error: error.message });
  }
}

/**
 * Handle get models request
 */
async function handleGetModels(sendResponse) {
  try {
    const models = await modelManager.getAvailableModels();
    sendResponse({ success: true, data: models });
  } catch (error) {
    console.error('[WebAI] Error getting models:', error);
    sendResponse({ success: false, error: error.message });
  }
}

/**
 * Handle test model request
 */
async function handleTestModel(payload, sendResponse) {
  try {
    updateExtensionBadge('busy');
    
    const result = await modelManager.testModel(payload.provider, payload.model);
    
    updateExtensionBadge('ready');
    sendResponse({ success: true, data: result });
  } catch (error) {
    console.error('[WebAI] Error testing model:', error);
    updateExtensionBadge('error');
    sendResponse({ success: false, error: error.message });
  }
}

/**
 * Handle configuration update
 */
async function handleUpdateConfig(config, sendResponse) {
  try {
    await storage.setConfig(config);
    
    // Reinitialize if WebSocket URL changed
    if (config.wsUrl && config.wsUrl !== wsClient.getUrl()) {
      await wsClient.disconnect();
      await wsClient.connect(config.wsUrl);
    }
    
    sendResponse({ success: true });
  } catch (error) {
    console.error('[WebAI] Error updating config:', error);
    sendResponse({ success: false, error: error.message });
  }
}

/**
 * Handle API key generation
 */
async function handleGenerateApiKey(sendResponse) {
  try {
    const key = await storage.generateApiKey();
    sendResponse({ success: true, data: key });
  } catch (error) {
    console.error('[WebAI] Error generating API key:', error);
    sendResponse({ success: false, error: error.message });
  }
}

/**
 * Handle auth token retrieval (for models that need web authentication)
 */
async function handleAuthTokenRetrieval(payload, sendResponse) {
  const { serviceName, targetUrl, urlPattern, extractorName, forceNewTab } = payload || {};
  
  if (!serviceName || !targetUrl || !urlPattern || !extractorName) {
    console.error('[WebAI] Invalid auth token retrieval payload:', payload);
    sendResponse({ success: false, error: 'Invalid payload' });
    return;
  }
  
  const extractorFunc = extractorFunctions[extractorName];
  if (!extractorFunc) {
    console.error('[WebAI] Unknown extractor function:', extractorName);
    sendResponse({ success: false, error: `Unknown extractor: ${extractorName}` });
    return;
  }
  
  try {
    const token = await executeTokenRetrievalLogic(
      serviceName,
      targetUrl,
      urlPattern,
      extractorFunc,
      forceNewTab ?? false
    );
    
    console.log(`[WebAI] Token retrieved for ${serviceName}:`, token ? 'Success' : 'Failed');
    sendResponse({ success: true, token: token || null });
  } catch (error) {
    console.error(`[WebAI] Error retrieving token for ${serviceName}:`, error);
    sendResponse({ success: false, error: error.message });
  }
}

/**
 * Handle WebSocket reconnection
 */
async function handleReconnectWebSocket(sendResponse) {
  try {
    await wsClient.disconnect();
    const config = await storage.getConfig();
    await wsClient.connect(config.wsUrl || 'ws://localhost:3001');
    
    connectionRetryCount = 0;
    sendResponse({ success: true });
  } catch (error) {
    console.error('[WebAI] Error reconnecting WebSocket:', error);
    sendResponse({ success: false, error: error.message });
  }
}

/**
 * Handle WebSocket messages from server
 */
wsClient.on('message', async (message) => {
  console.log('[WebAI] WebSocket message:', message.type);
  
  switch (message.type) {
    case 'request':
      await handleServerRequest(message.data);
      break;
      
    case 'status':
      await handleServerStatusRequest(message.data);
      break;
      
    case 'ping':
      wsClient.send({ type: 'pong', timestamp: Date.now() });
      break;
      
    default:
      console.warn('[WebAI] Unknown WebSocket message type:', message.type);
  }
});

/**
 * Handle chat request from server
 */
async function handleServerRequest(request) {
  try {
    updateExtensionBadge('busy');
    
    const response = await modelManager.sendMessage(request);
    
    wsClient.send({
      type: 'response',
      data: response
    });
    
    updateExtensionBadge('ready');
  } catch (error) {
    console.error('[WebAI] Error handling server request:', error);
    
    wsClient.send({
      type: 'response',
      data: {
        id: request.id,
        error: error.message,
        done: true
      }
    });
    
    updateExtensionBadge('error');
  }
}

/**
 * Handle status request from server
 */
async function handleServerStatusRequest(data) {
  try {
    const status = await modelManager.getModelStatus();
    
    wsClient.send({
      type: 'response',
      data: {
        requestId: data.requestId,
        status
      }
    });
  } catch (error) {
    console.error('[WebAI] Error handling status request:', error);
  }
}

/**
 * WebSocket event handlers
 */
wsClient.on('connected', () => {
  console.log('[WebAI] WebSocket connected');
  updateExtensionBadge('ready');
  connectionRetryCount = 0;
});

wsClient.on('disconnected', () => {
  console.log('[WebAI] WebSocket disconnected');
  updateExtensionBadge('offline');
  
  // Attempt to reconnect
  if (connectionRetryCount < MAX_RETRY_COUNT) {
    connectionRetryCount++;
    setTimeout(() => initialize(), 5000 * connectionRetryCount);
  }
});

wsClient.on('error', (error) => {
  console.error('[WebAI] WebSocket error:', error);
  updateExtensionBadge('error');
});

// Handle extension installation/update
browser.runtime.onInstalled.addListener(async (details) => {
  console.log('[WebAI] Extension installed/updated:', details.reason);
  
  if (details.reason === 'install') {
    // Open settings page on first install
    browser.runtime.openOptionsPage();
  }
  
  // Initialize extension
  await initialize();
});

// Handle extension startup
browser.runtime.onStartup.addListener(async () => {
  console.log('[WebAI] Extension started');
  await initialize();
});

// Initialize on load
initialize();

console.log('[WebAI] Background script loaded');
