{"name": "webai-extension", "version": "1.0.0", "description": "Chrome extension for WebAI - OpenAI-compatible interface for web AI services", "scripts": {"dev": "webpack --mode development --watch", "build": "webpack --mode production", "clean": "<PERSON><PERSON><PERSON> dist"}, "author": "", "license": "MIT", "devDependencies": {"@types/chrome": "^0.0.260", "@types/webextension-polyfill": "^0.10.7", "copy-webpack-plugin": "^12.0.2", "css-loader": "^6.10.0", "html-webpack-plugin": "^5.6.0", "mini-css-extract-plugin": "^2.8.1", "rimraf": "^5.0.7", "style-loader": "^3.3.4", "webpack": "^5.91.0", "webpack-cli": "^5.1.4"}, "dependencies": {"nanoid": "^5.0.7", "webextension-polyfill": "^0.10.0"}}