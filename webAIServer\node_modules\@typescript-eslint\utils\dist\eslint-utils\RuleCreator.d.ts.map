{"version": 3, "file": "RuleCreator.d.ts", "sourceRoot": "", "sources": ["../../src/eslint-utils/RuleCreator.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EACV,WAAW,EACX,YAAY,EACZ,YAAY,EACZ,gBAAgB,EAChB,UAAU,EACX,MAAM,mBAAmB,CAAC;AAG3B,YAAY,EAAE,YAAY,EAAE,UAAU,EAAE,CAAC;AAGzC,MAAM,MAAM,uBAAuB,CAAC,OAAO,SAAS,SAAS,OAAO,EAAE,IAAI,IAAI,CAC5E,gBAAgB,CAAC,OAAO,CAAC,EACzB,KAAK,CACN,CAAC;AACF,MAAM,MAAM,mBAAmB,CAC7B,UAAU,SAAS,MAAM,EACzB,OAAO,SAAS,SAAS,OAAO,EAAE,IAChC,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE,OAAO,CAAC,EAAE,MAAM,CAAC,GAAG;IACpD,IAAI,EAAE,uBAAuB,CAAC,OAAO,CAAC,CAAC;CACxC,CAAC;AAEF,MAAM,WAAW,oBAAoB,CACnC,OAAO,SAAS,SAAS,OAAO,EAAE,EAClC,UAAU,SAAS,MAAM;IAEzB,MAAM,EAAE,CACN,OAAO,EAAE,QAAQ,CAAC,WAAW,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC,EACnD,kBAAkB,EAAE,QAAQ,CAAC,OAAO,CAAC,KAClC,YAAY,CAAC;IAClB,cAAc,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC;CACnC;AAED,MAAM,WAAW,YAAY,CAC3B,OAAO,SAAS,SAAS,OAAO,EAAE,EAClC,UAAU,SAAS,MAAM,CACzB,SAAQ,oBAAoB,CAAC,OAAO,EAAE,UAAU,CAAC;IACjD,IAAI,EAAE,YAAY,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;CACzC;AAED,MAAM,WAAW,mBAAmB,CAClC,OAAO,SAAS,SAAS,OAAO,EAAE,EAClC,UAAU,SAAS,MAAM,CACzB,SAAQ,oBAAoB,CAAC,OAAO,EAAE,UAAU,CAAC;IACjD,IAAI,EAAE,mBAAmB,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;IAC/C,IAAI,EAAE,MAAM,CAAC;CACd;AAED;;;;;GAKG;AACH,wBAAgB,WAAW,CAAC,UAAU,EAAE,CAAC,QAAQ,EAAE,MAAM,KAAK,MAAM,IAIhE,OAAO,SAAS,SAAS,OAAO,EAAE,EAClC,UAAU,SAAS,MAAM,2BAKxB,QAAQ,CAAC,mBAAmB,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC,KAAG,UAAU,CAChE,UAAU,EACV,OAAO,CACR,CAYF;yBAzBe,WAAW;;;AA2B3B;;;;;GAKG;AACH,iBAAS,UAAU,CACjB,OAAO,SAAS,SAAS,OAAO,EAAE,EAClC,UAAU,SAAS,MAAM,EACzB,EACA,MAAM,EACN,cAAc,EACd,IAAI,GACL,EAAE,QAAQ,CAAC,YAAY,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC,GAAG,UAAU,CACzD,UAAU,EACV,OAAO,CACR,CASA"}