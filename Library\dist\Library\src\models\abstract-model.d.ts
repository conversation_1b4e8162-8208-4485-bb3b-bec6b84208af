import { AIModel, <PERSON><PERSON><PERSON>ageO<PERSON>s, ChatThread, Chat<PERSON>essage, StatusEvent, ErrorCode } from './types';
/**
 * Abstract base class for all AI models
 */
export declare abstract class AbstractModel implements AIModel {
    protected currentThread?: ChatThread;
    private static readonly THREADS_STORAGE_KEY;
    /**
    * Get the name of the AI model
    */
    abstract getName(): string;
    /**
     * Check if the model supports image input
     */
    abstract supportsImageInput(): boolean;
    /**
     * Send a message to the AI model and get a response
     * @param prompt The message to send
     * @param options Additional options for the request
     */
    sendMessage(prompt: string, options?: SendMessageOptions): Promise<string>;
    /**
     * Reset the conversation history
     */
    abstract initNewThread(): Promise<void>;
    /**
     * Internal method to send a message to the AI model
     * @param params Parameters for sending a message
     */
    protected abstract doSendMessage(params: {
        prompt: string;
        images?: File[];
        signal?: AbortSignal;
        mode?: string;
        model?: string;
        style_key?: string;
        searchFocus?: string;
        searchSources?: string[];
        onEvent: (event: StatusEvent) => void;
    }): Promise<void>;
    getAllThreads(): Promise<ChatThread[]>;
    protected saveThreadsToStorage(threads: ChatThread[]): Promise<void>;
    getCurrentThread(): ChatThread | undefined;
    loadThread(threadId: string): Promise<void>;
    saveThread(): Promise<void>;
    deleteThread(threadId: string, createNewThreadAfterDelete?: boolean): Promise<void>;
    protected createMessage(role: ChatMessage['role'], content: string): ChatMessage;
    /**
     * The base URL for the AI service
     */
    protected baseUrl: string;
    protected models: any;
    protected defaultModel: string;
    /**
     * Get the base URL for the AI service
     */
    getBaseUrl(): string;
    /**
     * Helper method to handle errors in a consistent way across all models
     *
     * This method:
     * 1. Creates a properly formatted AIModelError with appropriate error code
     * 2. Sends an ERROR event to the client if params.onEvent is provided
     * 3. Logs the error for debugging
     * 4. Throws the AIModelError
     *
     * @param errDesc A description of the error situation
     * @param errorCode The error code to use (will try to infer from original error if not provided)
     * @param params Optional - The original parameters with onEvent callback
     * @param originalError Optional - The original error that occurred
     * @returns Never returns - always throws an AIModelError
     */
    protected handleModelError(errDesc: string, errorCode?: ErrorCode, params?: {
        onEvent: (event: StatusEvent) => void;
    }, originalError?: unknown): never;
    /**
     * Share the current conversation and get a shareable URL
     * This is an optional method that models can implement if they support sharing
     * @param options Optional sharing options that may be model-specific
     * @returns A promise that resolves to a shareable URL
     */
    shareConversation(options?: any): Promise<string>;
}
