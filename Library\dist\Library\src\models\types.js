/**
 * Error codes for AI model errors
 */
export var ErrorCode;
(function (ErrorCode) {
    ErrorCode["UNKNOWN_ERROR"] = "unknown_error";
    ErrorCode["NETWORK_ERROR"] = "network_error";
    ErrorCode["UNAUTHORIZED"] = "unauthorized";
    ErrorCode["SERVICE_UNAVAILABLE"] = "service_unavailable";
    ErrorCode["MISSING_API_KEY"] = "missing_api_key";
    ErrorCode["MISSING_HOST_PERMISSION"] = "missing_host_permission";
    ErrorCode["CONVERSATION_LIMIT"] = "conversation_limit";
    ErrorCode["CONTENT_FILTERED"] = "content_filtered";
    ErrorCode["INVALID_REQUEST"] = "invalid_request";
    ErrorCode["INVALID_API_KEY"] = "invalid_api_key";
    ErrorCode["INVALID_THREAD_ID"] = "invalid_thread_id";
    ErrorCode["INVALID_MESSAGE_ID"] = "invalid_message_id";
    ErrorCode["INVALID_MODEL"] = "invalid_model";
    // INVALID_IMAGE = 'invalid_image',
    // INVALID_IMAGE_URL = 'invalid_image_url',
    ErrorCode["INVALID_IMAGE_TYPE"] = "invalid_image_type";
    ErrorCode["INVALID_IMAGE_CONTENT"] = "invalid_image_content";
    ErrorCode["UPLOAD_FAILED"] = "upload_failed";
    ErrorCode["UPLOAD_TIMEOUT"] = "upload_timeout";
    ErrorCode["UPLOAD_SIZE_EXCEEDED"] = "upload_size_exceeded";
    ErrorCode["UPLOAD_TYPE_EXCEEDED"] = "upload_type_exceeded";
    ErrorCode["UPLOAD_AMOUNT_EXCEEDED"] = "upload_amount_exceeded";
    ErrorCode["UPLOAD_TYPE_NOT_SUPPORTED"] = "upload_type_not_supported";
    ErrorCode["RATE_LIMIT_EXCEEDED"] = "rate_limit_exceeded";
    ErrorCode["METADATA_INITIALIZATION_ERROR"] = "metadata_initialization_error";
    ErrorCode["FEATURE_NOT_SUPPORTED"] = "feature_not_supported";
    ErrorCode["RESPONSE_PARSING_ERROR"] = "response_parsing_error";
})(ErrorCode || (ErrorCode = {}));
/**
 * Error class for AI model errors
 */
export class AIModelError extends Error {
    constructor(message, code = ErrorCode.UNKNOWN_ERROR) {
        super(message);
        this.code = code;
        this.name = 'AIModelError';
    }
}
