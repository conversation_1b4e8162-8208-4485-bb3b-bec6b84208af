import { AbstractModel } from './abstract-model';
import { AIModelError, ErrorCode, StatusEvent } from './types';
export declare class DeepseekWebModel extends AbstractModel {
    private authToken?;
    private readonly DEEPSEEK_HOST;
    private currentOnEventCallback?;
    constructor();
    private initialize;
    /**
     * Ensures the authentication token is loaded and valid. Throws if not available after trying.
     */
    private ensureAuthToken;
    /**
     * Initializes local storage for threads and validates existing ones.
     */
    private initializeStorage;
    /**
     * Validates metadata of existing threads stored locally.
     */
    private validateExistingThreads;
    /**
     * Type guard to check if metadata is valid for DeepSeek.
     */
    private isValidDeepseekMetadata;
    /**
     * Retrieves valid DeepSeek metadata from the current thread, throws if invalid.
     */
    private getDeepseekMetadata;
    getName(): string;
    supportsImageInput(): boolean;
    /**
     * Creates standard headers for DeepSeek API requests.
     */
    private getHeaders;
    /**
     * Helper to fetch and solve the PoW challenge for /api/v0/chat/completion
     */
    private getPowSolutionForCompletion;
    /**
    * Wrapper around ofetch to handle DeepSeek specific challenges like PoW and Cloudflare.
    */
    private makeDeepseekRequest;
    /**
     * Fetches current user information.
     */
    getUserInfo(): Promise<any>;
    /**
     * Fetches a page of conversation sessions.
     */
    getAllConversationsData(count?: number): Promise<any>;
    /**
     * Fetches platform version and status information.
     */
    getPlatformData(): Promise<[string | null, any | null]>;
    /**
     * Creates a new conversation session on the server.
     */
    private createConversation;
    /**
     * Initializes a new local and server-side conversation thread.
     */
    initNewThread(): Promise<void>;
    /**
     * Implements the core logic for sending a message and processing the response stream.
     */
    protected doSendMessage(params: {
        prompt: string;
        images?: File[] | undefined;
        signal?: AbortSignal | undefined;
        onEvent: (event: StatusEvent) => void;
    }): Promise<void>;
    /**
     * Handles API errors, attempting to map them to standard ErrorCodes.
     * This is called internally by makeDeepseekRequest or other API interaction points.
     * It should return 'never' because it ultimately calls the base handleModelError which throws.
     */
    private handleApiError;
    /** Helper to emit status events via the onEvent callback */
    private emitStatus;
    protected createModelError(message: string, code: ErrorCode, cause?: any): AIModelError;
}
