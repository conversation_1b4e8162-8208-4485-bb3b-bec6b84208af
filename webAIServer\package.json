{"name": "webai-server", "version": "1.0.0", "description": "OpenAI-compatible API server for WebAI Chrome Extension", "main": "dist/index.js", "scripts": {"start": "node dist/index.js", "dev": "tsx watch src/index.ts", "build": "tsc", "clean": "<PERSON><PERSON><PERSON> dist", "lint": "eslint src --ext .ts", "test": "jest"}, "keywords": ["openai", "api", "ai", "chatgpt", "claude", "gemini", "server", "websocket"], "author": "", "license": "MIT", "dependencies": {"cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.19.2", "express-rate-limit": "^7.2.0", "helmet": "^7.1.0", "morgan": "^1.10.0", "nanoid": "^5.0.7", "winston": "^3.13.0", "ws": "^8.17.0", "zod": "^3.23.8"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/morgan": "^1.9.9", "@types/node": "^20.12.12", "@types/ws": "^8.5.10", "@typescript-eslint/eslint-plugin": "^7.9.0", "@typescript-eslint/parser": "^7.9.0", "eslint": "^8.57.0", "jest": "^29.7.0", "rimraf": "^5.0.7", "tsx": "^4.10.5", "typescript": "^5.4.5"}}