import { AbstractModel } from './abstract-model';
import { StatusEvent } from './types';
interface ClaudeWebModelConfig {
    sessionKey?: string;
}
interface ClaudeThreadMetadata {
    organizationId: string;
    conversationId: string;
}
export declare class ClaudeWebModel extends AbstractModel {
    private sessionKey?;
    private organizationId?;
    constructor(config?: ClaudeWebModelConfig);
    private initializeStorage;
    private validateExistingThreads;
    private isValidClaudeMetadata;
    getName(): string;
    supportsImageInput(): boolean;
    private createConversation;
    private generateChatTitle;
    private getHeaders;
    private ensureThreadLoaded;
    private getClaudeMetadata;
    private getCurrentThreadSafe;
    initNewThread(): Promise<void>;
    loadThread(threadId: string): Promise<void>;
    protected doSendMessage(params: {
        prompt: string;
        images?: File[];
        signal?: AbortSignal;
        style_key?: string;
        onEvent: (event: StatusEvent) => void;
    }): Promise<void>;
    private processEvent;
    private fileToDataUrl;
    /**
     * Updates the title of a conversation on <PERSON>'s servers
     * @param newTitle The new title to set for the conversation
     * @param options Configuration options
     * @serverOperation This method makes direct API calls to Claude's servers
     */
    editTitle(newTitle: string, options?: {
        loadThread?: boolean;
        metadata?: ClaudeThreadMetadata;
        tryUpdateThread?: boolean;
    }): Promise<void>;
    /**
     * Deletes one or more conversations from Claude's servers
     * @param organizationId The organization ID
     * @param conversationIds Array of conversation IDs to delete
     * @returns Response data from the server
     * @serverOperation This method makes direct API calls to Claude's servers
     */
    deleteServerThreads(conversationIds: string[], updateLocalThread?: boolean, createNewThreadAfterDelete?: boolean, organizationId?: string | undefined): Promise<void>;
    /**
     * Shares a conversation and gets a shareable URL from Claude's servers
     * @param options Configuration options for sharing
     * @returns A promise that resolves to a shareable URL
     * @serverOperation This method makes direct API calls to Claude's servers
     */
    shareConversation(options?: {
        loadThread?: boolean;
        metadata?: ClaudeThreadMetadata;
    }): Promise<string>;
    /**
     * Finds a style by its key in the styles data
     * @param data The styles data object
     * @param targetKey The key to search for
     * @returns The found style or undefined
     */
    findStyleByKey(data: any, targetKey: string): any | undefined;
    /**
     * Gets available styles from Claude's servers
     * @param organizationId The organization ID
     * @returns The styles data
     * @serverOperation This method makes direct API calls to Claude's servers
     */
    getStyles(organizationId?: string): Promise<any>;
    /**
     * Gets conversation data from Claude's servers
     * @param options Configuration options
     * @returns The conversation data
     * @serverOperation This method makes direct API calls to Claude's servers
     */
    getConversationData(options?: {
        loadThread?: boolean;
        metadata?: ClaudeThreadMetadata;
    }): Promise<any>;
    /**
     * Gets all conversations data from Claude's servers
     * @param organizationId The organization ID
     * @returns All conversations data
     * @serverOperation This method makes direct API calls to Claude's servers
     */
    getAllConversationsData(organizationId?: string): Promise<any>;
    /**
     * Gets organization data from Claude's servers
     * @param organizationId The organization ID
     * @returns The organization data
     * @serverOperation This method makes direct API calls to Claude's servers
     */
    getOrganizationData(organizationId?: string): Promise<any>;
    /**
     * Gets all organizations data from Claude's servers
     * @returns All organizations data
     * @serverOperation This method makes direct API calls to Claude's servers
     */
    getAllOrganizationsData(): Promise<any>;
    /**
     * Fetches the organization ID from Claude's servers
     * @returns The organization ID or undefined
     * @serverOperation This method makes direct API calls to Claude's servers
     */
    getOrganizationId(): Promise<string | undefined>;
}
export {};
