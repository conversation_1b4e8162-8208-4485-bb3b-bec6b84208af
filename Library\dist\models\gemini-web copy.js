var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
import { ofetch } from 'ofetch';
import { v4 as uuid } from 'uuid';
import { AbstractModel } from './abstract-model';
import { AIModelError, ErrorCode } from './types';
import { requestHostPermission } from '../utils/auth'; // Added import
/**
 * Decorator for methods that interact directly with the Gemini server
 */
function serverOperation(target, propertyKey, descriptor) {
    // Original method is preserved. This is just for documentation/marking purposes
    return descriptor;
}
// Helper functions
function generateReqId() {
    return Math.floor(Math.random() * 900000) + 100000;
}
function extractFromHTML(variableName, html) {
    const regex = new RegExp(`"${variableName}":"([^"]+)"`);
    const match = regex.exec(html);
    return match?.[1];
}
export class GeminiWebModel extends AbstractModel {
    constructor() {
        super();
        this.models = {
            "gemini-2.0-flash": { "x-goog-ext-525001261-jspb": '[null,null,null,null,"f299729663a2343f"]' },
            "gemini-2.0-flash-exp": { "x-goog-ext-525001261-jspb": '[null,null,null,null,"f299729663a2343f"]' },
            "gemini-2.0-flash-thinking": { "x-goog-ext-525001261-jspb": '[null,null,null,null,"9c17b1863f581b8a"]' },
            "gemini-2.0-flash-thinking-with-apps": { "x-goog-ext-525001261-jspb": '[null,null,null,null,"f8f8f5ea629f5d37"]' },
            "gemini-2.0-exp-advanced": { "x-goog-ext-525001261-jspb": '[null,null,null,null,"b1e46a6037e6aa9f"]' },
            "gemini-1.5-flash": { "x-goog-ext-525001261-jspb": '[null,null,null,null,"418ab5ea040b5c43"]' },
            "gemini-1.5-pro": { "x-goog-ext-525001261-jspb": '[null,null,null,null,"9d60dfae93c9ff1f"]' },
            "gemini-1.5-pro-research": { "x-goog-ext-525001261-jspb": '[null,null,null,null,"e5a44cb1dae2b489"]' },
        };
        this.defaultModel = 'gemini-2.0-flash';
        this.initializeStorage().catch(console.error);
        this.fetchRequestParams().catch(console.error); // Fetch params on init
    }
    async fetchRequestParams() {
        try {
            if (!(await requestHostPermission('https://gemini.google.com/'))) {
                console.warn('Missing host permission for gemini.google.com');
                // Don't throw an error here, allow basic functionality if possible
                // The actual API calls will fail later if permission is still missing
                return;
            }
            const html = await ofetch('https://gemini.google.com/app');
            this.atValue = extractFromHTML('SNlM0e', html);
            this.blValue = extractFromHTML('cfb2h', html);
            if (!this.atValue || !this.blValue) {
                console.error('Failed to extract atValue or blValue from Gemini page');
                // Consider throwing an error or setting a specific state
            }
        }
        catch (error) {
            console.error('Error fetching initial Gemini page parameters:', error);
            // Handle error appropriately, maybe retry or inform the user
        }
    }
    async initializeStorage() {
        // Ensure threads storage exists
        const threads = await this.getAllThreads();
        if (!threads.length) {
            await this.saveThreadsToStorage([]);
        }
        await this.validateExistingThreads();
    }
    async validateExistingThreads() {
        const threads = await this.getAllThreads();
        let hasChanges = false;
        for (const thread of threads) {
            if (thread.modelName === this.getName() && !this.isValidBardMetadata(thread.metadata)) {
                await this.deleteThread(thread.id);
                hasChanges = true;
            }
        }
        if (hasChanges) {
            await this.saveThreadsToStorage(threads.filter(t => t.modelName !== this.getName() || this.isValidBardMetadata(t.metadata)));
        }
    }
    isValidBardMetadata(metadata) {
        return metadata?.contextIds && Array.isArray(metadata.contextIds) && metadata.contextIds.length === 3 &&
            metadata?.requestParams?.atValue && metadata?.requestParams?.blValue;
    }
    getName() {
        return 'Google Bard';
    }
    supportsImageInput() {
        return true;
    }
    /**
     * Fetches conversation data for a specific conversation
     * @param conversationId The ID of the conversation to fetch
     * @returns The conversation data
     * @serverOperation This method makes direct API calls to Gemini's servers
     */
    async getConversation(conversationId) {
        try {
            await this.ensureRequestParams();
            // Prepare the request payload
            const payload = [
                ["hNvQHb",
                    JSON.stringify([
                        [conversationId, 10], // 10 is the max number of messages to fetch
                        null,
                        null,
                        null,
                        "generic"
                    ])
                ]
            ];
            const requestBody = new URLSearchParams({
                'f.req': JSON.stringify(payload),
                'at': this.atValue
            });
            const apiUrl = `https://gemini.google.com/_/BardChatUi/data/batchexecute?${new URLSearchParams({
                'rpcids': 'hNvQHb',
                'source-path': `/app/${conversationId}`,
                'bl': this.blValue,
                'f.sid': '1644486548182535405', // This might need to be dynamic in the future
                'hl': 'en',
                '_reqid': String(generateReqId()),
                'rt': 'c'
            })}`;
            const response = await ofetch(apiUrl, {
                method: 'POST',
                headers: this.getHeaders(),
                body: requestBody,
                parseResponse: (txt) => txt,
            });
            // Parse the response
            try {
                const lines = response.split('\n');
                const jsonPart = lines.find((line) => line.startsWith('['));
                if (!jsonPart) {
                    throw new Error('Invalid response format');
                }
                const data = JSON.parse(jsonPart);
                if (data[0] && data[0][2]) {
                    const conversationData = JSON.parse(data[0][2]);
                    return conversationData;
                }
                throw new Error('Failed to parse conversation data');
            }
            catch (parseError) {
                console.error('Error parsing conversation data:', parseError);
                throw new AIModelError('Failed to parse conversation data', ErrorCode.UNKNOWN_ERROR);
            }
        }
        catch (error) {
            console.error('Error fetching conversation:', error);
            throw new AIModelError(`Failed to fetch conversation: ${error instanceof Error ? error.message : String(error)}`, ErrorCode.SERVICE_UNAVAILABLE);
        }
    }
    /**
     * Updates the title of a conversation
     * @param newTitle The new title to set for the conversation
     * @param emoji Optional emoji to display with the title
     * @serverOperation This method makes direct API calls to Gemini's servers
     */
    async editTitle(newTitle, emoji) {
        try {
            const thread = await this.getCurrentThreadSafe();
            const metadata = thread.metadata;
            if (!metadata || !metadata.contextIds || !metadata.contextIds[0]) {
                throw new AIModelError('Invalid thread metadata for title update', ErrorCode.INVALID_REQUEST);
            }
            const conversationId = metadata.contextIds[0];
            await this.ensureRequestParams();
            // Prepare the request payload
            const payload = [
                ["MUAZcd",
                    JSON.stringify([
                        null,
                        [[
                                "title",
                                "icon",
                                "user_selected_icon"
                            ]],
                        [conversationId, newTitle, null, null, emoji || ""],
                        null,
                        "generic"
                    ])
                ]
            ];
            const requestBody = new URLSearchParams({
                'f.req': JSON.stringify(payload),
                'at': this.atValue || metadata.requestParams.atValue
            });
            const apiUrl = `https://gemini.google.com/_/BardChatUi/data/batchexecute?${new URLSearchParams({
                'rpcids': 'MUAZcd',
                'source-path': `/app/${conversationId}`,
                'bl': this.blValue || metadata.requestParams.blValue,
                'f.sid': '-8989875085784444802', // This might need to be dynamic in the future
                'hl': 'en',
                '_reqid': String(generateReqId()),
                'rt': 'c'
            })}`;
            const response = await ofetch(apiUrl, {
                method: 'POST',
                headers: this.getHeaders(),
                body: requestBody
            });
            // Update local thread title
            thread.title = newTitle;
            await this.saveThread(thread);
            this.emit('threadUpdate', thread);
            return;
        }
        catch (error) {
            console.error('Error updating conversation title:', error);
            throw new AIModelError(`Failed to update conversation title: ${error instanceof Error ? error.message : String(error)}`, ErrorCode.SERVICE_UNAVAILABLE);
        }
    }
    getHeaders(contentType = 'application/x-www-form-urlencoded;charset=UTF-8') {
        // Note: Gemini uses application/x-www-form-urlencoded for most requests
        return {
            'Content-Type': contentType,
            'Host': 'gemini.google.com',
            'Origin': 'https://gemini.google.com',
            'Referer': 'https://gemini.google.com/',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            // Add other necessary headers like cookies if needed
            // 'Cookie': '...' // This would typically be handled by the browser environment or fetched dynamically
        };
    }
    /**
     * Ensures that we have valid request parameters (atValue and blValue)
     * @returns Promise that resolves when parameters are available
     */
    async ensureRequestParams() {
        if (!this.atValue || !this.blValue) {
            await this.fetchRequestParams();
            if (!this.atValue || !this.blValue) {
                throw new AIModelError('Failed to obtain required Gemini parameters', ErrorCode.SERVICE_UNAVAILABLE);
            }
        }
    }
    async getCurrentThreadSafe() {
        if (!this.currentThread) {
            throw new AIModelError('No thread selected', ErrorCode.THREAD_NOT_SELECTED);
        }
        const thread = await this.getThread(this.currentThreadId);
        if (!thread) {
            throw new AIModelError(`Thread ${this.currentThreadId} not found`, ErrorCode.THREAD_NOT_FOUND);
        }
        if (!this.isValidBardMetadata(thread.metadata)) {
            throw new AIModelError(`Invalid metadata for thread ${this.currentThreadId}`, ErrorCode.INVALID_THREAD_METADATA);
        }
        return thread;
    }
    async initNewThread() {
        await this.ensureRequestParams(); // Ensure params are loaded before creating a thread
        const newThreadId = uuid();
        const metadata = {
            // Placeholder contextIds, will be updated after first message
            contextIds: ['', '', ''],
            requestParams: {
                atValue: this.atValue,
                blValue: this.blValue
            }
        };
        const newThread = {
            id: newThreadId,
            title: 'New Chat',
            messages: [],
            metadata: metadata,
            modelName: this.getName(),
            inProgress: false,
            createdAt: Date.now(),
            updatedAt: Date.now(),
        };
        await this.saveThread(newThread);
        this.currentThreadId = newThreadId;
        this.emit('threadUpdate', newThread);
        this.emit('status', { type: 'info', message: 'New thread initialized' });
    }
    async loadThread(threadId) {
        const thread = await this.getThread(threadId);
        if (!thread) {
            throw new AIModelError(`Thread ${threadId} not found`, ErrorCode.THREAD_NOT_FOUND);
        }
        if (!this.isValidBardMetadata(thread.metadata)) {
            // Attempt to fetch missing params if possible, or mark as invalid
            console.warn(`Invalid or missing metadata for thread ${threadId}. Attempting to refresh.`);
            await this.ensureRequestParams();
            thread.metadata = {
                ...thread.metadata, // Keep existing contextIds if present
                requestParams: {
                    atValue: this.atValue,
                    blValue: this.blValue
                }
            };
            if (!this.isValidBardMetadata(thread.metadata)) {
                throw new AIModelError(`Invalid metadata for thread ${threadId} after refresh`, ErrorCode.INVALID_THREAD_METADATA);
            }
            await this.saveThread(thread); // Save updated metadata
        }
        this.currentThreadId = threadId;
        // Update internal at/bl values from loaded thread if they differ
        this.atValue = thread.metadata.requestParams.atValue;
        this.blValue = thread.metadata.requestParams.blValue;
        this.emit('threadUpdate', thread);
        this.emit('status', { type: 'info', message: `Thread ${thread.title} loaded` });
    }
    async doSendMessage(params) {
        const { prompt, images, model = this.defaultModel, signal, onEvent } = params;
        const thread = await this.getCurrentThreadSafe();
        const metadata = thread.metadata;
        onEvent({ type: 'info', message: 'Sending message...' });
        // Add user message to thread immediately
        const userMessage = {
            id: uuid(),
            role: 'user',
            content: prompt,
            timestamp: Date.now(),
            images: images ? images.map(img => ({ url: URL.createObjectURL(img), name: img.name })) : undefined // Store image URLs temporarily
        };
        thread.messages.push(userMessage);
        thread.updatedAt = Date.now();
        thread.inProgress = true;
        await this.saveThread(thread);
        this.emit('threadUpdate', thread);
        let assistantMessageId = uuid();
        let fullResponse = '';
        let responseContextIds = null;
        let finalTitle = null;
        // Define finalizeAssistantMessage function to handle saving the assistant's response
        const finalizeAssistantMessage = async (error) => {
            try {
                // Create assistant message
                const assistantMessage = {
                    id: assistantMessageId,
                    role: 'assistant',
                    content: error ? `Error: ${error instanceof Error ? error.message : String(error)}` : fullResponse,
                    timestamp: Date.now(),
                };
                // Update thread with assistant message and context IDs
                thread.messages.push(assistantMessage);
                thread.inProgress = false;
                thread.updatedAt = Date.now();
                // Update context IDs if available
                if (responseContextIds) {
                    thread.metadata.contextIds = responseContextIds;
                }
                // Update title if available and thread is new
                if (finalTitle && (thread.title === 'New Chat' || !thread.title)) {
                    thread.title = finalTitle;
                }
                // Save thread and emit update event
                await this.saveThread(thread);
                this.emit('threadUpdate', thread);
                onEvent({ type: 'done' });
            }
            catch (saveError) {
                console.error('Error finalizing assistant message:', saveError);
                onEvent({
                    type: 'error',
                    message: `Error saving response: ${saveError instanceof Error ? saveError.message : String(saveError)}`
                });
            }
        };
        try {
            const selectedModelConfig = this.models[model];
            if (!selectedModelConfig) {
                throw new AIModelError(`Model ${model} not found or supported.`, ErrorCode.MODEL_NOT_FOUND);
            }
            // TODO: Implement image upload if Gemini API requires it separately
            // For now, assuming images are handled within the main request if supported
            const imageUploads = [];
            if (images && images.length > 0) {
                onEvent({ type: 'info', message: `Uploading ${images.length} image(s)...` });
                // Placeholder for actual image upload logic
                // const uploadedUrls = await Promise.all(images.map(img => this.uploadImage(img)));
                // imageUploads = uploadedUrls.map(url => [url, 1, "image/jpeg"]); // Example structure
                onEvent({ type: 'warning', message: 'Image upload not yet fully implemented for Gemini Web.' });
            }
            const messageStruct = [
                prompt,
                0, // Use 0 for text prompts, adjust if images change this
                null,
                imageUploads, // Include uploaded image data here
                null, // Unknown
                [], // Unknown
                null, // Unknown
                null, // Unknown
                0, // Unknown
                null, // Unknown
                [], // Unknown
                1, // Unknown
            ];
            const payloadStruct = [
                null, // Unknown
                JSON.stringify([
                    messageStruct,
                    null, // Unknown
                    metadata.contextIds, // Crucial: [conversationId, responseId, choiceId]
                    null, // Unknown
                    null, // Unknown
                    null, // Unknown
                    null, // Unknown
                    null, // Unknown
                    null, // Unknown
                    null, // Unknown
                    null, // Unknown
                    null, // Unknown
                    [1], // Unknown
                    0, // Unknown
                    [], // Unknown
                    [], // Unknown
                    1, // Unknown
                    selectedModelConfig['x-goog-ext-525001261-jspb'] // Model specific config
                ])
            ];
            const requestBody = new URLSearchParams({
                'f.req': JSON.stringify([payloadStruct]),
                'at': metadata.requestParams.atValue,
                '': '' // This seems necessary based on observed requests
            }).toString();
            const apiUrl = `https://gemini.google.com/_/BardChatUi/data/assistant.lamda.BardFrontendService/StreamGenerate?${new URLSearchParams({
                'bl': metadata.requestParams.blValue,
                'rt': 'c',
                '_reqid': String(generateReqId()),
                // Add other query params if necessary
            })}`;
            const response = await ofetch(apiUrl, {
                method: 'POST',
                headers: this.getHeaders(),
                body: requestBody,
                signal: signal,
                // Response is streaming, handle line by line
                responseType: 'text',
                onResponse: ({ response }) => {
                    if (!response.body) {
                        throw new AIModelError('No response body received', ErrorCode.NETWORK_ERROR);
                    }
                    const reader = response.body.getReader();
                    const decoder = new TextDecoder();
                    let buffer = '';
                    function processChunk() {
                        reader.read().then(({ done, value }) => {
                            if (done) {
                                if (buffer.length > 0) {
                                    // Process any remaining buffer content if needed
                                    console.warn('Stream ended with unprocessed buffer:', buffer);
                                }
                                onEvent({ type: 'info', message: 'Stream finished.' });
                                // Finalize message saving after stream ends
                                finalizeAssistantMessage();
                                return;
                            }
                            buffer += decoder.decode(value, { stream: true });
                            const lines = buffer.split('\n');
                            // Keep the last potentially incomplete line in the buffer
                            buffer = lines.pop() || '';
                            for (const line of lines) {
                                if (line.trim().length === 0)
                                    continue; // Skip empty lines
                                try {
                                    // Check for title in response chunks
                                    if (line.startsWith('[') && !finalTitle) {
                                        try {
                                            const data = JSON.parse(line);
                                            if (data[0][2]) {
                                                const payload = JSON.parse(data[0][2]);
                                                // Check for title at index 10
                                                if (payload[10] && Array.isArray(payload[10]) && payload[10][0]) {
                                                    // Found a title, remove trailing newline if present
                                                    finalTitle = payload[10][0].replace(/\\n$/, '');
                                                    console.log('Found conversation title:', finalTitle);
                                                    // Update thread title and emit event
                                                    if (finalTitle && (thread.title === 'New Chat' || !thread.title)) {
                                                        thread.title = finalTitle;
                                                        onEvent({
                                                            type: 'TITLE_UPDATE',
                                                            data: {
                                                                title: finalTitle,
                                                                threadId: thread.id
                                                            }
                                                        });
                                                    }
                                                }
                                            }
                                        }
                                        catch (e) {
                                            // Ignore parsing errors for title detection
                                        }
                                    }
                                    // Skip the initial `)]}'` line and lines containing only numbers (lengths)
                                    if (line.startsWith(')]}\'') || /^[0-9]+$/.test(line.trim())) {
                                        continue;
                                    }
                                    const parsedLine = JSON.parse(line);
                                    // Example parsing logic based on observed structure:
                                    // [[ "wrb.fr", null, "[JSON_PAYLOAD]" ]]
                                    if (Array.isArray(parsedLine) && parsedLine.length > 0 && Array.isArray(parsedLine[0]) && parsedLine[0].length >= 3) {
                                        const innerJsonString = parsedLine[0][2];
                                        if (typeof innerJsonString === 'string') {
                                            const payload = JSON.parse(innerJsonString);
                                            // Check for title (index 10)
                                            if (payload && payload[10] && Array.isArray(payload[10]) && typeof payload[10][0] === 'string') {
                                                const titleCandidate = payload[10][0].replace(/\n$/, ''); // Remove trailing newline
                                                if (titleCandidate && titleCandidate !== thread.title) {
                                                    finalTitle = titleCandidate;
                                                    onEvent({ type: 'title', title: finalTitle });
                                                }
                                            }
                                            // Check for main response content (index 4 -> 0 -> 1 -> 0)
                                            if (payload && payload[4] && payload[4][0] && payload[4][0][1] && typeof payload[4][0][1][0] === 'string') {
                                                const textChunk = payload[4][0][1][0];
                                                fullResponse = textChunk; // Gemini seems to send the full text in each relevant chunk
                                                onEvent({ type: 'chunk', text: fullResponse });
                                            }
                                            // Check for context IDs (index 1)
                                            if (payload && payload[1] && Array.isArray(payload[1]) && payload[1].length === 3) {
                                                responseContextIds = payload[1];
                                            }
                                        }
                                    }
                                }
                                catch (e) {
                                    console.error('Error parsing stream line:', line, e);
                                    onEvent({ type: 'error', message: `Error parsing stream: ${e instanceof Error ? e.message : String(e)}` });
                                }
                            }
                            processChunk(); // Continue reading
                        }).catch(error => {
                            console.error('Stream reading error:', error);
                            onEvent({ type: 'error', message: `Stream error: ${error.message}` });
                            finalizeAssistantMessage(error); // Finalize with error
                        });
                    }
                    processChunk(); // Start processing the stream
                },
                onRequestError: ({ error }) => {
                    throw new AIModelError(`Network error: ${error.message}`, ErrorCode.NETWORK_ERROR);
                }
            });
        }
        catch (error) {
            console.error('Gemini send message error:', error);
            let errorCode = ErrorCode.UNKNOWN;
            if (error instanceof AIModelError) {
                errorCode = error.code;
            }
            else if (error.name === 'AbortError') {
                errorCode = ErrorCode.REQUEST_CANCELLED;
            }
            else if (error.data?.code) {
                // Handle potential API errors if structure is known
                errorCode = ErrorCode.API_ERROR;
            }
            const errorMessage = error instanceof Error ? error.message : String(error);
            onEvent({ type: 'error', message: errorMessage });
            finalizeAssistantMessage(error); // Finalize with error
            // Rethrow or handle as needed
            // throw new AIModelError(errorMessage, errorCode);
        }
    }
}
__decorate([
    serverOperation,
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], GeminiWebModel.prototype, "getConversation", null);
__decorate([
    serverOperation,
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], GeminiWebModel.prototype, "editTitle", null);
