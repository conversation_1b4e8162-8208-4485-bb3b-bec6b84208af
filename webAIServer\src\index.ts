import express, { Express, Request, Response } from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import dotenv from 'dotenv';
import winston from 'winston';
import path from 'path';
import { WebSocketManager } from './websocket';
import { ApiKeyManager } from './services/apiKeyManager';
import { createAuthMiddleware } from './middleware/auth';
import { createChatRouter } from './routes/chat';
import { createModelsRouter } from './routes/models';

// Load environment variables
dotenv.config();

// Configure logger
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  transports: [
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.simple()
      )
    }),
    new winston.transports.File({
      filename: process.env.LOG_FILE || 'webai-server.log',
      format: winston.format.json()
    })
  ]
});

// Initialize services
const apiKeyManager = new ApiKeyManager(
  path.join(__dirname, '../data/api-keys.json'),
  logger
);

const wsManager = new WebSocketManager(
  parseInt(process.env.WS_PORT || '3001'),
  logger
);

// Create Express app
const app: Express = express();
const PORT = parseInt(process.env.PORT || '3000');
const HOST = process.env.HOST || 'localhost';

// Middleware
app.use(helmet({
  contentSecurityPolicy: false // Disable CSP for SSE
}));

app.use(cors({
  origin: (process.env.CORS_ORIGINS || 'http://localhost:*').split(','),
  credentials: true,
  methods: ['GET', 'POST', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));

app.use(express.json({ limit: '10mb' }));
app.use(morgan('combined', { stream: { write: (message) => logger.info(message.trim()) } }));

// Health check endpoint (no auth required)
app.get('/health', (req: Request, res: Response) => {
  res.json({
    status: 'healthy',
    version: '1.0.0',
    extensionConnected: wsManager.isConnected(),
    clientsConnected: wsManager.getClientCount(),
    timestamp: new Date().toISOString()
  });
});

// API info endpoint (no auth required)
app.get('/', (req: Request, res: Response) => {
  res.json({
    name: 'WebAI Server',
    version: '1.0.0',
    description: 'OpenAI-compatible API server for WebAI Chrome Extension',
    endpoints: {
      health: '/health',
      models: '/v1/models',
      chat: '/v1/chat/completions'
    },
    documentation: 'https://github.com/yourusername/webai-server'
  });
});

// API key management endpoints (for development/testing)
if (process.env.NODE_ENV === 'development') {
  app.post('/api/keys/generate', async (req: Request, res: Response) => {
    const { name, rateLimit } = req.body;
    const key = await apiKeyManager.generateKey(name || 'Generated Key', rateLimit);
    res.json({ key });
  });

  app.get('/api/keys', async (req: Request, res: Response) => {
    const keys = await apiKeyManager.listKeys();
    res.json({ keys });
  });
}

// Create auth middleware
const authMiddleware = createAuthMiddleware(apiKeyManager, logger);

// API v1 routes
const apiV1 = express.Router();

// Models endpoint (no auth required for listing models)
apiV1.use('/models', createModelsRouter(wsManager, logger));

// Chat endpoint (requires auth)
apiV1.use('/chat', authMiddleware, createChatRouter(wsManager, apiKeyManager, logger));

// Mount API routes
app.use('/v1', apiV1);

// Error handling middleware
app.use((err: any, req: Request, res: Response, next: any) => {
  logger.error('Unhandled error:', err);
  res.status(500).json({
    error: {
      message: 'Internal server error',
      type: 'internal_error',
      param: null,
      code: 'internal_error'
    }
  });
});

// 404 handler
app.use((req: Request, res: Response) => {
  res.status(404).json({
    error: {
      message: `Endpoint ${req.path} not found`,
      type: 'invalid_request_error',
      param: null,
      code: 'endpoint_not_found'
    }
  });
});

// WebSocket event handlers
wsManager.on('client-disconnected', (clientId: string) => {
  logger.info(`Extension disconnected: ${clientId}`);
});

wsManager.on('model-status', (status: any) => {
  logger.debug('Model status update:', status);
});

// Start server
async function start() {
  try {
    // Ensure default API key exists
    await apiKeyManager.ensureDefaultKey();

    // Start Express server
    app.listen(PORT, HOST, () => {
      logger.info(`🚀 Server running at http://${HOST}:${PORT}`);
      logger.info(`📡 WebSocket server running on port ${process.env.WS_PORT || 3001}`);
      logger.info(`📚 API Documentation: http://${HOST}:${PORT}`);
      logger.info(`🏥 Health Check: http://${HOST}:${PORT}/health`);
      
      console.log('\n' + '='.repeat(60));
      console.log('  WebAI Server Started Successfully!');
      console.log('='.repeat(60));
      console.log(`\n  API Endpoint: http://${HOST}:${PORT}/v1/chat/completions`);
      console.log(`  Models List:  http://${HOST}:${PORT}/v1/models`);
      console.log(`  WebSocket:    ws://${HOST}:${process.env.WS_PORT || 3001}`);
      console.log('\n  Please ensure the WebAI extension is installed and running.');
      console.log('='.repeat(60) + '\n');
    });
  } catch (error) {
    logger.error('Failed to start server:', error);
    process.exit(1);
  }
}

// Handle graceful shutdown
process.on('SIGTERM', () => {
  logger.info('SIGTERM received, shutting down gracefully...');
  wsManager.close();
  process.exit(0);
});

process.on('SIGINT', () => {
  logger.info('SIGINT received, shutting down gracefully...');
  wsManager.close();
  process.exit(0);
});

// Start the server
start().catch(error => {
  logger.error('Fatal error:', error);
  process.exit(1);
});
