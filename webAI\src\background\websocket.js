/**
 * WebSocket client for communication with the server
 */

import { nanoid } from 'nanoid';

export class WebSocketClient {
  constructor() {
    this.ws = null;
    this.url = null;
    this.reconnectTimeout = null;
    this.pingInterval = null;
    this.messageQueue = [];
    this.listeners = new Map();
    this.isConnecting = false;
  }

  /**
   * Connect to WebSocket server
   */
  async connect(url) {
    if (this.isConnecting || (this.ws && this.ws.readyState === WebSocket.OPEN)) {
      console.log('[WebSocket] Already connected or connecting');
      return;
    }

    this.isConnecting = true;
    this.url = url;

    return new Promise((resolve, reject) => {
      try {
        console.log(`[WebSocket] Connecting to ${url}`);
        this.ws = new WebSocket(url);

        this.ws.onopen = () => {
          console.log('[WebSocket] Connected');
          this.isConnecting = false;
          this.emit('connected');
          this.startPingInterval();
          this.flushMessageQueue();
          resolve();
        };

        this.ws.onmessage = (event) => {
          try {
            const message = JSON.parse(event.data);
            console.log('[WebSocket] Received:', message.type);
            this.emit('message', message);
          } catch (error) {
            console.error('[WebSocket] Error parsing message:', error);
          }
        };

        this.ws.onerror = (error) => {
          console.error('[WebSocket] Error:', error);
          this.isConnecting = false;
          this.emit('error', error);
          reject(error);
        };

        this.ws.onclose = () => {
          console.log('[WebSocket] Disconnected');
          this.isConnecting = false;
          this.stopPingInterval();
          this.emit('disconnected');
          this.scheduleReconnect();
        };

      } catch (error) {
        console.error('[WebSocket] Connection failed:', error);
        this.isConnecting = false;
        reject(error);
      }
    });
  }

  /**
   * Disconnect from WebSocket server
   */
  async disconnect() {
    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout);
      this.reconnectTimeout = null;
    }

    this.stopPingInterval();

    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }

    this.emit('disconnected');
  }

  /**
   * Send message to server
   */
  send(message) {
    const wrappedMessage = {
      id: nanoid(),
      timestamp: Date.now(),
      ...message
    };

    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      try {
        this.ws.send(JSON.stringify(wrappedMessage));
        console.log('[WebSocket] Sent:', wrappedMessage.type);
      } catch (error) {
        console.error('[WebSocket] Send error:', error);
        this.messageQueue.push(wrappedMessage);
      }
    } else {
      console.log('[WebSocket] Queuing message (not connected)');
      this.messageQueue.push(wrappedMessage);
    }
  }

  /**
   * Check if connected
   */
  isConnected() {
    return this.ws && this.ws.readyState === WebSocket.OPEN;
  }

  /**
   * Get current WebSocket URL
   */
  getUrl() {
    return this.url;
  }

  /**
   * Add event listener
   */
  on(event, callback) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event).push(callback);
  }

  /**
   * Remove event listener
   */
  off(event, callback) {
    if (this.listeners.has(event)) {
      const callbacks = this.listeners.get(event);
      const index = callbacks.indexOf(callback);
      if (index !== -1) {
        callbacks.splice(index, 1);
      }
    }
  }

  /**
   * Emit event
   */
  emit(event, ...args) {
    if (this.listeners.has(event)) {
      const callbacks = this.listeners.get(event);
      callbacks.forEach(callback => {
        try {
          callback(...args);
        } catch (error) {
          console.error(`[WebSocket] Error in ${event} listener:`, error);
        }
      });
    }
  }

  /**
   * Start ping interval to keep connection alive
   */
  startPingInterval() {
    this.stopPingInterval();
    
    this.pingInterval = setInterval(() => {
      if (this.isConnected()) {
        this.send({ type: 'ping' });
      }
    }, 30000); // Ping every 30 seconds
  }

  /**
   * Stop ping interval
   */
  stopPingInterval() {
    if (this.pingInterval) {
      clearInterval(this.pingInterval);
      this.pingInterval = null;
    }
  }

  /**
   * Schedule reconnection attempt
   */
  scheduleReconnect() {
    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout);
    }

    this.reconnectTimeout = setTimeout(() => {
      console.log('[WebSocket] Attempting to reconnect...');
      this.connect(this.url).catch(error => {
        console.error('[WebSocket] Reconnection failed:', error);
      });
    }, 5000); // Reconnect after 5 seconds
  }

  /**
   * Flush queued messages
   */
  flushMessageQueue() {
    while (this.messageQueue.length > 0 && this.isConnected()) {
      const message = this.messageQueue.shift();
      try {
        this.ws.send(JSON.stringify(message));
        console.log('[WebSocket] Sent queued message:', message.type);
      } catch (error) {
        console.error('[WebSocket] Error sending queued message:', error);
        this.messageQueue.unshift(message); // Put it back
        break;
      }
    }
  }
}
