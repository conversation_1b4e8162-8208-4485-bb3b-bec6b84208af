import { Request, Response, NextFunction } from 'express';
import { ApiKeyManager } from '../services/apiKeyManager';
import winston from 'winston';

export interface AuthenticatedRequest extends Request {
  apiKey?: string;
  apiKeyInfo?: any;
}

export function createAuthMiddleware(
  apiKeyManager: ApiKeyManager,
  logger: winston.Logger
) {
  return async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    // Extract API key from Authorization header
    const authHeader = req.headers.authorization;
    
    if (!authHeader) {
      return res.status(401).json({
        error: {
          message: 'Missing Authorization header',
          type: 'authentication_error',
          param: null,
          code: 'missing_auth_header'
        }
      });
    }

    let apiKey: string;

    // Support both "Bearer sk-..." and "Bearer sk-webai-..." formats
    if (authHeader.startsWith('Bearer ')) {
      apiKey = authHeader.substring(7);
    } else {
      return res.status(401).json({
        error: {
          message: 'Invalid Authorization header format. Use: Bearer YOUR_API_KEY',
          type: 'authentication_error',
          param: null,
          code: 'invalid_auth_header'
        }
      });
    }

    // Validate API key
    const isValid = await apiKeyManager.validateKey(apiKey);
    
    if (!isValid) {
      logger.warn(`Invalid API key attempt: ${apiKey.substring(0, 12)}...`);
      return res.status(401).json({
        error: {
          message: 'Invalid API key',
          type: 'authentication_error',
          param: null,
          code: 'invalid_api_key'
        }
      });
    }

    // Attach API key info to request
    req.apiKey = apiKey;
    req.apiKeyInfo = apiKeyManager.getKeyInfo(apiKey);

    next();
  };
}
