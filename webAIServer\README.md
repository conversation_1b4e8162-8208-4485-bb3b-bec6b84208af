# WebAI Server

OpenAI-compatible API server that bridges AI web services through the WebAI Chrome Extension.

## 🚀 Features

- **OpenAI-Compatible API**: Drop-in replacement for OpenAI API endpoints
- **Multiple AI Providers**: Support for <PERSON>, <PERSON>, <PERSON> Copilot, Perplexity, and Deepseek
- **Streaming Support**: Real-time streaming responses via Server-Sent Events (SSE)
- **API Key Management**: Secure API key generation and validation
- **WebSocket Communication**: Real-time connection with Chrome extension
- **Model Registry**: Comprehensive model mapping and capabilities tracking
- **Rate Limiting**: Built-in rate limiting per API key
- **Production Ready**: Logging, error handling, and health checks

## 📋 Prerequisites

- Node.js 18+ 
- npm or yarn
- WebAI Chrome Extension installed and running

## 🛠️ Installation

1. Clone the repository:
```bash
git clone https://github.com/yourusername/webai-server.git
cd webai-server
```

2. Install dependencies:
```bash
npm install
```

3. Create environment file:
```bash
cp .env.example .env
```

4. Build the TypeScript code:
```bash
npm run build
```

## ⚙️ Configuration

Edit `.env` file to configure the server:

```env
# Server Configuration
PORT=3000                 # HTTP server port
HOST=localhost           # Server host

# WebSocket Configuration  
WS_PORT=3001             # WebSocket server port

# Security
CORS_ORIGINS=http://localhost:*,http://127.0.0.1:*
RATE_LIMIT_WINDOW_MS=60000    # Rate limit window (ms)
RATE_LIMIT_MAX_REQUESTS=100   # Max requests per window

# Logging
LOG_LEVEL=info           # Log level (debug, info, warn, error)
LOG_FILE=webai-server.log

# Development
NODE_ENV=development     # Environment (development, production)
```

## 🚀 Usage

### Starting the Server

**Development mode** (with auto-reload):
```bash
npm run dev
```

**Production mode**:
```bash
npm start
```

The server will:
1. Start HTTP server on port 3000
2. Start WebSocket server on port 3001
3. Generate a default API key (displayed in console)
4. Wait for WebAI extension connection

### API Endpoints

#### Chat Completions
```bash
POST http://localhost:3000/v1/chat/completions
Authorization: Bearer YOUR_API_KEY
Content-Type: application/json

{
  "model": "gpt-4",
  "messages": [
    {"role": "user", "content": "Hello!"}
  ],
  "stream": false
}
```

#### List Models
```bash
GET http://localhost:3000/v1/models
```

#### Get Model Details
```bash
GET http://localhost:3000/v1/models/gpt-4
```

#### Health Check
```bash
GET http://localhost:3000/health
```

## 📦 Available Models

| Model ID | Provider | Description |
|----------|----------|-------------|
| `gpt-4` | Claude Web | Claude with default style |
| `gpt-4-turbo` | Gemini | Gemini 2.0 Flash |
| `gpt-3.5-turbo` | Bing | Bing Copilot Chat |
| `claude-3-opus` | Claude Web | Claude default style |
| `claude-3-sonnet` | Claude Web | Claude concise style |
| `gemini-pro` | Gemini | Gemini 2.5 Pro Experimental |
| `gemini-flash` | Gemini | Gemini 2.0 Flash |
| `deepseek-chat` | Deepseek | Deepseek Chat Mode |
| `deepseek-reasoning` | Deepseek | Deepseek Reasoning Mode |
| `perplexity-online` | Perplexity | Internet search mode |
| `bing-creative` | Bing | Creative mode |

## 🔑 API Key Management

### Generate New API Key (Development Mode)
```bash
curl -X POST http://localhost:3000/api/keys/generate \
  -H "Content-Type: application/json" \
  -d '{"name": "My App", "rateLimit": {"requests": 100, "window": 60000}}'
```

### List API Keys (Development Mode)
```bash
curl http://localhost:3000/api/keys
```

## 🔌 Using with AI Tools

### Cline (VS Code Extension)

1. Install Cline extension in VS Code
2. Open settings and select "OpenAI Compatible" 
3. Set base URL: `http://localhost:3000/v1`
4. Enter your API key
5. Select a model from the list

### Continue.dev

Add to `~/.continue/config.json`:
```json
{
  "models": [{
    "title": "WebAI",
    "provider": "openai",
    "model": "gpt-4",
    "apiBase": "http://localhost:3000/v1",
    "apiKey": "YOUR_API_KEY"
  }]
}
```

### LangChain

```python
from langchain.chat_models import ChatOpenAI

llm = ChatOpenAI(
    model="gpt-4",
    openai_api_base="http://localhost:3000/v1",
    openai_api_key="YOUR_API_KEY"
)
```

### OpenAI Python SDK

```python
import openai

openai.api_base = "http://localhost:3000/v1"
openai.api_key = "YOUR_API_KEY"

response = openai.ChatCompletion.create(
    model="gpt-4",
    messages=[{"role": "user", "content": "Hello!"}]
)
```

## 🐛 Troubleshooting

### Extension Not Connected

If you see "Extension not connected" error:
1. Ensure WebAI Chrome extension is installed
2. Check extension is enabled and running
3. Verify WebSocket port (3001) is not blocked
4. Check browser console for connection errors

### Rate Limit Exceeded

If you hit rate limits:
1. Wait for the rate limit window to pass
2. Generate a new API key with higher limits
3. Distribute requests across multiple keys

### Model Not Available

If a model shows as unavailable:
1. Ensure you're logged into the corresponding service in Chrome
2. Check the extension settings page
3. Verify cookies/session are valid

## 📊 Monitoring

### Logs

Logs are written to:
- Console (colored output)
- `webai-server.log` file (JSON format)

### Health Monitoring

```bash
# Check server health
curl http://localhost:3000/health

# Response
{
  "status": "healthy",
  "version": "1.0.0",
  "extensionConnected": true,
  "clientsConnected": 1,
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

## 🔒 Security

- API keys are stored locally in `data/api-keys.json`
- All requests require valid API key authentication
- Rate limiting prevents abuse
- CORS configured for localhost only by default
- No external data storage or tracking

## 📝 Development

### Project Structure
```
webAIServer/
├── src/
│   ├── index.ts           # Main entry point
│   ├── websocket.ts       # WebSocket manager
│   ├── routes/            # API route handlers
│   ├── services/          # Business logic
│   ├── middleware/        # Express middleware
│   └── types/             # TypeScript types
├── data/                  # Runtime data (gitignored)
├── logs/                  # Log files (gitignored)
└── dist/                  # Compiled JavaScript
```

### Scripts

- `npm run dev` - Start development server with hot reload
- `npm run build` - Build TypeScript to JavaScript
- `npm run start` - Start production server
- `npm run lint` - Run ESLint
- `npm run test` - Run tests

## 🤝 Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

MIT License - see LICENSE file for details

## 🙏 Acknowledgments

- Built to work with the WebAI Chrome Extension
- OpenAI API specification for compatibility
- All supported AI service providers

## 📞 Support

For issues and questions:
- GitHub Issues: [Create an issue](https://github.com/yourusername/webai-server/issues)
- Documentation: [Wiki](https://github.com/yourusername/webai-server/wiki)
