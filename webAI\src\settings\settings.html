<!doctype html><html lang="en"><head><meta charset="UTF-8"><meta name="viewport" content="width=device-width,initial-scale=1"><title>WebAI Extension Settings</title><link rel="stylesheet" href="settings.css"><script defer="defer" src="../../dist/settings.bundle.js"></script><script defer="defer" src="../../dist/settings.bundle.js"></script><script defer="defer" src="../../dist/settings.bundle.js"></script><script defer="defer" src="../../dist/settings.bundle.js"></script><script defer="defer" src="../../dist/settings.bundle.js"></script></head><body><div class="container"><header class="header"><h1>WebAI Extension Settings</h1><p class="subtitle">Configure your AI models and server connection</p></header><div class="content"><section class="section"><h2>Connection Settings</h2><div class="form-group"><label for="serverUrl">Server URL:</label> <input id="serverUrl" placeholder="http://localhost:3000"> <small>The URL of your WebAI server</small></div><div class="form-group"><label for="wsUrl">WebSocket URL:</label> <input id="wsUrl" placeholder="ws://localhost:3001"> <small>WebSocket URL for real-time communication</small></div><div class="form-group"><label for="apiKey">API Key:</label><div class="input-group"><input type="password" id="apiKey" placeholder="Enter your API key"> <button type="button" id="generateApiKey" class="btn btn-secondary">Generate</button> <button type="button" id="toggleApiKey" class="btn btn-secondary">👁️</button></div><small>API key for authenticating with the server</small></div><div class="form-group"><label><input type="checkbox" id="autoConnect"> Auto-connect on startup</label></div></section><section class="section"><h2>Model Management</h2><div class="model-controls"><button type="button" id="refreshModels" class="btn btn-primary">Refresh Models</button> <button type="button" id="testConnection" class="btn btn-secondary">Test Connection</button></div><div id="modelsList" class="models-list"><div class="loading">Loading models...</div></div></section><section class="section"><h2>Authentication Tokens</h2><p class="section-description">Manage authentication tokens for web-based AI services. These tokens are automatically retrieved when needed.</p><div id="authTokensList" class="auth-tokens-list"><div class="loading">Loading tokens...</div></div><div class="auth-controls"><button type="button" id="clearAllTokens" class="btn btn-danger">Clear All Tokens</button></div></section><section class="section"><h2>Advanced Settings</h2><div class="form-group"><label><input type="checkbox" id="enableLogging"> Enable debug logging</label> <small>Show detailed logs in the browser console</small></div><div class="form-group"><label for="maxConversations">Max Conversations:</label> <input type="number" id="maxConversations" min="1" max="100" value="10"> <small>Maximum number of conversation threads to keep in memory</small></div></section><section class="section"><h2>Statistics</h2><div id="statsDisplay" class="stats-display"><div class="loading">Loading statistics...</div></div></section><section class="section"><h2>Data Management</h2><div class="data-controls"><button type="button" id="exportConfig" class="btn btn-secondary">Export Configuration</button> <button type="button" id="importConfig" class="btn btn-secondary">Import Configuration</button> <input type="file" id="importFile" accept=".json" style="display: none;"> <button type="button" id="clearAllData" class="btn btn-danger">Clear All Data</button></div></section></div><footer class="footer"><div class="status-bar"><span id="connectionStatus" class="status-indicator">Disconnected</span> <span id="lastSaved" class="last-saved"></span></div><div class="action-buttons"><button type="button" id="saveSettings" class="btn btn-primary">Save Settings</button> <button type="button" id="resetSettings" class="btn btn-secondary">Reset to Defaults</button></div></footer></div><div id="confirmModal" class="modal"><div class="modal-content"><h3 id="modalTitle">Confirm Action</h3><p id="modalMessage">Are you sure you want to proceed?</p><div class="modal-buttons"><button type="button" id="modalConfirm" class="btn btn-primary">Confirm</button> <button type="button" id="modalCancel" class="btn btn-secondary">Cancel</button></div></div></div><div id="toast" class="toast"></div><script src="../../dist/settings.bundle.js"></script></body></html>