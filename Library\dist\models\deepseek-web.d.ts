import { AbstractModel } from './abstract-model';
import { StatusEvent } from './types';
/**
 * Metadata specific to DeepSeek Web threads.
 */
interface DeepseekThreadMetadata {
    conversationId: string;
    organizationId?: string;
    lastMessageId?: number | string | null;
}
export declare class DeepseekWebModel extends AbstractModel {
    private authToken?;
    private readonly DEEPSEEK_HOST;
    constructor();
    private initialize;
    /**
     * Ensures the authentication token is loaded and valid. Throws if not available after trying.
     */
    private ensureAuthToken;
    /**
     * Initializes local storage for threads and validates existing ones.
     */
    private initializeStorage;
    /**
     * Validates metadata of existing threads stored locally.
     */
    private validateExistingThreads;
    /**
     * Type guard to check if metadata is valid for DeepSeek.
     */
    private isValidDeepseekMetadata;
    /**
     * Retrieves valid DeepSeek metadata from the current thread, throws if invalid.
     */
    private getDeepseekMetadata;
    /**
     * Ensures a thread is loaded, fetching the most recent or creating a new one.
     */
    private ensureThreadLoaded;
    /**
     * Gets the current thread or throws an error if none exists.
     */
    private getCurrentThreadSafe;
    getName(): string;
    supportsImageInput(): boolean;
    /**
     * Creates standard headers for DeepSeek API requests.
     */
    /**
     * Creates standard headers for DeepSeek API requests.
     */
    private getHeaders;
    /**
     * Helper to fetch and solve the PoW challenge for /api/v0/chat/completion
     * @serverOperation Interacts with DeepSeek's PoW endpoint.
     */
    private getPowSolutionForCompletion;
    /**
    * Wrapper around ofetch to handle DeepSeek specific challenges and errors.
    */
    private makeDeepseekRequest;
    /**
     * Fetches current user information.
     * @serverOperation Interacts with DeepSeek's session fetching endpoint.
     */
    getUserInfo(): Promise<any>;
    /**
     * Fetches a page of conversation sessions.
     * @serverOperation Interacts with DeepSeek's session fetching endpoint.
     */
    getAllConversationsData(count?: number): Promise<any>;
    /**
     * Fetches platform version and status information.
     * @serverOperation Interacts with DeepSeek's version/status endpoints.
     */
    getPlatformData(): Promise<[string | null, any | null]>;
    /**
     * Creates a new conversation session on the server.
     */
    private createConversation;
    /**
     * Initializes a new local and server-side conversation thread.
     */
    initNewThread(): Promise<void>;
    /**
     * Implements the core logic for sending a message and processing the response stream.
     * Uses the same event and error handling conventions as Claude/Gemini.
     */
    protected doSendMessage(params: {
        prompt: string;
        images?: File[] | undefined;
        signal?: AbortSignal | undefined;
        mode?: string;
        searchEnabled?: boolean;
        onEvent: (event: StatusEvent) => void;
    }): Promise<void>;
    /**
     * Updates the title of a DeepSeek conversation.
     * @param newTitle The new title to set for the conversation
     * @param options Optional config: loadThread (default true), metadata (optional), tryUpdateThread (default true)
     * @serverOperation This method makes direct API calls to DeepSeek's servers
     */
    editTitle(newTitle: string, options?: {
        loadThread?: boolean;
        metadata?: DeepseekThreadMetadata;
        tryUpdateThread?: boolean;
    }): Promise<void>;
    /**
     * Deletes one or more conversations from DeepSeek's servers.
     * @param threadIds Array of thread IDs (local thread IDs, not chat_session_id)
     * @param updateLocalThread If true, also delete the thread from local storage upon successful server deletion.
     * @param createNewThreadAfterDelete If true and the currently active thread is deleted locally, initialize a new thread.
     * @serverOperation This method makes direct API calls to DeepSeek's servers.
     */
    deleteServerThreads(threadIds: string[], updateLocalThread?: boolean, createNewThreadAfterDelete?: boolean): Promise<void>;
    /**
     * Uploads a file to Deepseek (supports images and certain file types).
     * If the file is an image, only extracted text is used; if no text, returns error.
     * Returns the file id if successful and usable, or throws on error/unsupported.
     */
    uploadFile(file: File): Promise<string>;
    /**
     * Polls Deepseek for file processing status until terminal state.
     */
    private pollFileStatus;
}
export {};
