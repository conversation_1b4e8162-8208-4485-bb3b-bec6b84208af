{"version": 3, "file": "RuleTester.d.ts", "sourceRoot": "", "sources": ["../../src/ts-eslint/RuleTester.ts"], "names": [], "mappings": "AAEA,OAAO,KAAK,EAAE,cAAc,EAAE,eAAe,EAAE,MAAM,cAAc,CAAC;AACpE,OAAO,KAAK,EAAE,aAAa,EAAE,MAAM,UAAU,CAAC;AAC9C,OAAO,KAAK,EAAE,MAAM,EAAE,MAAM,UAAU,CAAC;AACvC,OAAO,KAAK,EAAE,aAAa,EAAE,MAAM,iBAAiB,CAAC;AACrD,OAAO,KAAK,EACV,2BAA2B,EAC3B,kBAAkB,EAClB,UAAU,EACV,2BAA2B,EAC5B,MAAM,QAAQ,CAAC;AAEhB,UAAU,aAAa,CAAC,OAAO,SAAS,SAAS,OAAO,EAAE;IACxD;;OAEG;IACH,QAAQ,CAAC,IAAI,CAAC,EAAE,MAAM,CAAC;IACvB;;OAEG;IACH,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC;IACtB;;OAEG;IACH,QAAQ,CAAC,GAAG,CAAC,EAAE,QAAQ,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC;IAClD;;OAEG;IACH,QAAQ,CAAC,QAAQ,CAAC,EAAE,MAAM,CAAC;IAC3B;;OAEG;IACH,QAAQ,CAAC,OAAO,CAAC,EAAE,QAAQ,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;IAClD;;OAEG;IACH,QAAQ,CAAC,OAAO,CAAC,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC;IACrC;;OAEG;IACH,QAAQ,CAAC,MAAM,CAAC,EAAE,MAAM,CAAC;IACzB;;OAEG;IACH,QAAQ,CAAC,aAAa,CAAC,EAAE,QAAQ,CAAC,aAAa,CAAC,CAAC;IACjD;;OAEG;IACH,QAAQ,CAAC,QAAQ,CAAC,EAAE,QAAQ,CAAC,2BAA2B,CAAC,CAAC;IAC1D;;OAEG;IACH,QAAQ,CAAC,IAAI,CAAC,EAAE,OAAO,CAAC;CACzB;AAED,UAAU,gBAAgB,CAAC,UAAU,SAAS,MAAM;IAClD;;OAEG;IACH,QAAQ,CAAC,SAAS,EAAE,UAAU,CAAC;IAC/B;;OAEG;IACH,QAAQ,CAAC,IAAI,CAAC,EAAE,2BAA2B,CAAC;IAC5C;;;OAGG;IACH,QAAQ,CAAC,MAAM,EAAE,MAAM,CAAC;CAIzB;AAED,UAAU,eAAe,CACvB,UAAU,SAAS,MAAM,EACzB,OAAO,SAAS,SAAS,OAAO,EAAE,CAClC,SAAQ,aAAa,CAAC,OAAO,CAAC;IAC9B;;OAEG;IACH,QAAQ,CAAC,MAAM,EAAE,SAAS,aAAa,CAAC,UAAU,CAAC,EAAE,CAAC;IACtD;;OAEG;IACH,QAAQ,CAAC,MAAM,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;CACjC;AAED,UAAU,aAAa,CAAC,UAAU,SAAS,MAAM;IAC/C;;OAEG;IACH,QAAQ,CAAC,MAAM,CAAC,EAAE,MAAM,CAAC;IACzB;;OAEG;IACH,QAAQ,CAAC,IAAI,CAAC,EAAE,2BAA2B,CAAC;IAC5C;;OAEG;IACH,QAAQ,CAAC,SAAS,CAAC,EAAE,MAAM,CAAC;IAC5B;;OAEG;IACH,QAAQ,CAAC,OAAO,CAAC,EAAE,MAAM,CAAC;IAC1B;;OAEG;IACH,QAAQ,CAAC,IAAI,CAAC,EAAE,MAAM,CAAC;IACvB;;OAEG;IACH,QAAQ,CAAC,SAAS,EAAE,UAAU,CAAC;IAC/B;;OAEG;IACH,QAAQ,CAAC,WAAW,CAAC,EAAE,SAAS,gBAAgB,CAAC,UAAU,CAAC,EAAE,GAAG,IAAI,CAAC;IACtE;;OAEG;IACH,QAAQ,CAAC,IAAI,CAAC,EAAE,cAAc,GAAG,eAAe,CAAC;CAIlD;AAED;;GAEG;AACH,KAAK,+BAA+B,GAAG,CACrC,IAAI,EAAE,MAAM,EACZ,QAAQ,EAAE,MAAM,IAAI,KACjB,IAAI,CAAC;AAEV,UAAU,QAAQ,CAChB,UAAU,SAAS,MAAM,EACzB,OAAO,SAAS,SAAS,OAAO,EAAE;IAGlC,QAAQ,CAAC,KAAK,EAAE,SAAS,CAAC,aAAa,CAAC,OAAO,CAAC,GAAG,MAAM,CAAC,EAAE,CAAC;IAC7D,QAAQ,CAAC,OAAO,EAAE,SAAS,eAAe,CAAC,UAAU,EAAE,OAAO,CAAC,EAAE,CAAC;CACnE;AACD,UAAU,gBAAiB,SAAQ,aAAa,CAAC,MAAM;IAErD,QAAQ,CAAC,MAAM,EAAE,MAAM,CAAC;IACxB,QAAQ,CAAC,aAAa,CAAC,EAAE,QAAQ,CAAC,aAAa,CAAC,CAAC;CAClD;AAED,OAAO,OAAO,cAAc;IAC1B;;;OAGG;gBACS,YAAY,CAAC,EAAE,gBAAgB;IAE3C;;;;;OAKG;IACH,GAAG,CAAC,UAAU,SAAS,MAAM,EAAE,OAAO,SAAS,SAAS,OAAO,EAAE,EAC/D,QAAQ,EAAE,MAAM,EAChB,IAAI,EAAE,UAAU,CAAC,UAAU,EAAE,OAAO,CAAC,EACrC,KAAK,EAAE,QAAQ,CAAC,UAAU,EAAE,OAAO,CAAC,GACnC,IAAI;IAEP;;;OAGG;IACH,MAAM,KAAK,QAAQ,IAAI,+BAA+B,CAAC;IACvD,MAAM,KAAK,QAAQ,CAAC,KAAK,EAAE,+BAA+B,GAAG,SAAS,EAAE;IAExE;;;OAGG;IACH,MAAM,KAAK,EAAE,IAAI,+BAA+B,CAAC;IACjD,MAAM,KAAK,EAAE,CAAC,KAAK,EAAE,+BAA+B,GAAG,SAAS,EAAE;IAElE;;;OAGG;IACH,MAAM,KAAK,MAAM,IAAI,+BAA+B,CAAC;IACrD,MAAM,KAAK,MAAM,CAAC,KAAK,EAAE,+BAA+B,GAAG,SAAS,EAAE;IAEtE;;OAEG;IACH,UAAU,CAAC,UAAU,SAAS,MAAM,EAAE,OAAO,SAAS,SAAS,OAAO,EAAE,EACtE,IAAI,EAAE,MAAM,EACZ,IAAI,EACA,kBAAkB,CAAC,UAAU,EAAE,OAAO,CAAC,GACvC,UAAU,CAAC,UAAU,EAAE,OAAO,CAAC,GAClC,IAAI;CACR;;AAED,cAAM,UAAW,SAAQ,eAA2C;CAAG;AAEvE,OAAO,EACL,eAAe,EACf,gBAAgB,EAChB,UAAU,EACV,gBAAgB,EAChB,+BAA+B,EAC/B,QAAQ,EACR,aAAa,EACb,aAAa,GACd,CAAC"}