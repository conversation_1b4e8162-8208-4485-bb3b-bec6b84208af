# Server Configuration
PORT=3000
HOST=localhost

# WebSocket Configuration
WS_PORT=3001

# Security
CORS_ORIGINS=http://localhost:*,http://127.0.0.1:*
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX_REQUESTS=100

# Logging
LOG_LEVEL=info
LOG_FILE=webai-server.log

# API Configuration
API_PREFIX=/v1
DEFAULT_MODEL=gpt-3.5-turbo

# Extension Communication
EXTENSION_ID=your-extension-id-here
EXTENSION_SECRET=generate-a-secure-secret-here

# Development
NODE_ENV=development
DEBUG=false
