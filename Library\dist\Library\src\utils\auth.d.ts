export declare const AUTH_EVENTS: {
    TOKEN_REFRESH_START: string;
    TOKEN_REFRESH_COMPLETE: string;
    TOKEN_REFRESH_ERROR: string;
};
export declare function requestHostPermission(host: string): Promise<boolean>;
/**
 * Configure authentication notification preferences
 * @param options Configuration options
 */
export declare function configureAuth(options: {
    notifyTokenRefresh?: boolean;
}): void;
/**
 * Get all cookies for a specific domain
 */
export declare function getCookiesForDomain(domain: string): Promise<Record<string, string>>;
/**
 * Clear the auth cache for a specific service or all services
 * Note: This clears the chrome.storage cache used by getTokenFromWebsite
 */
export declare function clearAuthCache(serviceName?: string): Promise<void>;
/**
 * Function to inject into a DeepSeek tab to extract the token.
 */
export declare function deepseekExtractor(): string | null;
/**
 * Function to inject into a Copilot tab to extract the token.
 */
export declare function copilotExtractor(): string | null;
/**
 * Internal logic containing the core steps for token retrieval (tabs, temp window).
 * Should only be called from a context where browser APIs are available (e.g., background script).
 */
export declare function executeTokenRetrievalLogic(// Renamed slightly to emphasize internal use
serviceName: string, targetUrl: string, urlPattern: string, extractorFunc: () => string | null, // The actual function
forceNewTab: boolean): Promise<string | null>;
/**
 * Sends a message to the background script requesting token retrieval.
 * Caching logic should be handled by the caller (the Model) if needed.
 *
 * @param serviceName - Unique name for logging/cache keys (e.g., 'deepseek').
 * @param targetUrl - Base URL of the target website.
 * @param urlPattern - URL pattern for permissions/tab querying.
 * @param extractorName - The *name* (string) of the exported extractor function in this file.
 * @param forceRefresh - If true, tells background to skip existing tab check (will still hit cache in caller).
 * @returns The extracted token string, or null. Throws on communication errors.
 */
export declare function getTokenFromWebsite(serviceName: string, targetUrl: string, urlPattern: string, extractorName: 'deepseekExtractor' | 'copilotExtractor' | string, // Expects function name
forceRefresh?: boolean): Promise<string | null>;
