interface DeepSeekChallenge {
    algorithm: string;
    challenge: string;
    salt: string;
    signature: string;
    difficulty: number;
    expire_at: number;
    expire_after: number;
    target_path?: string;
}
/**
 * Solves the PoW challenge using the loaded WASM and logic from the working JS example.
 */
export declare function solveDeepSeekPowWasm(challengeObj: DeepSeekChallenge): Promise<string>;
export {};
