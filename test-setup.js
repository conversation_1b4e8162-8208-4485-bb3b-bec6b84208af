#!/usr/bin/env node

// Simple test script to verify the WebAI setup

console.log('🧪 Testing WebAI Setup');
console.log('=====================');

async function testServer() {
    console.log('\n1. Testing server health...');
    
    try {
        const response = await fetch('http://localhost:3000/health');
        if (response.ok) {
            const data = await response.json();
            console.log('✅ Server is running');
            console.log(`   Status: ${data.status}`);
            console.log(`   Uptime: ${data.uptime}s`);
            return true;
        } else {
            console.log('❌ Server responded with error:', response.status);
            return false;
        }
    } catch (error) {
        console.log('❌ Server is not running');
        console.log('   Please start the server with: npm start');
        return false;
    }
}

async function testAPI() {
    console.log('\n2. Testing API endpoints...');
    
    try {
        // Test without API key (should fail)
        const response1 = await fetch('http://localhost:3000/v1/models');
        if (response1.status === 401) {
            console.log('✅ API key authentication is working');
        } else {
            console.log('⚠️  API key authentication might be disabled');
        }
        
        // Test with demo API key
        const response2 = await fetch('http://localhost:3000/v1/models', {
            headers: {
                'Authorization': 'Bearer webai-demo-key-1234567890abcdef'
            }
        });
        
        if (response2.ok) {
            const data = await response2.json();
            console.log('✅ Models endpoint is working');
            console.log(`   Available models: ${data.data.length}`);
            data.data.forEach(model => {
                console.log(`   - ${model.id} (${model.owned_by})`);
            });
            return true;
        } else {
            console.log('❌ Models endpoint failed:', response2.status);
            return false;
        }
    } catch (error) {
        console.log('❌ API test failed:', error.message);
        return false;
    }
}

async function testChatCompletion() {
    console.log('\n3. Testing chat completion...');
    
    try {
        const response = await fetch('http://localhost:3000/v1/chat/completions', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': 'Bearer webai-demo-key-1234567890abcdef'
            },
            body: JSON.stringify({
                model: 'gemini-web',
                messages: [
                    { role: 'user', content: 'Hello, this is a test message' }
                ]
            })
        });
        
        if (response.ok) {
            const data = await response.json();
            console.log('✅ Chat completion is working');
            console.log(`   Model: ${data.model}`);
            console.log(`   Response: ${data.choices[0].message.content.substring(0, 100)}...`);
            return true;
        } else {
            const error = await response.json();
            console.log('❌ Chat completion failed:', error.error?.message || response.status);
            return false;
        }
    } catch (error) {
        console.log('❌ Chat completion test failed:', error.message);
        return false;
    }
}

async function main() {
    const serverOk = await testServer();
    
    if (serverOk) {
        const apiOk = await testAPI();
        if (apiOk) {
            await testChatCompletion();
        }
    }
    
    console.log('\n📋 Next Steps:');
    console.log('1. Install the WebAI browser extension');
    console.log('2. Generate a real API key in the extension');
    console.log('3. Use with tools like Cline, Continue, or custom apps');
    console.log('4. Base URL: http://localhost:3000');
    console.log('5. Available models: gemini-web, claude-web, bing-web, etc.');
    
    console.log('\n🎉 WebAI setup test completed!');
}

main().catch(console.error);