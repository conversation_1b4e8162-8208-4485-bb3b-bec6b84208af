<!doctype html><html lang="en"><head><meta charset="UTF-8"><meta name="viewport" content="width=device-width,initial-scale=1"><title>WebAI Extension</title><link rel="stylesheet" href="popup.css"><script defer="defer" src="../../dist/popup.bundle.js"></script><script defer="defer" src="../../dist/popup.bundle.js"></script><script defer="defer" src="../../dist/popup.bundle.js"></script><script defer="defer" src="../../dist/popup.bundle.js"></script><script defer="defer" src="../../dist/popup.bundle.js"></script></head><body><div class="popup-container"><header class="header"><div class="logo"><img src="../../icons/icon48.png" alt="WebAI" class="logo-icon"><h1>WebAI</h1></div><div class="status-indicator" id="statusIndicator"><span class="status-dot"></span> <span class="status-text">Connecting...</span></div></header><main class="content"><section class="section"><div class="connection-info"><div class="info-item"><span class="label">Server:</span> <span class="value" id="serverUrl">Not connected</span></div><div class="info-item"><span class="label">WebSocket:</span> <span class="value" id="wsStatus">Disconnected</span></div></div></section><section class="section"><h3>Quick Actions</h3><div class="action-buttons"><button class="btn btn-primary" id="testConnectionBtn"><span class="btn-icon">🔗</span> Test Connection</button> <button class="btn btn-secondary" id="refreshModelsBtn"><span class="btn-icon">🔄</span> Refresh Models</button></div></section><section class="section"><h3>Available Models</h3><div class="models-summary" id="modelsSummary"><div class="loading">Loading models...</div></div></section><section class="section"><h3>Statistics</h3><div class="stats-grid"><div class="stat-item"><div class="stat-value" id="messagesCount">0</div><div class="stat-label">Messages</div></div><div class="stat-item"><div class="stat-value" id="modelsCount">0</div><div class="stat-label">Models</div></div><div class="stat-item"><div class="stat-value" id="errorsCount">0</div><div class="stat-label">Errors</div></div></div></section><section class="section"><h3>Recent Activity</h3><div class="activity-list" id="activityList"><div class="activity-item"><span class="activity-text">No recent activity</span> <span class="activity-time"></span></div></div></section></main><footer class="footer"><div class="footer-buttons"><button class="btn btn-text" id="settingsBtn"><span class="btn-icon">⚙️</span> Settings</button> <button class="btn btn-text" id="helpBtn"><span class="btn-icon">❓</span> Help</button></div><div class="version-info"><span id="versionText">v1.0.0</span></div></footer></div><div class="loading-overlay" id="loadingOverlay"><div class="spinner"></div><div class="loading-text">Loading...</div></div><div class="toast" id="toast"></div><script src="../../dist/popup.bundle.js"></script></body></html>