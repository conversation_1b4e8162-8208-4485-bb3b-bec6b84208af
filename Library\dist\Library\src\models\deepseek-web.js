import { ofetch, FetchError } from 'ofetch';
import { v4 as uuid } from 'uuid';
// import Browser from 'webextension-polyfill';
import { getTokenFromWebsite } from '../utils/auth'; // <-- Import generic auth function
import { solvePowChallenge } from '../../../pow-solver'; // Import PoW solver
import { AbstractModel } from './abstract-model';
import { AIModelError, ErrorCode } from './types';
export class DeepseekWebModel extends AbstractModel {
    constructor() {
        super();
        this.DEEPSEEK_HOST = 'https://chat.deepseek.com';
        // Initialize asynchronously
        this.initialize().catch(err => {
            console.error('Failed to initialize DeepseekWebModel:', err);
            // Emit an error status if initialization fails critically
            // Note: emitStatus won't work here as onEvent callback isn't set yet.
            // Consider a different way to report constructor errors if needed.
        });
    }
    // --- Initialization ---
    async initialize() {
        // emitStatus requires onEvent context, cannot be used reliably here.
        console.log('Initializing DeepSeek model...');
        try {
            // Attempt to get token during initialization using the new utility
            const token = await getTokenFromWebsite('Deepseek', this.DEEPSEEK_HOST, `${this.DEEPSEEK_HOST}/*`, 'deepseekExtractor', // <-- Pass extractor name as string
            false // Don't force new tab on initial load
            );
            if (token) {
                this.authToken = `Bearer ${token}`;
                console.log('[Deepseek Init] Auth token loaded.');
            }
            else {
                console.warn('[Deepseek Init] Auth token not found during initial load.');
            }
            await this.initializeStorage();
            // Only log ready if all init steps succeed
            console.log('DeepSeek model ready.');
        }
        catch (error) {
            // handleModelError will throw, so we catch it here to prevent unhandled rejections
            const modelError = error instanceof AIModelError ? error : this.createModelError('Initialization failed', ErrorCode.UNKNOWN_ERROR, error);
            // Cannot reliably emitStatus here. Log instead.
            console.error("DeepSeek initialization failed:", modelError);
            // Let the error propagate if necessary, or handle appropriately
        }
    }
    /**
     * Ensures the authentication token is loaded and valid. Throws if not available after trying.
     */
    async ensureAuthToken() {
        if (!this.authToken) {
            console.log("[Deepseek ensureAuthToken] Auth token missing, attempting to retrieve...");
            // Use the generic utility, forceNewTab = true to ensure it tries the popup if needed
            const token = await getTokenFromWebsite('Deepseek', this.DEEPSEEK_HOST, `${this.DEEPSEEK_HOST}/*`, 'deepseekExtractor', // <-- Pass extractor name as string
            true // Force new tab if necessary
            );
            if (token) {
                this.authToken = `Bearer ${token}`;
            }
            else {
                console.error("[Deepseek ensureAuthToken] Failed to retrieve auth token even after forcing.");
                // Use handleModelError to throw a standardized error
                return this.handleModelError('DeepSeek authentication token is missing. Please log in to https://chat.deepseek.com.', ErrorCode.UNAUTHORIZED);
            }
        }
        return this.authToken;
    }
    /**
     * Initializes local storage for threads and validates existing ones.
     */
    async initializeStorage() {
        const threads = await this.getAllThreads();
        if (!threads) {
            // Handle case where storage might be inaccessible - AbstractModel returns [] on error
            console.warn("Could not retrieve threads from storage, starting fresh.");
            await this.saveThreadsToStorage([]);
        }
        else if (threads.length === 0) {
            await this.saveThreadsToStorage([]); // Ensure key exists even if empty
        }
        await this.validateExistingThreads();
    }
    /**
     * Validates metadata of existing threads stored locally.
     */
    async validateExistingThreads() {
        const threads = await this.getAllThreads();
        if (!threads)
            return; // Storage might be inaccessible
        let hasChanges = false;
        const validThreads = [];
        for (const thread of threads) {
            if (thread.modelName === this.getName()) {
                if (this.isValidDeepseekMetadata(thread.metadata)) {
                    validThreads.push(thread);
                }
                else {
                    console.warn(`Removing Deepseek thread ${thread.id} due to invalid metadata.`);
                    hasChanges = true; // Mark for removal, filter later
                }
            }
            else {
                validThreads.push(thread); // Keep threads from other models
            }
        }
        if (hasChanges) {
            await this.saveThreadsToStorage(validThreads);
            console.log('Removed threads with invalid metadata.');
        }
    }
    /**
     * Type guard to check if metadata is valid for DeepSeek.
     */
    isValidDeepseekMetadata(metadata) {
        return typeof metadata?.conversationId === 'string' && metadata.conversationId.length > 0;
        // lastMessageId is optional
    }
    /**
     * Retrieves valid DeepSeek metadata from the current thread, throws if invalid.
     */
    getDeepseekMetadata() {
        const currentThread = this.getCurrentThread();
        if (!currentThread) {
            // This case should ideally be prevented by checks before calling getDeepseekMetadata
            return this.handleModelError('Cannot get metadata: No current thread available.', ErrorCode.INVALID_REQUEST);
        }
        if (!currentThread.metadata || !this.isValidDeepseekMetadata(currentThread.metadata)) {
            console.error('Invalid or missing Deepseek metadata for current thread:', currentThread.id, currentThread.metadata);
            return this.handleModelError('Invalid or missing Deepseek thread metadata.', ErrorCode.INVALID_THREAD_ID);
        }
        // We've validated it, so cast is safe
        return currentThread.metadata;
    }
    // --- Model Information ---
    getName() {
        return 'DeepSeek Web';
    }
    supportsImageInput() {
        return false; // As specified in the prompt
    }
    // --- API Interaction ---
    /**
     * Creates standard headers for DeepSeek API requests.
     */
    getHeaders(includeAuth = true) {
        const headers = {
            'Accept': 'application/json, text/plain, */*',
            'Content-Type': 'application/json',
            // 'User-Agent': this.USER_AGENT, // Add User-Agent
            // Add other common headers if needed
        };
        if (includeAuth) {
            if (!this.authToken) {
                // This should ideally be caught by ensureAuthToken before calling getHeaders
                this.handleModelError('Authentication token is missing when creating headers.', ErrorCode.UNAUTHORIZED);
            }
            headers['Authorization'] = this.authToken;
        }
        return headers;
    }
    /**
     * Helper to fetch and solve the PoW challenge for /api/v0/chat/completion
     */
    async getPowSolutionForCompletion(targetUrl) {
        const powUrl = `${this.DEEPSEEK_HOST}/api/v0/chat/create_pow_challenge`;
        const payload = { target_path: targetUrl || "/api/v0/chat/completion" };
        let challengeObj;
        try {
            const response = await this.makeDeepseekRequest(powUrl, {
                method: 'POST',
                headers: this.getHeaders(),
                body: payload,
                responseType: 'json',
            });
            const data = response._data;
            if (data?.code === 0 &&
                data?.data?.biz_code === 0 &&
                data?.data?.biz_data?.challenge) {
                challengeObj = data.data.biz_data.challenge;
            }
            else {
                throw new Error('Invalid PoW challenge response structure.');
            }
        }
        catch (err) {
            throw this.createModelError('Failed to fetch PoW challenge.', ErrorCode.RATE_LIMIT_EXCEEDED, err);
        }
        // The pow-solver expects the challenge object as a JSON string
        try {
            const solution = await solvePowChallenge(JSON.stringify(challengeObj));
            return solution;
        }
        catch (err) {
            throw this.createModelError('Failed to solve PoW challenge.', ErrorCode.RATE_LIMIT_EXCEEDED, err);
        }
    }
    /**
    * Wrapper around ofetch to handle DeepSeek specific challenges like PoW and Cloudflare.
    */
    async makeDeepseekRequest(url, options, // Using the general FetchOptions from 'ofetch'
    retryCount = 1 // Allow one retry for PoW
    ) {
        try {
            // Ensure auth token is included if required by the specific endpoint
            const needsAuth = !options.headers || !options.headers['Authorization'];
            if (needsAuth && options.method !== 'GET') { // Assume GET for version/status might not need auth
                const token = await this.ensureAuthToken();
                options.headers = { ...options.headers, 'Authorization': token };
            }
            // Add common headers if not already set
            options.headers = { ...this.getHeaders(false), ...options.headers };
            console.debug(`Making Deepseek request: ${options.method || 'GET'} ${url}`); // Log request
            // Ensure the options passed are compatible with ofetch.raw
            // The generic T should ideally match the expectation based on options.responseType
            const response = await ofetch.raw(url, options); // Using 'as any' temporarily to bypass strict check, review if possible
            console.debug(`Deepseek request to ${url} successful.`);
            return response;
        }
        catch (error) {
            console.warn(`Deepseek request to ${url} failed initially:`, error);
            if (error instanceof FetchError && error.response && retryCount > 0) {
                const status = error.response.status;
                const responseBodyPromise = error.response.text(); // Read body once as text
                // Check for Cloudflare challenge (e.g., status 403, specific content)
                const responseBody = await responseBodyPromise; // Await the text body
                if (status === 403 && (responseBody.includes('cf-challenge-running') || responseBody.includes('Cloudflare') || responseBody.includes('Checking if the site connection is secure'))) {
                    console.error('Cloudflare challenge detected. Manual intervention or browser context might be required.');
                    this.emitStatus('ERROR', 'Cloudflare challenge detected.');
                    throw this.handleModelError('Cloudflare challenge detected. Please ensure you can access chat.deepseek.com in your browser.', ErrorCode.NETWORK_ERROR // Or a custom Cloudflare error code
                    );
                }
                // Fall through if not Cloudflare
            }
            // If not a handled error or retry failed, process the original error
            console.error(`Deepseek request to ${url} failed permanently after retries (if any).`);
            // Use handleModelError for consistent error throwing
            throw this.handleApiError('Deepseek API request failed', error); // Pass to specific API error handler
        }
    }
    /**
     * Fetches current user information.
     */
    async getUserInfo() {
        const url = `${this.DEEPSEEK_HOST}/api/v0/users/current`;
        try {
            const response = await this.makeDeepseekRequest(url, {
                method: 'GET',
                // Auth token will be added by makeDeepseekRequest -> ensureAuthToken
            });
            return response._data; // Return raw JSON as requested
        }
        catch (error) {
            // Let handleApiError (called by makeDeepseekRequest) handle throwing
            console.error('getUserInfo failed:', error);
            throw error; // Re-throw the error processed by handleApiError
        }
    }
    /**
     * Fetches a page of conversation sessions.
     */
    async getAllConversationsData(count = 100) {
        const url = `${this.DEEPSEEK_HOST}/api/v0/chat_session/fetch_page?count=${count}`;
        try {
            const response = await this.makeDeepseekRequest(url, {
                method: 'GET',
                // Auth token will be added by makeDeepseekRequest -> ensureAuthToken
            });
            // Basic validation as requested
            if (response._data?.code === 0 && response._data?.data?.biz_code === 0 && response._data?.data?.biz_data?.chat_sessions) {
                return response._data; // Return the full structure for now, caller can extract biz_data if needed
                // return response._data.data.biz_data; // Or return just biz_data
            }
            else {
                console.error('Invalid format received from getAllConversationsData:', response._data);
                throw new Error(`API returned unexpected structure or error: ${response._data?.msg || response._data?.data?.biz_msg || 'Unknown error'}`);
            }
        }
        catch (error) {
            // Let handleApiError (called by makeDeepseekRequest) handle throwing
            console.error('getAllConversationsData failed:', error);
            throw error; // Re-throw the error processed by handleApiError
        }
    }
    /**
     * Fetches platform version and status information.
     */
    async getPlatformData() {
        let version = null;
        let status = null;
        const versionUrl = `${this.DEEPSEEK_HOST}/version.txt`;
        const statusUrl = `${this.DEEPSEEK_HOST}/downloads/status.json`;
        try {
            const versionResponse = await this.makeDeepseekRequest(versionUrl, {
                method: 'GET',
                responseType: 'text', // Expect text
                // No auth likely needed for static files
            });
            version = versionResponse._data ?? null; // Assign null if undefined
        }
        catch (err) {
            console.warn('Failed to fetch DeepSeek version:', err);
            // Non-critical, continue
        }
        try {
            const statusResponse = await this.makeDeepseekRequest(statusUrl, {
                method: 'GET',
                responseType: 'json', // Expect JSON
                // No auth likely needed for static files
            });
            status = statusResponse._data;
        }
        catch (err) {
            console.warn('Failed to fetch DeepSeek status:', err);
            // Non-critical, continue
        }
        return [version, status];
    }
    /**
     * Creates a new conversation session on the server.
     */
    async createConversation() {
        const url = `${this.DEEPSEEK_HOST}/api/v0/chat_session/create`;
        try {
            const response = await this.makeDeepseekRequest(url, {
                method: 'POST',
                body: { character_id: null }, // Request payload
                // Auth token will be added by makeDeepseekRequest -> ensureAuthToken
            });
            const responseData = response._data;
            if (responseData?.code === 0 && responseData.data?.biz_code === 0 && responseData.data?.biz_data?.id) {
                console.log(`Created new Deepseek conversation: ${responseData.data.biz_data.id}`);
                return responseData.data.biz_data.id;
            }
            else {
                console.error('Failed to create DeepSeek conversation, unexpected response:', responseData);
                throw new Error(responseData?.msg || responseData?.data?.biz_msg || 'Unknown error creating conversation');
            }
        }
        catch (error) {
            // Let handleApiError (called by makeDeepseekRequest) handle throwing
            console.error('createConversation failed:', error);
            throw error; // Re-throw the error processed by handleApiError
        }
    }
    // --- Thread Management ---
    /**
     * Initializes a new local and server-side conversation thread.
     */
    async initNewThread() {
        this.emitStatus('BUSY', 'Initializing new DeepSeek conversation...');
        try {
            // Ensure auth token is available before creating conversation
            await this.ensureAuthToken();
            const conversationId = await this.createConversation();
            this.currentThread = {
                id: uuid(), // Internal unique ID
                title: 'New DeepSeek Chat',
                messages: [],
                createdAt: Date.now(),
                updatedAt: Date.now(),
                modelName: this.getName(),
                metadata: {
                    conversationId: conversationId,
                    lastMessageId: null // Initialize last message ID
                }
            };
            await this.saveThread(); // Save the new thread locally
            this.emitStatus('READY', 'New conversation ready.');
            console.log(`Initialized and saved new DeepSeek thread: ${this.currentThread.id} (Conv ID: ${conversationId})`);
        }
        catch (error) {
            // Error is already handled and thrown by createConversation or ensureAuthToken
            // Use handleModelError for consistency before throwing/emitting
            const modelError = error instanceof AIModelError ? error : this.createModelError('Failed to initialize new thread', ErrorCode.UNKNOWN_ERROR, error);
            this.emitStatus('ERROR', 'Initialization failed.', modelError);
            console.error('Failed to initialize new DeepSeek thread:', modelError);
            throw modelError; // Re-throw standardized error
        }
    }
    // --- Message Sending ---
    /**
     * Implements the core logic for sending a message and processing the response stream.
     */
    async doSendMessage(params) {
        this.currentOnEventCallback = params.onEvent; // Store callback for emitStatus
        this.emitStatus('BUSY', 'Preparing message...'); // Use emitStatus helper
        if (params.images && params.images.length > 0) {
            this.handleModelError('Image input is not supported by the DeepSeek Web model.', ErrorCode.FEATURE_NOT_SUPPORTED, params);
            // handleModelError throws, so no return needed
        }
        try {
            // Ensure a thread exists, load or create if necessary
            if (!this.currentThread || !this.isValidDeepseekMetadata(this.currentThread.metadata)) {
                console.log('No valid current thread found, initializing new DeepSeek thread...');
                await this.initNewThread(); // Will throw on failure
            }
            // Assert thread and metadata exist after potential initNewThread call
            const currentThread = this.getCurrentThread(); // Use base method
            if (!currentThread) {
                // This should technically be caught by ensureThreadLoaded logic, but as a safeguard
                this.handleModelError("Current thread is unexpectedly undefined after initialization.", ErrorCode.UNKNOWN_ERROR, params);
            }
            const metadata = this.getDeepseekMetadata(); // Safe getter ensures validity
            // Add user message to local thread history
            const userMessage = this.createMessage('user', params.prompt);
            currentThread.messages.push(userMessage);
            currentThread.updatedAt = Date.now();
            await this.saveThread(); // Save user message immediately
            this.emitStatus('BUSY', 'Solving PoW and sending message to DeepSeek...');
            // --- NEW POW FLOW ---
            let powSolution;
            try {
                powSolution = await this.getPowSolutionForCompletion("/api/v0/chat/completion");
            }
            catch (err) {
                console.error('Failed to solve DeepSeek PoW challenge:', err);
                this.emitStatus('ERROR', 'Failed to solve PoW challenge.');
                // Throw a specific error indicating PoW failure using handleModelError
                throw this.handleModelError('Failed to solve Proof-of-Work challenge.', ErrorCode.RATE_LIMIT_EXCEEDED, // Use appropriate code
                undefined, // No params with onEvent here
                err);
            }
            const payload = {
                chat_session_id: metadata.conversationId,
                // Use last message ID from metadata, which should be updated after each assistant response
                parent_message_id: metadata.lastMessageId || null,
                prompt: params.prompt,
                ref_file_ids: [], // No file support
                thinking_enabled: false, // As per prompt examples
                search_enabled: false, // As per prompt examples
            };
            const url = `${this.DEEPSEEK_HOST}/api/v0/chat/completion`;
            const streamResponse = await this.makeDeepseekRequest(url, {
                method: 'POST',
                headers: {
                    ...this.getHeaders(), // Ensure auth is included
                    'Accept': 'text/event-stream', // Crucial for SSE
                    'x-ds-pow-response': powSolution, // Add PoW solution header
                },
                body: payload, // ofetch handles JSON stringify
                responseType: 'stream', // Use ofetch's stream handling
                signal: params.signal, // Pass the abort signal
            });
            if (!streamResponse.body) {
                throw this.createModelError('No response body received in stream.', ErrorCode.NETWORK_ERROR);
            }
            this.emitStatus('BUSY', 'Receiving response...');
            // Process the stream
            const reader = streamResponse.body.getReader();
            const decoder = new TextDecoder();
            let buffer = '';
            let fullResponseText = '';
            let assistantMessageId = uuid(); // Pre-generate internal ID
            let assistantMessage = null;
            let serverMessageId = null; // Store the actual ID from server
            let doneProcessing = false;
            while (!doneProcessing) {
                const { done, value } = await reader.read();
                if (done) {
                    doneProcessing = true;
                    // Process any remaining buffer content if necessary
                    if (buffer.trim()) {
                        console.warn("Stream ended with unprocessed buffer:", buffer);
                        // Potentially try parsing the last bit
                    }
                    break;
                }
                buffer += decoder.decode(value, { stream: true });
                // Process buffer line by line (SSE format: event: type\ndata: json\n\n)
                let eventSeparatorIndex;
                while ((eventSeparatorIndex = buffer.indexOf('\n\n')) !== -1) {
                    const eventBlock = buffer.substring(0, eventSeparatorIndex);
                    buffer = buffer.substring(eventSeparatorIndex + 2); // Move past the separator
                    let currentEventType = null;
                    let currentEventData = null;
                    const lines = eventBlock.split('\n');
                    for (const line of lines) {
                        if (line.startsWith('event: ')) {
                            currentEventType = line.substring(7).trim();
                        }
                        else if (line.startsWith('data: ')) {
                            const dataStr = line.substring(5).trim();
                            if (dataStr) {
                                try {
                                    currentEventData = JSON.parse(dataStr);
                                }
                                catch (e) {
                                    console.error('Failed to parse SSE data line:', dataStr, e);
                                    // Continue to next line/event, maybe log error
                                }
                            }
                        }
                    }
                    // Process the assembled event
                    if (currentEventType && currentEventData) {
                        // --- Event Processing Logic ---
                        if (currentEventType === 'title') {
                            if (currentEventData.content && currentThread.title !== currentEventData.content) {
                                currentThread.title = currentEventData.content;
                                currentThread.updatedAt = Date.now();
                                // Debounce saving? For now, save immediately.
                                await this.saveThread();
                                this.emitStatus('TITLE_UPDATED', currentEventData.content, undefined, currentThread.id);
                                console.log("Thread title updated:", currentEventData.content);
                            }
                        }
                        else if (currentEventType === 'ping') {
                            // Ignore ping events
                        }
                        else if (currentEventType === 'finish' || currentEventType === 'close') {
                            console.log(`Received stream event: ${currentEventType}`);
                            // Might signal the end, but rely on FINISHED status in data or done flag
                        }
                        else {
                            // Potentially handle other event types if they exist
                            console.log(`Unhandled SSE event type: ${currentEventType}`, currentEventData);
                        }
                    }
                    else if (currentEventData) { // Handle data line without preceding event (common case for content)
                        // --- Data Processing Logic (No explicit event type) ---
                        // Adapt based on the structure provided in response.txt example:
                        // { "v": " chunk", "p": "response/content", "o": "APPEND" }
                        // { "v": "FINISHED", "p": "response/status", "o": "SET" }
                        // { "v": 12345, "p": "response/message_id", "o": "SET" }
                        if (typeof currentEventData.v !== 'undefined' && currentEventData.p === 'response/content' && currentEventData.o === 'APPEND') {
                            const textChunk = currentEventData.v;
                            fullResponseText += textChunk;
                            if (!assistantMessage) {
                                // Create message object on first content chunk
                                assistantMessage = this.createMessage('assistant', fullResponseText);
                                assistantMessage.id = assistantMessageId; // Use pre-generated ID initially
                                currentThread.messages.push(assistantMessage);
                            }
                            else {
                                // Update existing message content
                                assistantMessage.content = fullResponseText;
                            }
                            currentThread.updatedAt = Date.now();
                            this.emitStatus('PARTIAL_UPDATE', fullResponseText);
                            // Debounced save might be better here
                            // await this.saveThread();
                        }
                        else if (typeof currentEventData.v !== 'undefined' && currentEventData.p === 'response/message_id' && currentEventData.o === 'SET') {
                            serverMessageId = currentEventData.v;
                            if (assistantMessage) {
                                assistantMessage.metadata = { ...(assistantMessage.metadata || {}), serverId: serverMessageId };
                                assistantMessage.id = String(serverMessageId); // Optionally overwrite internal ID with server ID if preferred
                            }
                            console.log("Received server message ID:", serverMessageId);
                        }
                        else if (typeof currentEventData.v !== 'undefined' && currentEventData.p === 'response/status' && currentEventData.v === 'FINISHED') {
                            console.log('Received FINISHED status event.');
                            // Don't necessarily break here, wait for the reader.read() done flag
                            // The FINISHED status might come before the last content chunk in some APIs
                        }
                        else if (typeof currentEventData.v !== 'undefined' && currentEventData.p === 'response/accumulated_token_usage') {
                            console.log(`Token usage info:`, currentEventData.v);
                            // Store or log token usage if needed
                        }
                        else {
                            console.log("Received unhandled data structure:", currentEventData);
                        }
                    }
                } // End while loop processing buffer
            } // End while loop reading stream
            // --- Stream Finished ---
            console.log('DeepSeek stream processing finished.');
            if (assistantMessage) {
                // Ensure final content and metadata are set
                assistantMessage.content = fullResponseText;
                if (serverMessageId !== null) {
                    assistantMessage.metadata = { ...(assistantMessage.metadata || {}), serverId: serverMessageId };
                    assistantMessage.id = String(serverMessageId); // Update ID if server ID received
                    metadata.lastMessageId = serverMessageId; // IMPORTANT: Update metadata for next request
                }
                else {
                    // If no server ID received, maybe use the last user message ID? Or null?
                    // For Deepseek, parent_message_id seems crucial, needs clarification if server ID isn't always sent.
                    // Let's assume null if not received for now, or check Deepseek API docs.
                    // metadata.lastMessageId = null; // Or keep previous if null?
                    console.warn("No server message ID received for assistant response.");
                }
                currentThread.updatedAt = Date.now();
                await this.saveThread(); // Final save after completion
                this.emitStatus('COMPLETE', fullResponseText);
            }
            else if (!fullResponseText) {
                // Handle cases where the stream ended without sending any content
                console.warn('DeepSeek stream ended without generating content.');
                // Don't add an empty assistant message
                this.emitStatus('COMPLETE', ''); // Signal completion with empty content
            }
            else {
                // Should not happen if fullResponseText is populated, but as a safeguard
                console.error("Stream finished, response text exists, but no assistant message object was created.");
                this.emitStatus('ERROR', 'Internal error processing response.');
            }
        }
        catch (error) {
            // Handle errors from initial setup or stream processing
            // Use handleModelError for consistent error handling and throwing
            this.handleModelError('Error during message sending or processing', error instanceof AIModelError ? error.code : undefined, // Preserve code if already AIModelError
            params, // Pass params for onEvent callback
            error // Pass original error as cause
            );
            // handleModelError will throw the error after calling onEvent('ERROR', ...)
        }
        finally {
            // Ensure DONE is sent *unless* an error was thrown by handleModelError inside the catch block
            // handleModelError already sends ERROR and DONE is often implicit after ERROR.
            // However, explicitly sending DONE might be expected by the caller.
            // Check if an error occurred *during* this operation.
            // This is tricky. The AbstractModel's sendMessage handles sending DONE after doSendMessage resolves or rejects.
            // So, doSendMessage should focus on its core task and throw on error.
            this.currentOnEventCallback = undefined; // Clear the callback
        }
    }
    // --- Error Handling ---
    /**
     * Handles API errors, attempting to map them to standard ErrorCodes.
     * This is called internally by makeDeepseekRequest or other API interaction points.
     * It should return 'never' because it ultimately calls the base handleModelError which throws.
     */
    handleApiError(message, error) {
        let errorCode = ErrorCode.UNKNOWN_ERROR;
        let errorMessage = message;
        if (error instanceof FetchError) {
            errorMessage = `${message}: ${error.status} ${error.statusText || ''}.`;
            // Try to append details from response body if available (error.data is parsed body)
            if (error.data) {
                // Check for Deepseek's specific error structure if known
                const deepseekMsg = error.data.msg || error.data.data?.biz_msg;
                if (deepseekMsg) {
                    errorMessage += ` Server Msg: ${deepseekMsg}`;
                }
                else {
                    errorMessage += ` Details: ${JSON.stringify(error.data).substring(0, 200)}`;
                }
            }
            else {
                errorMessage += ` ${error.message}`; // Fallback to FetchError message
            }
            switch (error.status) {
                case 401: // Unauthorized - Token likely invalid or expired
                    errorCode = ErrorCode.UNAUTHORIZED;
                    this.authToken = undefined; // Clear invalid token
                    // Don't automatically retry here, let ensureAuthToken handle retry on next call
                    break;
                case 403: // Forbidden - Could be auth, permissions, or Cloudflare/WAF
                    // Cloudflare is checked earlier in makeDeepseekRequest. If we reach here, it might be other permissions.
                    // Let's lean towards UNAUTHORIZED unless we have more specific info.
                    errorCode = ErrorCode.UNAUTHORIZED;
                    this.authToken = undefined; // Clear token just in case
                    break;
                case 400: // Bad Request
                    errorCode = ErrorCode.INVALID_REQUEST;
                    break;
                case 429: // Too Many Requests (Rate limit or PoW)
                    // PoW is handled in makeDeepseekRequest, this would be a real rate limit
                    errorCode = ErrorCode.RATE_LIMIT_EXCEEDED;
                    break;
                case 500: // Internal Server Error
                case 503: // Service Unavailable
                    errorCode = ErrorCode.SERVICE_UNAVAILABLE;
                    break;
                // Add other status code mappings if needed
                default:
                    errorCode = ErrorCode.NETWORK_ERROR; // Treat other errors as network/service issues
            }
        }
        else if (error instanceof Error) {
            // General JS Error
            errorMessage = `${message}: ${error.message}`;
        }
        else {
            // Unknown error type
            errorMessage = `${message}: Unknown error occurred.`;
        }
        // Use the base class handler to format, log, emit event, and throw
        return this.handleModelError(errorMessage, errorCode, undefined, error);
    }
    /** Helper to emit status events via the onEvent callback */
    emitStatus(
    // Use string literals for type comparison
    type, message, // Often used for text content in UPDATE_ANSWER/COMPLETE
    errorOrData, // Error object for ERROR, data payload otherwise
    threadId // Specifically for TITLE_UPDATE or DONE
    ) {
        const onEvent = this.currentOnEventCallback;
        if (!onEvent) {
            // Log if called outside the context of doSendMessage (where callback is set)
            console.warn(`[emitStatus Ignored (${type})] ${message || ''}`, errorOrData || '');
            return;
        }
        let event;
        switch (type) {
            case 'BUSY':
            case 'READY':
                // Represent BUSY/READY via UPDATE_ANSWER or a custom event if needed.
                // Using UPDATE_ANSWER might be confusing. Let's just log for now.
                console.log(`[Status:${type}] ${message || ''}`);
                // If a specific event structure is needed for BUSY/READY, define it in types.ts
                return; // Don't send an event for these simple statuses for now
            case 'UPDATE_ANSWER': // Explicitly handle UPDATE_ANSWER
                event = { type: 'UPDATE_ANSWER', data: { text: message || '' } };
                break;
            case 'PARTIAL_UPDATE': // Map PARTIAL_UPDATE to UPDATE_ANSWER
                event = { type: 'UPDATE_ANSWER', data: { text: message || '' } };
                break;
            case 'COMPLETE': // Map COMPLETE to UPDATE_ANSWER (final) and DONE
                // Send final content update
                onEvent({ type: 'UPDATE_ANSWER', data: { text: message || '' } });
                // Send DONE event
                event = { type: 'DONE', data: { threadId: threadId || this.currentThread?.id || '' } };
                break;
            case 'DONE': // Handle explicit DONE calls if needed
                event = { type: 'DONE', data: { threadId: threadId || this.currentThread?.id || '' } };
                break;
            case 'ERROR':
                const error = errorOrData instanceof AIModelError
                    ? errorOrData
                    : this.createModelError(message || 'Unknown Error', ErrorCode.UNKNOWN_ERROR, errorOrData);
                event = { type: 'ERROR', error: error };
                break;
            case 'TITLE_UPDATED':
                event = { type: 'TITLE_UPDATE', data: { title: message || '', threadId: threadId || this.currentThread?.id || '' } };
                break;
            case 'SUGGESTED_RESPONSES':
                // Assuming errorOrData contains the suggestions array
                event = { type: 'SUGGESTED_RESPONSES', data: { suggestions: Array.isArray(errorOrData) ? errorOrData : [] } };
                break;
            default:
                console.warn(`Unknown event type for emitStatus: ${type}`);
                return; // Unknown type
        }
        onEvent(event);
    }
    // Helper to create AIModelError instances consistently
    // Overloads base class method to ensure 'cause' is handled if possible
    createModelError(message, code, cause) {
        const modelError = new AIModelError(message, code);
        if (cause && 'cause' in Error.prototype) {
            try {
                // Use Object.assign for environments where 'cause' isn't directly assignable
                Object.assign(modelError, { cause: cause });
            }
            catch (e) {
                console.warn("Could not assign 'cause' to AIModelError", e);
            }
        }
        else if (cause instanceof Error) {
            // Fallback: Append cause message if it's an Error
            modelError.message += ` | Caused by: ${cause.message}`;
        }
        return modelError;
    }
}
