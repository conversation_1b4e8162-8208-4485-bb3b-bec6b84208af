import { AbstractModel } from './abstract-model';
import { StatusEvent } from './types';
import { AUTH_EVENTS } from '../utils/auth';
export { AUTH_EVENTS };
export declare class BingWebModel extends AbstractModel {
    private authToken?;
    constructor();
    private initializeStorage;
    private validateExistingThreads;
    private isValidBingMetadata;
    getName(): string;
    supportsImageInput(): boolean;
    /**
     * Function to inject into Copilot tab to extract token from localStorage.
     */
    private static injectedCopilotTokenExtractor;
    /**
    * Retrieves the Copilot auth token using the generic utility.
    * @param forceNewTab If true, forces the temporary tab method.
    * @returns The raw token string or null.
    */
    private getCopilotToken;
    /**
     * Ensures a valid auth token is available, retrieving it if necessary.
     * Stores the token with "Bearer " prefix in this.authToken.
     * Throws AIModelError if retrieval fails.
     */
    private ensureAuthToken;
    private createConversation;
    protected doSendMessage(params: {
        prompt: string;
        images?: File[];
        signal?: AbortSignal;
        mode?: string;
        onEvent: (event: StatusEvent) => void;
    }): Promise<void>;
    private ensureThreadLoaded;
    private getCurrentThreadSafe;
    initNewThread(): Promise<void>;
    loadThread(threadId: string): Promise<void>;
    private getBingMetadata;
    saveThread(): Promise<void>;
    private uploadImage;
}
