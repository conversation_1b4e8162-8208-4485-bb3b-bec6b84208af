import { Router, Request, Response } from 'express';
import winston from 'winston';
import { WebSocketManager } from '../websocket';
import { MODEL_REGISTRY } from '../types/models';
import { ModelListResponse, Model } from '../types/openai';

export function createModelsRouter(
  wsManager: WebSocketManager,
  logger: winston.Logger
): Router {
  const router = Router();

  router.get('/', async (req: Request, res: Response) => {
    try {
      const timestamp = Math.floor(Date.now() / 1000);
      
      // Get model status from extension if connected
      let availableModels = new Set<string>();
      
      if (wsManager.isConnected()) {
        try {
          const modelStatus = await wsManager.getModelStatus();
          modelStatus.forEach(status => {
            if (status.available && status.authenticated) {
              // Map provider to available models
              Object.entries(MODEL_REGISTRY).forEach(([modelId, config]) => {
                if (config.provider === status.provider) {
                  availableModels.add(modelId);
                }
              });
            }
          });
        } catch (error) {
          logger.warn('Failed to get model status from extension:', error);
        }
      }

      // If no connection or no status, show all models as potentially available
      if (availableModels.size === 0) {
        Object.keys(MODEL_REGISTRY).forEach(modelId => availableModels.add(modelId));
      }

      // Build model list
      const models: Model[] = Array.from(availableModels).map(modelId => {
        const config = MODEL_REGISTRY[modelId];
        return {
          id: modelId,
          object: 'model',
          created: timestamp - 86400, // Created yesterday
          owned_by: config.provider.replace('-web', ''),
          permission: [{
            id: `modelperm-${modelId}`,
            object: 'model_permission',
            created: timestamp - 86400,
            allow_create_engine: false,
            allow_sampling: true,
            allow_logprobs: false,
            allow_search_indices: false,
            allow_view: true,
            allow_fine_tuning: false,
            organization: '*',
            group: null,
            is_blocking: false
          }],
          root: modelId,
          parent: null
        };
      });

      const response: ModelListResponse = {
        object: 'list',
        data: models
      };

      res.json(response);
    } catch (error) {
      logger.error('Error fetching models:', error);
      res.status(500).json({
        error: {
          message: 'Failed to fetch models',
          type: 'internal_error',
          param: null,
          code: 'internal_error'
        }
      });
    }
  });

  router.get('/:model', async (req: Request, res: Response) => {
    const modelId = req.params.model;
    const config = MODEL_REGISTRY[modelId];

    if (!config) {
      return res.status(404).json({
        error: {
          message: `Model ${modelId} not found`,
          type: 'invalid_request_error',
          param: 'model',
          code: 'model_not_found'
        }
      });
    }

    const timestamp = Math.floor(Date.now() / 1000);
    const model: Model = {
      id: modelId,
      object: 'model',
      created: timestamp - 86400,
      owned_by: config.provider.replace('-web', ''),
      permission: [{
        id: `modelperm-${modelId}`,
        object: 'model_permission',
        created: timestamp - 86400,
        allow_create_engine: false,
        allow_sampling: true,
        allow_logprobs: false,
        allow_search_indices: false,
        allow_view: true,
        allow_fine_tuning: false,
        organization: '*',
        group: null,
        is_blocking: false
      }],
      root: modelId,
      parent: null
    };

    res.json(model);
  });

  return router;
}
