{"version": 3, "file": "prefer-readonly.js", "sourceRoot": "", "sources": ["../../src/rules/prefer-readonly.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AACA,oDAAoE;AACpE,sDAAwC;AACxC,+CAAiC;AAEjC,kCAKiB;AACjB,+DAGkC;AASlC,MAAM,uBAAuB,GAAG;IAC9B,sBAAc,CAAC,uBAAuB;IACtC,sBAAc,CAAC,mBAAmB;IAClC,sBAAc,CAAC,kBAAkB;IACjC,sBAAc,CAAC,gBAAgB;CAChC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAEb,kBAAe,IAAA,iBAAU,EAAsB;IAC7C,IAAI,EAAE,iBAAiB;IACvB,IAAI,EAAE;QACJ,IAAI,EAAE;YACJ,WAAW,EACT,yGAAyG;YAC3G,oBAAoB,EAAE,IAAI;SAC3B;QACD,OAAO,EAAE,MAAM;QACf,QAAQ,EAAE;YACR,cAAc,EACZ,+DAA+D;SAClE;QACD,MAAM,EAAE;YACN;gBACE,oBAAoB,EAAE,KAAK;gBAC3B,UAAU,EAAE;oBACV,iBAAiB,EAAE;wBACjB,IAAI,EAAE,SAAS;qBAChB;iBACF;gBACD,IAAI,EAAE,QAAQ;aACf;SACF;QACD,IAAI,EAAE,YAAY;KACnB;IACD,cAAc,EAAE,CAAC,EAAE,iBAAiB,EAAE,KAAK,EAAE,CAAC;IAC9C,MAAM,CAAC,OAAO,EAAE,CAAC,EAAE,iBAAiB,EAAE,CAAC;QACrC,MAAM,QAAQ,GAAG,IAAA,wBAAiB,EAAC,OAAO,CAAC,CAAC;QAC5C,MAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;QAClD,MAAM,eAAe,GAAiB,EAAE,CAAC;QAEzC,SAAS,8BAA8B,CACrC,IAAiC,EACjC,MAAe,EACf,UAAsB;YAEtB,IAAI,EAAE,CAAC,kBAAkB,CAAC,MAAM,CAAC,EAAE,CAAC;gBAClC,4BAA4B,CAAC,IAAI,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC;gBACvD,OAAO;YACT,CAAC;YAED,IAAI,EAAE,CAAC,kBAAkB,CAAC,MAAM,CAAC,IAAI,yBAAyB,CAAC,IAAI,CAAC,EAAE,CAAC;gBACrE,UAAU,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC;gBACzC,OAAO;YACT,CAAC;YAED,IACE,EAAE,CAAC,wBAAwB,CAAC,MAAM,CAAC;gBACnC,EAAE,CAAC,uBAAuB,CAAC,MAAM,CAAC,EAClC,CAAC;gBACD,0CAA0C,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;YACjE,CAAC;QACH,CAAC;QAED,SAAS,4BAA4B,CACnC,IAAiC,EACjC,MAA2B,EAC3B,UAAsB;YAEtB,IACE,MAAM,CAAC,IAAI,KAAK,IAAI;gBACpB,OAAO,CAAC,gBAAgB,CAAC,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,EACnD,CAAC;gBACD,UAAU,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC;YAC3C,CAAC;QACH,CAAC;QAED,SAAS,0CAA0C,CACjD,IAA0D,EAC1D,UAAsB;YAEtB,IACE,IAAI,CAAC,QAAQ,KAAK,EAAE,CAAC,UAAU,CAAC,aAAa;gBAC7C,IAAI,CAAC,QAAQ,KAAK,EAAE,CAAC,UAAU,CAAC,eAAe,EAC/C,CAAC;gBACD,UAAU,CAAC,uBAAuB,CAChC,IAAI,CAAC,OAAsC,CAC5C,CAAC;YACJ,CAAC;QACH,CAAC;QAED,SAAS,yBAAyB,CAChC,IAAiC;YAEjC,IAAI,OAAO,GAAG,IAAI,CAAC,MAA6B,CAAC;YAEjD,OAAO,OAAO,EAAE,CAAC;gBACf,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;gBAE9B,IACE,EAAE,CAAC,yBAAyB,CAAC,MAAM,CAAC;oBACpC,EAAE,CAAC,wBAAwB,CAAC,MAAM,CAAC;oBACnC,EAAE,CAAC,kBAAkB,CAAC,MAAM,CAAC;oBAC7B,CAAC,EAAE,CAAC,eAAe,CAAC,MAAM,CAAC;wBACzB,EAAE,CAAC,wBAAwB,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,EAC7C,CAAC;oBACD,OAAO,GAAG,MAAM,CAAC;gBACnB,CAAC;qBAAM,IACL,EAAE,CAAC,kBAAkB,CAAC,MAAM,CAAC;oBAC7B,CAAC,EAAE,CAAC,0BAA0B,CAAC,OAAO,CAAC,EACvC,CAAC;oBACD,OAAO,CACL,MAAM,CAAC,IAAI,KAAK,OAAO;wBACvB,MAAM,CAAC,aAAa,CAAC,IAAI,KAAK,EAAE,CAAC,UAAU,CAAC,WAAW,CACxD,CAAC;gBACJ,CAAC;qBAAM,CAAC;oBACN,MAAM;gBACR,CAAC;YACH,CAAC;YAED,OAAO,KAAK,CAAC;QACf,CAAC;QAED,SAAS,8BAA8B,CACrC,IAI6B;YAE7B,IAAI,eAAe,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACjC,OAAO,KAAK,CAAC;YACf,CAAC;YAED,MAAM,MAAM,GAAG,QAAQ,CAAC,qBAAqB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YACxD,IAAI,EAAE,CAAC,wBAAwB,CAAC,MAAM,CAAC,EAAE,CAAC;gBACxC,OAAO,KAAK,CAAC;YACf,CAAC;YAED,OAAO,OAAO,CAAC,uBAAuB,CAAC,MAAM,CAAC,CAAC;QACjD,CAAC;QAED,SAAS,2BAA2B,CAClC,aAA6C;YAE7C,OAAO;gBACL,MAAM,EAAE,QAAQ,CAAC,qBAAqB,CAAC,GAAG,CAAC,aAAa,CAAC;gBACzD,QAAQ,EAAE,QAAQ,CAAC,qBAAqB,CAAC,GAAG,CAAC,aAAa,CAAC,IAAI,CAAC;aACjE,CAAC;QACJ,CAAC;QAED,OAAO;YACL,mCAAmC,CACjC,IAA0D;gBAE1D,eAAe,CAAC,IAAI,CAClB,IAAI,UAAU,CACZ,OAAO,EACP,QAAQ,CAAC,qBAAqB,CAAC,GAAG,CAAC,IAAI,CAAC,EACxC,iBAAiB,CAClB,CACF,CAAC;YACJ,CAAC;YACD,wCAAwC;gBACtC,MAAM,mBAAmB,GAAG,IAAA,iBAAU,EACpC,eAAe,CAAC,GAAG,EAAE,EACrB,kCAAkC,CACnC,CAAC;gBAEF,KAAK,MAAM,aAAa,IAAI,mBAAmB,CAAC,qCAAqC,EAAE,EAAE,CAAC;oBACxF,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,GACxB,2BAA2B,CAAC,aAAa,CAAC,CAAC;oBAE7C,MAAM,eAAe,GAEkB,CAAC,GAAG,EAAE;wBAC3C,QAAQ,MAAM,CAAC,IAAI,EAAE,CAAC;4BACpB,KAAK,sBAAc,CAAC,gBAAgB,CAAC;4BACrC,KAAK,sBAAc,CAAC,kBAAkB,CAAC;4BACvC,KAAK,sBAAc,CAAC,0BAA0B;gCAC5C,OAAO,EAAE,GAAG,EAAE,IAAA,mCAAgB,EAAC,OAAO,CAAC,UAAU,EAAE,MAAM,CAAC,EAAE,CAAC;4BAC/D,KAAK,sBAAc,CAAC,mBAAmB;gCACrC,OAAO;oCACL,GAAG,EAAE,IAAA,8CAA2B,EAC9B,OAAO,CAAC,UAAU,EAClB,MAAM,EACL,QAAgC,CAAC,IAAI,CACvC;iCACF,CAAC;4BACJ;gCACE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;wBAC5B,CAAC;oBACH,CAAC,CAAC,EAAE,CAAC;oBAEL,OAAO,CAAC,MAAM,CAAC;wBACb,GAAG,eAAe;wBAClB,IAAI,EAAE;4BACJ,IAAI,EAAE,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,QAAQ,CAAC;yBAC3C;wBACD,GAAG,EAAE,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,gBAAgB,CAAC,QAAQ,EAAE,WAAW,CAAC;wBAC3D,SAAS,EAAE,gBAAgB;qBAC5B,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;YACD,gBAAgB,CAAC,IAAI;gBACnB,IAAI,eAAe,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;oBACnD,MAAM,MAAM,GAAG,QAAQ,CAAC,qBAAqB,CAAC,GAAG,CAC/C,IAAI,CAC0B,CAAC;oBACjC,8BAA8B,CAC5B,MAAM,EACN,MAAM,CAAC,MAAM,EACb,eAAe,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,CAAC,CAC5C,CAAC;gBACJ,CAAC;YACH,CAAC;YACD,CAAC,uBAAuB,CAAC,CACvB,IAI6B;gBAE7B,IAAI,gBAAQ,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE,CAAC;oBACjC,eAAe,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,gBAAgB,CAC1D,QAAQ,CAAC,qBAAqB,CAAC,GAAG,CAAC,IAAI,CAAC,CACzC,CAAC;gBACJ,CAAC;qBAAM,IAAI,8BAA8B,CAAC,IAAI,CAAC,EAAE,CAAC;oBAChD,eAAe,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,mBAAmB,EAAE,CAAC;gBACpE,CAAC;YACH,CAAC;YACD,CAAC,GAAG,uBAAuB,OAAO,CAAC,CACjC,IAI6B;gBAE7B,IAAI,gBAAQ,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE,CAAC;oBACjC,eAAe,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,eAAe,EAAE,CAAC;gBAChE,CAAC;qBAAM,IAAI,8BAA8B,CAAC,IAAI,CAAC,EAAE,CAAC;oBAChD,eAAe,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,kBAAkB,EAAE,CAAC;gBACnE,CAAC;YACH,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC;AAMH,MAAM,mBAAmB,GAAG,CAAC,CAAC,CAAC;AAC/B,MAAM,2BAA2B,GAAG,CAAC,CAAC;AAEtC,IAAK,mBAKJ;AALD,WAAK,mBAAmB;IACtB,qFAAgB,CAAA;IAChB,+DAAK,CAAA;IACL,qEAAQ,CAAA;IACR,6DAAI,CAAA;AACN,CAAC,EALI,mBAAmB,KAAnB,mBAAmB,QAKvB;AAED,MAAM,UAAU;IAgBd,YACmB,OAAuB,EACxC,SAAkC,EACjB,iBAA2B;QAF3B,YAAO,GAAP,OAAO,CAAgB;QAEvB,sBAAiB,GAAjB,iBAAiB,CAAU;QAlB7B,6BAAwB,GAAG,IAAI,GAAG,EAGhD,CAAC;QACa,6BAAwB,GAAG,IAAI,GAAG,EAGhD,CAAC;QACa,gCAA2B,GAAG,IAAI,GAAG,EAAU,CAAC;QAChD,gCAA2B,GAAG,IAAI,GAAG,EAAU,CAAC;QAIzD,0BAAqB,GAAG,mBAAmB,CAAC;QAOlD,MAAM,SAAS,GAAG,OAAO,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;QACvD,IAAI,OAAO,CAAC,kBAAkB,CAAC,SAAS,CAAC,EAAE,CAAC;YAC1C,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QACtC,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC7B,CAAC;QAED,KAAK,MAAM,MAAM,IAAI,SAAS,CAAC,OAAO,EAAE,CAAC;YACvC,IAAI,EAAE,CAAC,qBAAqB,CAAC,MAAM,CAAC,EAAE,CAAC;gBACrC,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;YACnC,CAAC;QACH,CAAC;IACH,CAAC;IAEM,mBAAmB,CAAC,IAAoC;QAC7D,IACE,CAAC,CACC,OAAO,CAAC,iBAAiB,CAAC,IAAI,EAAE,EAAE,CAAC,aAAa,CAAC,OAAO,CAAC;YACzD,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,EAAE,CAAC,UAAU,CAAC,iBAAiB,CACnD;YACD,OAAO,CAAC,iBAAiB,CACvB,IAAI,EACJ,EAAE,CAAC,aAAa,CAAC,QAAQ,GAAG,EAAE,CAAC,aAAa,CAAC,QAAQ,CACtD;YACD,EAAE,CAAC,sBAAsB,CAAC,IAAI,CAAC,IAAI,CAAC,EACpC,CAAC;YACD,OAAO;QACT,CAAC;QAED,IACE,IAAI,CAAC,iBAAiB;YACtB,IAAI,CAAC,WAAW,KAAK,SAAS;YAC9B,CAAC,EAAE,CAAC,eAAe,CAAC,IAAI,CAAC,WAAW,CAAC,EACrC,CAAC;YACD,OAAO;QACT,CAAC;QAED,CAAC,OAAO,CAAC,iBAAiB,CAAC,IAAI,EAAE,EAAE,CAAC,aAAa,CAAC,MAAM,CAAC;YACvD,CAAC,CAAC,IAAI,CAAC,wBAAwB;YAC/B,CAAC,CAAC,IAAI,CAAC,wBAAwB,CAChC,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,IAAI,CAAC,CAAC;IACnC,CAAC;IAEM,sBAAsB,CAAC,IAAa;QACzC,IAAI,IAAI,CAAC,cAAc,EAAE,EAAE,CAAC;YAC1B,IAAI,MAAM,GAAwB,mBAAmB,CAAC,IAAI,CAAC;YAC3D,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;gBACjC,MAAM,aAAa,GAAG,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,CAAC;gBAC3D,QAAQ,aAAa,EAAE,CAAC;oBACtB,KAAK,mBAAmB,CAAC,KAAK;wBAC5B,IAAI,MAAM,KAAK,mBAAmB,CAAC,QAAQ,EAAE,CAAC;4BAC5C,OAAO,mBAAmB,CAAC,gBAAgB,CAAC;wBAC9C,CAAC;wBACD,MAAM,GAAG,mBAAmB,CAAC,KAAK,CAAC;wBACnC,MAAM;oBACR,KAAK,mBAAmB,CAAC,QAAQ;wBAC/B,IAAI,MAAM,KAAK,mBAAmB,CAAC,KAAK,EAAE,CAAC;4BACzC,OAAO,mBAAmB,CAAC,gBAAgB,CAAC;wBAC9C,CAAC;wBACD,MAAM,GAAG,mBAAmB,CAAC,QAAQ,CAAC;wBACtC,MAAM;gBACV,CAAC;YACH,CAAC;YACD,OAAO,MAAM,CAAC;QAChB,CAAC;QACD,IAAI,IAAI,CAAC,OAAO,EAAE,EAAE,CAAC;YACnB,wEAAwE;YACxE,oEAAoE;YACpE,iEAAiE;YACjE,OAAO,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QACpD,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,IAAA,0BAAmB,EAAC,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;YACpE,OAAO,mBAAmB,CAAC,IAAI,CAAC;QAClC,CAAC;QAED,MAAM,WAAW,GACf,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC;YAC1B,OAAO,CAAC,eAAe,CAAC,IAAI,EAAE,EAAE,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;QAE1D,IAAI,WAAW,EAAE,CAAC;YAChB,OAAO,mBAAmB,CAAC,KAAK,CAAC;QACnC,CAAC;QAED,OAAO,mBAAmB,CAAC,QAAQ,CAAC;IACtC,CAAC;IAEM,uBAAuB,CAAC,IAAiC;QAC9D,MAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAErE,MAAM,6BAA6B,GACjC,IAAI,CAAC,sBAAsB,CAAC,YAAY,CAAC,CAAC;QAE5C,IACE,6BAA6B,KAAK,mBAAmB,CAAC,QAAQ;YAC9D,IAAI,CAAC,qBAAqB,KAAK,2BAA2B,EAC1D,CAAC;YACD,OAAO;QACT,CAAC;QAED,IACE,6BAA6B,KAAK,mBAAmB,CAAC,QAAQ;YAC9D,6BAA6B,KAAK,mBAAmB,CAAC,gBAAgB,EACtE,CAAC;YACD,IAAI,CAAC,2BAA2B,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACvD,CAAC;QACD,IACE,6BAA6B,KAAK,mBAAmB,CAAC,KAAK;YAC3D,6BAA6B,KAAK,mBAAmB,CAAC,gBAAgB,EACtE,CAAC;YACD,IAAI,CAAC,2BAA2B,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACvD,CAAC;IACH,CAAC;IAEM,gBAAgB,CACrB,IAI6B;QAE7B,IAAI,CAAC,qBAAqB,GAAG,2BAA2B,CAAC;QAEzD,KAAK,MAAM,SAAS,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YACxC,IAAI,OAAO,CAAC,iBAAiB,CAAC,SAAS,EAAE,EAAE,CAAC,aAAa,CAAC,OAAO,CAAC,EAAE,CAAC;gBACnE,IAAI,CAAC,mBAAmB,CAAC,SAAS,CAAC,CAAC;YACtC,CAAC;QACH,CAAC;IACH,CAAC;IAEM,eAAe;QACpB,IAAI,CAAC,qBAAqB,GAAG,mBAAmB,CAAC;IACnD,CAAC;IAEM,mBAAmB;QACxB,IAAI,IAAI,CAAC,qBAAqB,KAAK,mBAAmB,EAAE,CAAC;YACvD,IAAI,CAAC,qBAAqB,IAAI,CAAC,CAAC;QAClC,CAAC;IACH,CAAC;IAEM,kBAAkB;QACvB,IAAI,IAAI,CAAC,qBAAqB,KAAK,mBAAmB,EAAE,CAAC;YACvD,IAAI,CAAC,qBAAqB,IAAI,CAAC,CAAC;QAClC,CAAC;IACH,CAAC;IAEM,qCAAqC;QAC1C,IAAI,CAAC,2BAA2B,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE;YACtD,IAAI,CAAC,wBAAwB,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,2BAA2B,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE;YACtD,IAAI,CAAC,wBAAwB,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC;QAEH,OAAO;YACL,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,wBAAwB,CAAC,MAAM,EAAE,CAAC;YACrD,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,wBAAwB,CAAC,MAAM,EAAE,CAAC;SACtD,CAAC;IACJ,CAAC;CACF"}