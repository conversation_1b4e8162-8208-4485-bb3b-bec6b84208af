import { AbstractModel } from './abstract-model';
import { StatusEvent } from './types';
export declare class GeminiWebModel extends AbstractModel {
    private atValue?;
    private blValue?;
    constructor();
    private fetchRequestParams;
    private initializeStorage;
    private validateExistingThreads;
    private isValidBardMetadata;
    getName(): string;
    supportsImageInput(): boolean;
    /**
     * Fetches conversation data for a specific conversation
     * @param conversationId The ID of the conversation to fetch
     * @returns The conversation data
     * @serverOperation This method makes direct API calls to Gemini's servers
     */
    getConversation(conversationId: string): Promise<any>;
    /**
     * Updates the title of a conversation
     * @param newTitle The new title to set for the conversation
     * @param emoji Optional emoji to display with the title
     * @serverOperation This method makes direct API calls to Gemini's servers
     */
    editTitle(newTitle: string, emoji?: string): Promise<void>;
    private getHeaders;
    /**
     * Ensures that we have valid request parameters (atValue and blValue)
     * @returns Promise that resolves when parameters are available
     */
    private ensureRequestParams;
    private getCurrentThreadSafe;
    initNewThread(): Promise<void>;
    loadThread(threadId: string): Promise<void>;
    protected doSendMessage(params: {
        prompt: string;
        images?: File[];
        model?: string;
        signal?: AbortSignal;
        onEvent: (event: StatusEvent) => void;
    }): Promise<void>;
}
