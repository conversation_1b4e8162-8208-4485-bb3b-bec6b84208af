/**
 * Model configuration and type definitions
 */

export interface ModelConfig {
  provider: 'gemini-web' | 'claude-web' | 'bing-web' | 'perplexity-web' | 'deepseek-web';
  internalModel?: string;
  mode?: string;
  style?: string;
  searchFocus?: string;
  description?: string;
  capabilities?: ModelCapabilities;
}

export interface ModelCapabilities {
  supportsStreaming: boolean;
  supportsImages: boolean;
  supportsSystemMessage: boolean;
  supportsFunctions: boolean;
  supportsReasoning: boolean;
  maxTokens?: number;
  maxImageSize?: number;
  maxImages?: number;
}

// Model registry mapping OpenAI model IDs to internal configurations
export const MODEL_REGISTRY: Record<string, ModelConfig> = {
  // GPT models mapped to various providers
  'gpt-4': {
    provider: 'claude-web',
    style: 'default',
    description: 'Claude Web (Default Style)',
    capabilities: {
      supportsStreaming: true,
      supportsImages: true,
      supportsSystemMessage: true,
      supportsFunctions: false,
      supportsReasoning: false,
      maxImages: 5
    }
  },
  'gpt-4-turbo': {
    provider: 'gemini-web',
    internalModel: 'gemini-2.0-flash',
    description: 'Gemini 2.0 Flash',
    capabilities: {
      supportsStreaming: true,
      supportsImages: true,
      supportsSystemMessage: false,
      supportsFunctions: false,
      supportsReasoning: false,
      maxImages: 1
    }
  },
  'gpt-4-vision': {
    provider: 'gemini-web',
    internalModel: 'gemini-2.5-pro-exp',
    description: 'Gemini 2.5 Pro Experimental',
    capabilities: {
      supportsStreaming: true,
      supportsImages: true,
      supportsSystemMessage: false,
      supportsFunctions: false,
      supportsReasoning: false,
      maxImages: 1
    }
  },
  'gpt-3.5-turbo': {
    provider: 'bing-web',
    mode: 'chat',
    description: 'Bing Copilot (Chat Mode)',
    capabilities: {
      supportsStreaming: true,
      supportsImages: true,
      supportsSystemMessage: false,
      supportsFunctions: false,
      supportsReasoning: false,
      maxImages: 1
    }
  },
  
  // Claude models
  'claude-3-opus': {
    provider: 'claude-web',
    style: 'default',
    description: 'Claude Web (Default Style)',
    capabilities: {
      supportsStreaming: true,
      supportsImages: true,
      supportsSystemMessage: true,
      supportsFunctions: false,
      supportsReasoning: false,
      maxImages: 5
    }
  },
  'claude-3-sonnet': {
    provider: 'claude-web',
    style: 'concise',
    description: 'Claude Web (Concise Style)',
    capabilities: {
      supportsStreaming: true,
      supportsImages: true,
      supportsSystemMessage: true,
      supportsFunctions: false,
      supportsReasoning: false,
      maxImages: 5
    }
  },
  
  // Gemini models
  'gemini-pro': {
    provider: 'gemini-web',
    internalModel: 'gemini-2.5-pro-exp',
    description: 'Gemini 2.5 Pro Experimental',
    capabilities: {
      supportsStreaming: true,
      supportsImages: true,
      supportsSystemMessage: false,
      supportsFunctions: false,
      supportsReasoning: false,
      maxImages: 1
    }
  },
  'gemini-flash': {
    provider: 'gemini-web',
    internalModel: 'gemini-2.0-flash',
    description: 'Gemini 2.0 Flash',
    capabilities: {
      supportsStreaming: true,
      supportsImages: true,
      supportsSystemMessage: false,
      supportsFunctions: false,
      supportsReasoning: false,
      maxImages: 1
    }
  },
  'gemini-thinking': {
    provider: 'gemini-web',
    internalModel: 'gemini-2.0-flash-thinking',
    description: 'Gemini 2.0 Flash Thinking',
    capabilities: {
      supportsStreaming: true,
      supportsImages: true,
      supportsSystemMessage: false,
      supportsFunctions: false,
      supportsReasoning: true,
      maxImages: 1
    }
  },
  'gemini-advanced': {
    provider: 'gemini-web',
    internalModel: 'gemini-2.0-exp-advanced',
    description: 'Gemini 2.0 Experimental Advanced',
    capabilities: {
      supportsStreaming: true,
      supportsImages: true,
      supportsSystemMessage: false,
      supportsFunctions: false,
      supportsReasoning: false,
      maxImages: 1
    }
  },
  
  // Deepseek models
  'deepseek-chat': {
    provider: 'deepseek-web',
    mode: 'chat',
    description: 'Deepseek (Chat Mode)',
    capabilities: {
      supportsStreaming: true,
      supportsImages: true,
      supportsSystemMessage: false,
      supportsFunctions: false,
      supportsReasoning: false,
      maxImages: 4
    }
  },
  'deepseek-reasoning': {
    provider: 'deepseek-web',
    mode: 'reasoning',
    description: 'Deepseek (Reasoning Mode)',
    capabilities: {
      supportsStreaming: true,
      supportsImages: true,
      supportsSystemMessage: false,
      supportsFunctions: false,
      supportsReasoning: true,
      maxImages: 4
    }
  },
  
  // Perplexity models
  'perplexity-online': {
    provider: 'perplexity-web',
    searchFocus: 'internet',
    description: 'Perplexity (Internet Search)',
    capabilities: {
      supportsStreaming: true,
      supportsImages: true,
      supportsSystemMessage: false,
      supportsFunctions: false,
      supportsReasoning: false,
      maxImages: 4
    }
  },
  'perplexity-writing': {
    provider: 'perplexity-web',
    searchFocus: 'writing',
    description: 'Perplexity (Writing Mode)',
    capabilities: {
      supportsStreaming: true,
      supportsImages: true,
      supportsSystemMessage: false,
      supportsFunctions: false,
      supportsReasoning: false,
      maxImages: 4
    }
  },
  
  // Bing models
  'bing-creative': {
    provider: 'bing-web',
    mode: 'chat',
    description: 'Bing Copilot (Creative)',
    capabilities: {
      supportsStreaming: true,
      supportsImages: true,
      supportsSystemMessage: false,
      supportsFunctions: false,
      supportsReasoning: false,
      maxImages: 1
    }
  },
  'bing-reasoning': {
    provider: 'bing-web',
    mode: 'reasoning',
    description: 'Bing Copilot (Reasoning)',
    capabilities: {
      supportsStreaming: true,
      supportsImages: true,
      supportsSystemMessage: false,
      supportsFunctions: false,
      supportsReasoning: true,
      maxImages: 1
    }
  }
};

// WebSocket message types for extension communication
export interface ExtensionMessage {
  id: string;
  type: 'request' | 'response' | 'error' | 'status' | 'ping' | 'pong';
  timestamp: number;
  data?: any;
}

export interface ChatRequest {
  id: string;
  provider: string;
  model?: string;
  messages: Array<{
    role: string;
    content: string;
    images?: string[]; // Base64 encoded images
  }>;
  options?: {
    temperature?: number;
    maxTokens?: number;
    stream?: boolean;
    mode?: string;
    style?: string;
    searchFocus?: string;
    searchSources?: string[];
    searchEnabled?: boolean;
  };
}

export interface ChatResponse {
  id: string;
  content?: string;
  reasoningContent?: string;
  done?: boolean;
  error?: string;
  metadata?: {
    model?: string;
    tokensUsed?: number;
    suggestedResponses?: string[];
  };
}

export interface ModelStatus {
  provider: string;
  available: boolean;
  authenticated: boolean;
  error?: string;
  models?: string[];
}
