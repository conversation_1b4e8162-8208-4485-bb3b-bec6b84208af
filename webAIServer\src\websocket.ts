import { WebSocketServer, WebSocket } from 'ws';
import { EventEmitter } from 'events';
import { nanoid } from 'nanoid';
import winston from 'winston';
import { ExtensionMessage, ChatRequest, ChatResponse, ModelStatus } from './types/models';

export class WebSocketManager extends EventEmitter {
  private wss: WebSocketServer;
  private clients: Map<string, WebSocket> = new Map();
  private pendingRequests: Map<string, (response: any) => void> = new Map();
  private logger: winston.Logger;

  constructor(port: number, logger: winston.Logger) {
    super();
    this.logger = logger;

    this.wss = new WebSocketServer({ port });

    this.wss.on('connection', (ws) => {
      const clientId = nanoid();
      this.handleNewConnection(ws, clientId);
    });

    this.logger.info(`WebSocket server listening on port ${port}`);
  }

  private handleNewConnection(ws: WebSocket, clientId: string) {
    this.clients.set(clientId, ws);
    this.logger.info(`New WebSocket connection: ${clientId}`);

    // Send initial connection confirmation
    this.sendMessage(ws, {
      id: nanoid(),
      type: 'status',
      timestamp: Date.now(),
      data: { connected: true, clientId }
    });

    ws.on('message', (data) => {
      try {
        const message = JSON.parse(data.toString()) as ExtensionMessage;
        this.handleMessage(clientId, message);
      } catch (error) {
        this.logger.error(`Error parsing WebSocket message: ${error}`);
      }
    });

    ws.on('close', () => {
      this.clients.delete(clientId);
      this.logger.info(`WebSocket connection closed: ${clientId}`);
      this.emit('client-disconnected', clientId);
    });

    ws.on('error', (error) => {
      this.logger.error(`WebSocket error for client ${clientId}: ${error}`);
    });

    // Setup ping/pong for connection health
    const pingInterval = setInterval(() => {
      if (ws.readyState === WebSocket.OPEN) {
        this.sendMessage(ws, {
          id: nanoid(),
          type: 'ping',
          timestamp: Date.now()
        });
      } else {
        clearInterval(pingInterval);
      }
    }, 30000); // Ping every 30 seconds

    ws.on('pong', () => {
      // Connection is alive
    });
  }

  private handleMessage(clientId: string, message: ExtensionMessage) {
    this.logger.debug(`Received message from ${clientId}: ${message.type}`);

    switch (message.type) {
      case 'response':
        this.handleResponse(message);
        break;
      case 'status':
        this.emit('model-status', message.data);
        break;
      case 'pong':
        // Connection is alive
        break;
      default:
        this.emit('message', { clientId, message });
    }
  }

  private handleResponse(message: ExtensionMessage) {
    const requestId = message.data?.requestId;
    if (requestId && this.pendingRequests.has(requestId)) {
      const resolver = this.pendingRequests.get(requestId);
      this.pendingRequests.delete(requestId);
      resolver!(message.data);
    }
  }

  private sendMessage(ws: WebSocket, message: ExtensionMessage) {
    if (ws.readyState === WebSocket.OPEN) {
      ws.send(JSON.stringify(message));
    }
  }

  public async sendChatRequest(request: ChatRequest): Promise<ChatResponse> {
    const client = this.getActiveClient();
    if (!client) {
      throw new Error('No active WebSocket connection to extension');
    }

    return new Promise((resolve, reject) => {
      const requestId = request.id;
      const timeout = setTimeout(() => {
        this.pendingRequests.delete(requestId);
        reject(new Error('Request timeout'));
      }, 120000); // 2 minute timeout

      this.pendingRequests.set(requestId, (response: ChatResponse) => {
        clearTimeout(timeout);
        resolve(response);
      });

      this.sendMessage(client, {
        id: nanoid(),
        type: 'request',
        timestamp: Date.now(),
        data: request
      });
    });
  }

  public streamChatRequest(request: ChatRequest, onUpdate: (response: ChatResponse) => void): void {
    const client = this.getActiveClient();
    if (!client) {
      throw new Error('No active WebSocket connection to extension');
    }

    // For streaming, we'll handle multiple response messages
    const streamId = request.id;
    
    const messageHandler = (data: Buffer) => {
      try {
        const message = JSON.parse(data.toString()) as ExtensionMessage;
        if (message.type === 'response' && message.data?.id === streamId) {
          onUpdate(message.data as ChatResponse);
          
          // If done, remove the handler
          if (message.data.done) {
            client.off('message', messageHandler);
          }
        }
      } catch (error) {
        this.logger.error(`Error handling stream message: ${error}`);
      }
    };

    client.on('message', messageHandler);

    this.sendMessage(client, {
      id: nanoid(),
      type: 'request',
      timestamp: Date.now(),
      data: request
    });
  }

  public async getModelStatus(): Promise<ModelStatus[]> {
    const client = this.getActiveClient();
    if (!client) {
      return [];
    }

    return new Promise((resolve) => {
      const requestId = nanoid();
      const timeout = setTimeout(() => {
        this.pendingRequests.delete(requestId);
        resolve([]);
      }, 5000);

      this.pendingRequests.set(requestId, (response: ModelStatus[]) => {
        clearTimeout(timeout);
        resolve(response);
      });

      this.sendMessage(client, {
        id: requestId,
        type: 'status',
        timestamp: Date.now(),
        data: { request: 'model-status' }
      });
    });
  }

  private getActiveClient(): WebSocket | null {
    // Return the first active client
    for (const [, client] of this.clients) {
      if (client.readyState === WebSocket.OPEN) {
        return client;
      }
    }
    return null;
  }

  public isConnected(): boolean {
    return this.getActiveClient() !== null;
  }

  public getClientCount(): number {
    return this.clients.size;
  }

  public close() {
    this.wss.close();
  }
}
