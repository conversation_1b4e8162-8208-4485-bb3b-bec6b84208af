/**
 * Model Manager for WebAI Extension
 * Handles AI model interactions using the ai-models-bridge library
 */

import {
  <PERSON><PERSON>eb<PERSON>odel,
  GeminiWebModel,
  BingWebModel,
  PerplexityWebModel,
  DeepseekWebModel,
  AIModelError
} from '../../lib/ai-models-bridge.esm.js';

export class ModelManager {
  constructor() {
    this.models = new Map(); // Cache for initialized models
    this.conversations = new Map(); // Track conversation threads
    this.isInitialized = false;

    // Define available models
    this.availableModels = [
      { id: 'claude-3-opus', name: 'Claude 3 Opus', provider: 'claude-web', class: ClaudeWebModel },
      { id: 'claude-3-sonnet', name: 'Claude 3 Sonnet', provider: 'claude-web', class: ClaudeWebModel },
      { id: 'claude-3-haiku', name: '<PERSON> 3 Haiku', provider: 'claude-web', class: ClaudeWebModel },
      { id: 'gemini-pro', name: '<PERSON> Pro', provider: 'gemini-web', class: GeminiWebModel },
      { id: 'gemini-2.0-flash', name: 'Gemini 2.0 Flash', provider: 'gemini-web', class: GeminiWebModel },
      { id: 'gpt-4', name: 'GPT-4 (via Copilot)', provider: 'bing-web', class: BingWebModel },
      { id: 'perplexity-pro', name: 'Perplexity Pro', provider: 'pplx-web', class: PerplexityWebModel },
      { id: 'deepseek-chat', name: 'DeepSeek Chat', provider: 'deepseek-web', class: DeepseekWebModel }
    ];
  }

  /**
   * Initialize the model manager
   */
  async initialize() {
    if (this.isInitialized) return;

    console.log('[ModelManager] Initializing...');

    try {
      console.log('[ModelManager] Available models:', this.availableModels.length);

      this.isInitialized = true;
      console.log('[ModelManager] Initialized successfully');
    } catch (error) {
      console.error('[ModelManager] Failed to initialize:', error);
      throw error;
    }
  }

  /**
   * Get available models from the library
   */
  async getAvailableModels() {
    try {
      // Return the predefined available models
      return this.availableModels.map(model => ({
        id: model.id,
        name: model.name,
        provider: model.provider,
        description: `${model.name} - Web-based AI model`,
        capabilities: ['chat', 'text-generation'],
        requiresAuth: true,
        webBased: true
      }));
    } catch (error) {
      console.error('[ModelManager] Error getting available models:', error);
      throw error;
    }
  }

  /**
   * Get model status for all cached models
   */
  async getModelStatus() {
    const status = {};

    for (const [modelId, model] of this.models) {
      try {
        status[modelId] = {
          initialized: true,
          ready: await this.isModelReady(model),
          conversations: this.getModelConversationCount(modelId)
        };
      } catch (error) {
        status[modelId] = {
          initialized: false,
          error: error.message,
          conversations: 0
        };
      }
    }

    return status;
  }

  /**
   * Test a specific model
   */
  async testModel(provider, modelName) {
    console.log(`[ModelManager] Testing model: ${provider}:${modelName}`);

    try {
      const model = await this.getOrCreateModel(provider, modelName);

      // Send a simple test message
      const testMessage = {
        role: 'user',
        content: 'Hello! Please respond with just "Test successful" to confirm you are working.'
      };

      const response = await model.sendMessage([testMessage]);

      return {
        success: true,
        response: response.content,
        provider,
        model: modelName
      };
    } catch (error) {
      console.error(`[ModelManager] Test failed for ${provider}:${modelName}:`, error);
      return {
        success: false,
        error: error.message,
        provider,
        model: modelName
      };
    }
  }

  /**
   * Send a message to a model
   */
  async sendMessage(request) {
    const { id, model: modelId, messages, stream = false, ...options } = request;

    console.log(`[ModelManager] Sending message to model: ${modelId}`);

    try {
      // Parse model ID (format: provider:model or just model)
      const [provider, modelName] = this.parseModelId(modelId);

      // Get or create model instance
      const model = await this.getOrCreateModel(provider, modelName);

      // Get or create conversation thread
      const conversationId = options.conversationId || `${modelId}-${Date.now()}`;
      let conversation = this.conversations.get(conversationId);

      if (!conversation) {
        conversation = {
          id: conversationId,
          model: modelId,
          messages: [],
          created: Date.now()
        };
        this.conversations.set(conversationId, conversation);
      }

      // Add new messages to conversation
      conversation.messages.push(...messages);

      if (stream) {
        return await this.sendStreamingMessage(model, conversation, id, options);
      } else {
        return await this.sendSingleMessage(model, conversation, id, options);
      }
    } catch (error) {
      console.error('[ModelManager] Error sending message:', error);
      throw error;
    }
  }

  /**
   * Send a single (non-streaming) message
   */
  async sendSingleMessage(model, conversation, requestId, options) {
    try {
      const response = await model.sendMessage(conversation.messages, options);

      // Add response to conversation
      conversation.messages.push({
        role: 'assistant',
        content: response.content
      });

      return {
        id: requestId,
        choices: [{
          index: 0,
          message: {
            role: 'assistant',
            content: response.content
          },
          finish_reason: 'stop'
        }],
        usage: response.usage || {},
        done: true
      };
    } catch (error) {
      console.error('[ModelManager] Error in single message:', error);
      throw error;
    }
  }

  /**
   * Send a streaming message
   */
  async sendStreamingMessage(model, conversation, requestId, options) {
    try {
      const stream = await model.sendMessageStream(conversation.messages, options);
      let fullContent = '';

      return new Promise((resolve, reject) => {
        const chunks = [];

        stream.on('data', (chunk) => {
          fullContent += chunk.content || '';

          const responseChunk = {
            id: requestId,
            choices: [{
              index: 0,
              delta: {
                role: 'assistant',
                content: chunk.content || ''
              },
              finish_reason: null
            }],
            done: false
          };

          chunks.push(responseChunk);
        });

        stream.on('end', () => {
          // Add final response to conversation
          conversation.messages.push({
            role: 'assistant',
            content: fullContent
          });

          // Send final chunk
          const finalChunk = {
            id: requestId,
            choices: [{
              index: 0,
              delta: {},
              finish_reason: 'stop'
            }],
            usage: stream.usage || {},
            done: true
          };

          chunks.push(finalChunk);
          resolve({ chunks, fullContent });
        });

        stream.on('error', (error) => {
          console.error('[ModelManager] Stream error:', error);
          reject(error);
        });
      });
    } catch (error) {
      console.error('[ModelManager] Error in streaming message:', error);
      throw error;
    }
  }

  /**
   * Get or create a model instance
   */
  async getOrCreateModel(provider, modelName) {
    const modelKey = `${provider}:${modelName}`;

    if (this.models.has(modelKey)) {
      return this.models.get(modelKey);
    }

    console.log(`[ModelManager] Creating new model instance: ${modelKey}`);

    try {
      // Find the model configuration
      const modelConfig = this.availableModels.find(m =>
        m.id === modelName || m.provider === provider
      );

      if (!modelConfig) {
        throw new AIModelError(`Model ${modelName} not found`);
      }

      // Create instance of the appropriate model class
      const model = new modelConfig.class();
      this.models.set(modelKey, model);
      return model;
    } catch (error) {
      console.error(`[ModelManager] Failed to create model ${modelKey}:`, error);
      throw error;
    }
  }

  /**
   * Parse model ID into provider and model name
   */
  parseModelId(modelId) {
    if (modelId.includes(':')) {
      const [provider, ...modelParts] = modelId.split(':');
      return [provider, modelParts.join(':')];
    }

    // Default provider mapping for common models
    const defaultProviders = {
      'gpt-4': 'openai',
      'gpt-3.5-turbo': 'openai',
      'claude-3-opus': 'anthropic',
      'claude-3-sonnet': 'anthropic',
      'claude-3-haiku': 'anthropic',
      'gemini-pro': 'google',
      'gemini-1.5-pro': 'google'
    };

    const provider = defaultProviders[modelId] || 'unknown';
    return [provider, modelId];
  }

  /**
   * Check if a model is ready
   */
  async isModelReady(model) {
    try {
      // Simple health check
      return model && typeof model.sendMessage === 'function';
    } catch (error) {
      return false;
    }
  }

  /**
   * Get conversation count for a model
   */
  getModelConversationCount(modelId) {
    let count = 0;
    for (const conversation of this.conversations.values()) {
      if (conversation.model === modelId) {
        count++;
      }
    }
    return count;
  }

  /**
   * Clear old conversations (cleanup)
   */
  clearOldConversations(maxAge = 24 * 60 * 60 * 1000) { // 24 hours default
    const now = Date.now();
    const toDelete = [];

    for (const [id, conversation] of this.conversations) {
      if (now - conversation.created > maxAge) {
        toDelete.push(id);
      }
    }

    toDelete.forEach(id => this.conversations.delete(id));

    if (toDelete.length > 0) {
      console.log(`[ModelManager] Cleared ${toDelete.length} old conversations`);
    }
  }

  /**
   * Get conversation by ID
   */
  getConversation(conversationId) {
    return this.conversations.get(conversationId);
  }

  /**
   * Delete a conversation
   */
  deleteConversation(conversationId) {
    return this.conversations.delete(conversationId);
  }

  /**
   * Get all conversations for a model
   */
  getModelConversations(modelId) {
    const conversations = [];
    for (const conversation of this.conversations.values()) {
      if (conversation.model === modelId) {
        conversations.push(conversation);
      }
    }
    return conversations;
  }

  /**
   * Cleanup resources
   */
  async cleanup() {
    console.log('[ModelManager] Cleaning up...');

    // Clear all conversations
    this.conversations.clear();

    // Clear model cache
    this.models.clear();

    this.isInitialized = false;
  }
}
