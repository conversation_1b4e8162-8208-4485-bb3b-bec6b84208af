interface DeepSeekChallenge {
    algorithm: string;
    challenge: string;
    salt: string;
    signature: string;
    difficulty: number;
    expire_at?: number;
    expire_after?: number;
    target_path?: string;
    [key: string]: any;
}
/**
 * Solves the DeepSeek "DeepSeekHashV1" Proof-of-Work challenge using the correct
 * difficulty interpretation (comparing hash value against a target threshold).
 *
 * @param challengeObj - The challenge object received from the API.
 * @returns A Promise resolving to the Base64-encoded JSON string for the header.
 * @throws If the algorithm is not "DeepSeekHashV1" or if difficulty is missing/invalid.
 */
export declare function solveDeepSeekPow(challengeObj: DeepSeekChallenge): Promise<string>;
export {};
