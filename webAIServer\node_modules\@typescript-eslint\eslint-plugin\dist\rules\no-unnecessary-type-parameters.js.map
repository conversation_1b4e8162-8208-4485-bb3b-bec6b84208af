{"version": 3, "file": "no-unnecessary-type-parameters.js", "sourceRoot": "", "sources": ["../../src/rules/no-unnecessary-type-parameters.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAEA,oDAA0D;AAC1D,sDAAwC;AACxC,+CAAiC;AAGjC,kCAAwD;AAOxD,kBAAe,IAAA,iBAAU,EAAC;IACxB,cAAc,EAAE,EAAE;IAClB,IAAI,EAAE;QACJ,IAAI,EAAE;YACJ,WAAW,EAAE,gDAAgD;YAC7D,oBAAoB,EAAE,IAAI;SAC3B;QACD,QAAQ,EAAE;YACR,IAAI,EAAE,4CAA4C;SACnD;QACD,MAAM,EAAE,EAAE;QACV,IAAI,EAAE,SAAS;KAChB;IACD,IAAI,EAAE,gCAAgC;IACtC,MAAM,CAAC,OAAO;QACZ,MAAM,cAAc,GAAG,IAAA,wBAAiB,EAAC,OAAO,CAAC,CAAC;QAClD,OAAO;YACL,CAAC;gBACC,yCAAyC;gBACzC,kCAAkC;gBAClC,iCAAiC;gBACjC,qCAAqC;gBACrC,oCAAoC;gBACpC,4CAA4C;gBAC5C,mCAAmC;gBACnC,mCAAmC;gBACnC,+CAA+C;gBAC/C,gCAAgC;gBAChC,mCAAmC;aACpC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,IAA2B;gBACvC,MAAM,MAAM,GAAG,cAAc,CAAC,qBAAqB,CAAC,GAAG,CACrD,IAAI,CACqB,CAAC;gBAE5B,MAAM,OAAO,GAAG,cAAc,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;gBACxD,IAAI,MAA8C,CAAC;gBAEnD,KAAK,MAAM,aAAa,IAAI,MAAM,CAAC,cAAc,EAAE,CAAC;oBAClD,MAAM,eAAe,GACnB,cAAc,CAAC,qBAAqB,CAAC,GAAG,CACtC,aAAa,CACd,CAAC;oBACJ,MAAM,KAAK,GAAG,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC;oBAE3D,uEAAuE;oBACvE,yDAAyD;oBACzD,IAAI,4BAA4B,CAAC,eAAe,EAAE,KAAK,CAAC,UAAU,CAAC,EAAE,CAAC;wBACpE,SAAS;oBACX,CAAC;oBAED,6DAA6D;oBAC7D,MAAM,KAAK,uBAAuB,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;oBACpD,MAAM,gBAAgB,GAAG,MAAM,CAAC,GAAG,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;oBACxD,IAAI,CAAC,gBAAgB,IAAI,gBAAgB,GAAG,CAAC,EAAE,CAAC;wBAC9C,SAAS;oBACX,CAAC;oBAED,OAAO,CAAC,MAAM,CAAC;wBACb,IAAI,EAAE;4BACJ,IAAI,EAAE,aAAa,CAAC,IAAI,CAAC,IAAI;yBAC9B;wBACD,IAAI,EAAE,eAAe;wBACrB,SAAS,EAAE,MAAM;qBAClB,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC;AAEH,SAAS,4BAA4B,CACnC,IAA8B,EAC9B,UAAuB;IAEvB,IAAI,KAAK,GAAG,CAAC,CAAC;IAEd,KAAK,MAAM,SAAS,IAAI,UAAU,EAAE,CAAC;QACnC,iEAAiE;QACjE,IACE,SAAS,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;YAC7C,SAAS,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAC7C,CAAC;YACD,SAAS;QACX,CAAC;QAED,gEAAgE;QAChE,yEAAyE;QACzE,yDAAyD;QACzD,IACE,CAAC,SAAS,CAAC,eAAe;YAC1B,SAAS,CAAC,UAAU,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,CAAC,IAAI,EAC5C,CAAC;YACD,SAAS;QACX,CAAC;QAED,kEAAkE;QAClE,iEAAiE;QACjE,IAAI,SAAS,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,eAAe,EAAE,CAAC;YACxE,MAAM,WAAW,GAAG,sBAAsB,CACxC,SAAS,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CACnC,CAAC;YACF,IACE,WAAW,CAAC,IAAI,KAAK,sBAAc,CAAC,4BAA4B;gBAChE,WAAW,CAAC,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,UAAU,CAAC,MAAM,CAAC,EACxD,CAAC;gBACD,OAAO,IAAI,CAAC;YACd,CAAC;QACH,CAAC;QAED,KAAK,IAAI,CAAC,CAAC;QAEX,IAAI,KAAK,GAAG,CAAC,EAAE,CAAC;YACd,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAED,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,sBAAsB,CAAC,IAAmB;IACjD,QAAQ,IAAI,CAAC,IAAI,EAAE,CAAC;QAClB,KAAK,sBAAc,CAAC,kBAAkB,CAAC;QACvC,KAAK,sBAAc,CAAC,WAAW;YAC7B,OAAO,sBAAsB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC7C;YACE,OAAO,IAAI,CAAC;IAChB,CAAC;AACH,CAAC;AAED;;;;;GAKG;AACH,SAAS,uBAAuB,CAC9B,OAAuB,EACvB,IAA4B;IAE5B,MAAM,MAAM,GAAG,IAAI,GAAG,EAAyB,CAAC;IAEhD,IAAI,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC;QACzB,KAAK,MAAM,aAAa,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YAChD,+BAA+B,CAAC,OAAO,EAAE,aAAa,EAAE,MAAM,CAAC,CAAC;QAClE,CAAC;QACD,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YAClC,+BAA+B,CAAC,OAAO,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;QAC3D,CAAC;IACH,CAAC;SAAM,CAAC;QACN,+BAA+B,CAAC,OAAO,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;IACzD,CAAC;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;;GAIG;AACH,SAAS,+BAA+B,CACtC,OAAuB,EACvB,IAAa,EACb,qBAAiD;IAEjD,MAAM,kBAAkB,GAAG,IAAI,GAAG,EAAe,CAAC;IAClD,MAAM,IAAI,GAAG,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;IAC7C,MAAM,UAAU,GAAG,IAAI,GAAG,EAAmB,CAAC;IAC9C,MAAM,kBAAkB,GAAG,IAAI,GAAG,EAAe,CAAC;IAClD,IAAI,gBAAgB,GAAG,KAAK,CAAC;IAC7B,IAAI,cAAc,GAAG,KAAK,CAAC;IAE3B,IACE,EAAE,CAAC,0BAA0B,CAAC,IAAI,CAAC;QACnC,EAAE,CAAC,wBAAwB,CAAC,IAAI,CAAC,EACjC,CAAC;QACD,gBAAgB,GAAG,IAAI,CAAC;QACxB,cAAc,CAAC,OAAO,CAAC,2BAA2B,CAAC,IAAI,CAAC,CAAC,CAAC;IAC5D,CAAC;IAED,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACtB,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IACzB,CAAC;IAED,SAAS,SAAS,CAChB,IAAyB,EACzB,kBAA2B;QAE3B,qEAAqE;QACrE,yDAAyD;QACzD,wEAAwE;QACxE,gEAAgE;QAChE,IAAI,CAAC,IAAI,IAAI,mBAAmB,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;YAC3C,OAAO;QACT,CAAC;QAED,6DAA6D;QAC7D,IAAK,OAAO,CAAC,eAA8C,CAAC,IAAI,CAAC,EAAE,CAAC;YAClE,MAAM,WAAW,GAAG,IAAI,CAAC,SAAS,EAAE,EAAE,eAAe,EAAE,EAAE,CAAC,CAAC,CAE9C,CAAC;YAEd,IAAI,WAAW,EAAE,CAAC;gBAChB,wBAAwB,CAAC,WAAW,CAAC,IAAI,EAAE,kBAAkB,CAAC,CAAC;gBAE/D,sEAAsE;gBACtE,sEAAsE;gBACtE,IACE,WAAW,CAAC,UAAU;oBACtB,CAAC,kBAAkB,CAAC,GAAG,CAAC,WAAW,CAAC,UAAU,CAAC,EAC/C,CAAC;oBACD,kBAAkB,CAAC,GAAG,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;oBAC/C,SAAS,CAAC,OAAO,CAAC,iBAAiB,CAAC,WAAW,CAAC,UAAU,CAAC,EAAE,KAAK,CAAC,CAAC;gBACtE,CAAC;gBAED,IAAI,WAAW,CAAC,OAAO,IAAI,CAAC,cAAc,EAAE,CAAC;oBAC3C,cAAc,GAAG,IAAI,CAAC;oBACtB,SAAS,CAAC,OAAO,CAAC,iBAAiB,CAAC,WAAW,CAAC,OAAO,CAAC,EAAE,KAAK,CAAC,CAAC;gBACnE,CAAC;YACH,CAAC;QACH,CAAC;QAED,wCAAwC;aACnC,IAAI,OAAO,CAAC,yBAAyB,CAAC,IAAI,CAAC,EAAE,CAAC;YACjD,cAAc,CAAC,IAAI,CAAC,KAAK,EAAE,kBAAkB,CAAC,CAAC;QACjD,CAAC;QAED,iCAAiC;aAC5B,IAAI,OAAO,CAAC,mBAAmB,CAAC,IAAI,CAAC,EAAE,CAAC;YAC3C,SAAS,CAAC,IAAI,CAAC,UAAU,EAAE,kBAAkB,CAAC,CAAC;YAC/C,SAAS,CAAC,IAAI,CAAC,SAAS,EAAE,kBAAkB,CAAC,CAAC;QAChD,CAAC;QAED,4BAA4B;QAC5B,2CAA2C;aACtC,IAAI,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,EAAE,CAAC;YACpE,KAAK,MAAM,YAAY,IAAI,IAAI,CAAC,aAAa,IAAI,EAAE,EAAE,CAAC;gBACpD,SAAS,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;YAChC,CAAC;QACH,CAAC;QAED,kCAAkC;aAC7B,IAAI,OAAO,CAAC,qBAAqB,CAAC,IAAI,CAAC,EAAE,CAAC;YAC7C,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;gBACjC,SAAS,CAAC,OAAO,EAAE,kBAAkB,CAAC,CAAC;YACzC,CAAC;QACH,CAAC;QAED,wDAAwD;aACnD,IAAI,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,EAAE,CAAC;YACzC,SAAS,CAAC,IAAI,CAAC,SAAS,EAAE,kBAAkB,CAAC,CAAC;YAC9C,SAAS,CAAC,IAAI,CAAC,WAAW,EAAE,kBAAkB,CAAC,CAAC;QAClD,CAAC;QAED,oDAAoD;QACpD,+DAA+D;QAC/D,qEAAqE;QACrE,6CAA6C;aACxC,IAAI,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC;YACpC,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;YACxC,oBAAoB,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;YAExC,IAAI,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC;gBACvB,SAAS,CAAC,IAAI,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;gBACrC,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;oBAC5B,+DAA+D;oBAC/D,6DAA6D;oBAC7D,SAAS,CAAC,IAAI,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;gBACtC,CAAC;YACH,CAAC;YAED,KAAK,MAAM,YAAY,IAAI,IAAI,CAAC,kBAAkB,IAAI,EAAE,EAAE,CAAC;gBACzD,SAAS,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;YAChC,CAAC;YAED,SAAS,CAAC,IAAI,CAAC,kBAAkB,EAAE,EAAE,IAAI,CAAC,CAAC;YAC3C,SAAS,CAAC,IAAI,CAAC,kBAAkB,EAAE,EAAE,IAAI,CAAC,CAAC;YAE3C,IAAI,CAAC,iBAAiB,EAAE,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE;gBAC3C,gBAAgB,GAAG,IAAI,CAAC;gBACxB,cAAc,CAAC,SAAS,CAAC,CAAC;YAC5B,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,sBAAsB,EAAE,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE;gBAChD,gBAAgB,GAAG,IAAI,CAAC;gBACxB,cAAc,CAAC,SAAS,CAAC,CAAC;YAC5B,CAAC,CAAC,CAAC;QACL,CAAC;QAED,2CAA2C;aACtC,IAAI,cAAc,CAAC,IAAI,CAAC,EAAE,CAAC;YAC9B,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,kBAAkB,CAAC,CAAC;QAC3C,CAAC;QAED,6DAA6D;aACxD,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;YACjC,cAAc,CAAC,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,CAAC;QAChD,CAAC;IACH,CAAC;IAED,SAAS,wBAAwB,CAC/B,EAAiB,EACjB,kBAA2B;QAE3B,MAAM,eAAe,GAAG,qBAAqB,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;QAC3D,MAAM,KAAK,GAAG,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzC,qBAAqB,CAAC,GAAG,CAAC,EAAE,EAAE,eAAe,GAAG,KAAK,CAAC,CAAC;IACzD,CAAC;IAED,SAAS,mBAAmB,CAAC,IAAa;QACxC,MAAM,KAAK,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;QAC9C,UAAU,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QAC5B,OAAO,KAAK,CAAC;IACf,CAAC;IAED,SAAS,cAAc,CAAC,SAAmC;QACzD,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,OAAO;QACT,CAAC;QAED,IAAI,SAAS,CAAC,aAAa,EAAE,CAAC;YAC5B,SAAS,CAAC,OAAO,CAAC,eAAe,CAAC,SAAS,CAAC,aAAa,CAAC,EAAE,KAAK,CAAC,CAAC;QACrE,CAAC;QAED,KAAK,MAAM,SAAS,IAAI,SAAS,CAAC,UAAU,EAAE,CAAC;YAC7C,SAAS,CAAC,OAAO,CAAC,eAAe,CAAC,SAAS,CAAC,EAAE,KAAK,CAAC,CAAC;QACvD,CAAC;QAED,KAAK,MAAM,aAAa,IAAI,SAAS,CAAC,iBAAiB,EAAE,IAAI,EAAE,EAAE,CAAC;YAChE,SAAS,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;QAClC,CAAC;QAED,SAAS,CACP,OAAO,CAAC,2BAA2B,CAAC,SAAS,CAAC,EAAE,IAAI;YAClD,SAAS,CAAC,aAAa,EAAE,EAC3B,KAAK,CACN,CAAC;IACJ,CAAC;IAED,SAAS,oBAAoB,CAC3B,OAAoB,EACpB,kBAA2B;QAE3B,IAAI,kBAAkB,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC;YACpC,OAAO;QACT,CAAC;QAED,kBAAkB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAEhC,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;YAC7B,SAAS,CAAC,OAAO,CAAC,eAAe,CAAC,MAAM,CAAC,EAAE,kBAAkB,CAAC,CAAC;QACjE,CAAC;IACH,CAAC;IAED,SAAS,cAAc,CACrB,KAAyB,EACzB,kBAA2B;QAE3B,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;YACzB,SAAS,CAAC,IAAI,EAAE,kBAAkB,CAAC,CAAC;QACtC,CAAC;IACH,CAAC;AACH,CAAC;AAQD,SAAS,YAAY,CAAC,IAAa;IACjC,OAAO,eAAe,IAAI,IAAI,CAAC;AACjC,CAAC;AAMD,SAAS,cAAc,CAAC,IAAa;IACnC,OAAO,MAAM,IAAI,IAAI,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;AACvC,CAAC"}