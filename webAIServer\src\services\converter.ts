import { nanoid } from 'nanoid';
import {
  ChatCompletionRequest,
  ChatCompletionResponse,
  ChatCompletionChunk,
  ChatMessage as OpenAIMessage,
  ErrorResponse
} from '../types/openai';
import { ChatRequest, ChatResponse, MODEL_REGISTRY } from '../types/models';

export class FormatConverter {
  /**
   * Convert OpenAI chat completion request to internal format
   */
  public static openAIToInternal(request: ChatCompletionRequest): ChatRequest {
    const modelConfig = MODEL_REGISTRY[request.model];
    if (!modelConfig) {
      throw new Error(`Model ${request.model} not found in registry`);
    }

    // Convert messages
    const messages = request.messages.map(msg => {
      // Handle system messages (combine with first user message if not supported)
      if (msg.role === 'system' && !modelConfig.capabilities?.supportsSystemMessage) {
        return null; // Will be handled below
      }

      return {
        role: msg.role,
        content: msg.content || '',
        images: [] // Will be populated if needed
      };
    }).filter(Boolean) as any[];

    // If system message exists but not supported, prepend to first user message
    const systemMessage = request.messages.find(m => m.role === 'system');
    if (systemMessage && !modelConfig.capabilities?.supportsSystemMessage) {
      const firstUserIndex = messages.findIndex(m => m.role === 'user');
      if (firstUserIndex !== -1) {
        messages[firstUserIndex].content = 
          `${systemMessage.content}\n\n${messages[firstUserIndex].content}`;
      }
    }

    return {
      id: nanoid(),
      provider: modelConfig.provider,
      model: modelConfig.internalModel,
      messages,
      options: {
        temperature: request.temperature,
        maxTokens: request.max_tokens,
        stream: request.stream || false,
        mode: modelConfig.mode,
        style: modelConfig.style,
        searchFocus: modelConfig.searchFocus,
        searchEnabled: modelConfig.searchFocus === 'internet'
      }
    };
  }

  /**
   * Convert internal response to OpenAI format
   */
  public static internalToOpenAI(
    response: ChatResponse,
    request: ChatCompletionRequest,
    streamMode: boolean = false
  ): ChatCompletionResponse | ChatCompletionChunk {
    const timestamp = Math.floor(Date.now() / 1000);
    const responseId = `chatcmpl-${nanoid(29)}`;

    if (streamMode) {
      // Return streaming chunk
      return {
        id: responseId,
        object: 'chat.completion.chunk',
        created: timestamp,
        model: request.model,
        choices: [{
          index: 0,
          delta: {
            content: response.content || '',
            role: response.done ? undefined : 'assistant'
          },
          finish_reason: response.done ? 'stop' : null,
          logprobs: null
        }]
      } as ChatCompletionChunk;
    }

    // Return complete response
    return {
      id: responseId,
      object: 'chat.completion',
      created: timestamp,
      model: request.model,
      choices: [{
        index: 0,
        message: {
          role: 'assistant',
          content: response.content || ''
        },
        finish_reason: 'stop',
        logprobs: null
      }],
      usage: {
        prompt_tokens: 0, // Would need to implement token counting
        completion_tokens: 0,
        total_tokens: 0
      }
    };
  }

  /**
   * Format error as OpenAI error response
   */
  public static formatError(error: any, statusCode: number = 500): ErrorResponse {
    let errorType = 'internal_error';
    let errorCode = 'internal_error';
    let message = 'An internal error occurred';

    if (error instanceof Error) {
      message = error.message;
    } else if (typeof error === 'string') {
      message = error;
    }

    // Map common errors to OpenAI error types
    if (statusCode === 401) {
      errorType = 'authentication_error';
      errorCode = 'invalid_api_key';
    } else if (statusCode === 429) {
      errorType = 'rate_limit_error';
      errorCode = 'rate_limit_exceeded';
    } else if (statusCode === 400) {
      errorType = 'invalid_request_error';
      errorCode = 'invalid_request';
    } else if (statusCode === 404) {
      errorType = 'invalid_request_error';
      errorCode = 'model_not_found';
    }

    return {
      error: {
        message,
        type: errorType,
        param: null,
        code: errorCode
      }
    };
  }

  /**
   * Convert streaming response to SSE format
   */
  public static toSSE(data: any): string {
    if (data === '[DONE]') {
      return 'data: [DONE]\n\n';
    }
    return `data: ${JSON.stringify(data)}\n\n`;
  }

  /**
   * Extract images from OpenAI messages if they contain image URLs or base64
   */
  public static extractImages(messages: OpenAIMessage[]): Map<number, string[]> {
    const imageMap = new Map<number, string[]>();
    
    messages.forEach((message, index) => {
      if (typeof message.content === 'object' && message.content !== null) {
        // Handle structured content with images
        // This would need to be implemented based on OpenAI's image format
        // For now, we'll skip this
      }
    });

    return imageMap;
  }

  /**
   * Validate request against model capabilities
   */
  public static validateRequest(request: ChatCompletionRequest): { valid: boolean; error?: string } {
    const modelConfig = MODEL_REGISTRY[request.model];
    
    if (!modelConfig) {
      return { valid: false, error: `Model ${request.model} not found` };
    }

    const capabilities = modelConfig.capabilities;
    if (!capabilities) {
      return { valid: true };
    }

    // Check for functions/tools support
    if ((request.functions || request.tools) && !capabilities.supportsFunctions) {
      return { 
        valid: false, 
        error: `Model ${request.model} does not support functions/tools` 
      };
    }

    // Check for system message support
    const hasSystemMessage = request.messages.some(m => m.role === 'system');
    if (hasSystemMessage && !capabilities.supportsSystemMessage) {
      // This is okay, we'll handle it in conversion
      // Just log a warning
      console.warn(`Model ${request.model} does not support system messages, will be combined with user message`);
    }

    return { valid: true };
  }
}
