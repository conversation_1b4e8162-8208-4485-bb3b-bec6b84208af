import { Router, Request, Response } from 'express';
import { z } from 'zod';
import winston from 'winston';
import { WebSocketManager } from '../websocket';
import { ApiKeyManager } from '../services/apiKeyManager';
import { FormatConverter } from '../services/converter';
import { ChatCompletionRequest } from '../types/openai';

// Validation schema for chat completion request
const chatCompletionSchema = z.object({
  model: z.string(),
  messages: z.array(z.object({
    role: z.enum(['system', 'user', 'assistant', 'function', 'tool']),
    content: z.string().nullable(),
    name: z.string().optional(),
    function_call: z.object({
      name: z.string(),
      arguments: z.string()
    }).optional(),
    tool_calls: z.array(z.any()).optional()
  })),
  temperature: z.number().min(0).max(2).optional(),
  top_p: z.number().min(0).max(1).optional(),
  n: z.number().int().min(1).optional(),
  stream: z.boolean().optional(),
  stop: z.union([z.string(), z.array(z.string())]).optional(),
  max_tokens: z.number().int().positive().optional(),
  presence_penalty: z.number().min(-2).max(2).optional(),
  frequency_penalty: z.number().min(-2).max(2).optional(),
  logit_bias: z.record(z.number()).optional(),
  user: z.string().optional(),
  functions: z.array(z.any()).optional(),
  function_call: z.any().optional(),
  tools: z.array(z.any()).optional(),
  tool_choice: z.any().optional()
});

export function createChatRouter(
  wsManager: WebSocketManager,
  apiKeyManager: ApiKeyManager,
  logger: winston.Logger
): Router {
  const router = Router();

  router.post('/completions', async (req: Request, res: Response) => {
    try {
      // Validate request body
      const validationResult = chatCompletionSchema.safeParse(req.body);
      if (!validationResult.success) {
        return res.status(400).json(
          FormatConverter.formatError(
            `Invalid request: ${validationResult.error.errors[0].message}`,
            400
          )
        );
      }

      const request = validationResult.data as ChatCompletionRequest;

      // Validate model and capabilities
      const validation = FormatConverter.validateRequest(request);
      if (!validation.valid) {
        return res.status(400).json(
          FormatConverter.formatError(validation.error, 400)
        );
      }

      // Check WebSocket connection
      if (!wsManager.isConnected()) {
        return res.status(503).json(
          FormatConverter.formatError(
            'Extension not connected. Please ensure the WebAI extension is running.',
            503
          )
        );
      }

      // Convert to internal format
      const internalRequest = FormatConverter.openAIToInternal(request);

      if (request.stream) {
        // Handle streaming response
        res.writeHead(200, {
          'Content-Type': 'text/event-stream',
          'Cache-Control': 'no-cache',
          'Connection': 'keep-alive',
          'X-Accel-Buffering': 'no' // Disable Nginx buffering
        });

        let fullResponse = '';
        
        wsManager.streamChatRequest(internalRequest, (response) => {
          if (response.error) {
            const errorChunk = FormatConverter.formatError(response.error, 500);
            res.write(FormatConverter.toSSE(errorChunk));
            res.write(FormatConverter.toSSE('[DONE]'));
            res.end();
            return;
          }

          if (response.content) {
            // Send incremental content
            const deltaContent = response.content.substring(fullResponse.length);
            fullResponse = response.content;

            const chunk = FormatConverter.internalToOpenAI(
              { ...response, content: deltaContent },
              request,
              true
            );
            res.write(FormatConverter.toSSE(chunk));
          }

          if (response.done) {
            res.write(FormatConverter.toSSE('[DONE]'));
            res.end();
          }
        });

        // Handle client disconnect
        req.on('close', () => {
          logger.debug('Client disconnected from stream');
        });

      } else {
        // Handle non-streaming response
        try {
          const response = await wsManager.sendChatRequest(internalRequest);
          
          if (response.error) {
            return res.status(500).json(
              FormatConverter.formatError(response.error, 500)
            );
          }

          const openAIResponse = FormatConverter.internalToOpenAI(
            response,
            request,
            false
          );

          res.json(openAIResponse);
        } catch (error) {
          logger.error('Error processing chat request:', error);
          res.status(500).json(
            FormatConverter.formatError(error, 500)
          );
        }
      }

    } catch (error) {
      logger.error('Unexpected error in chat completion:', error);
      res.status(500).json(
        FormatConverter.formatError(error, 500)
      );
    }
  });

  return router;
}
