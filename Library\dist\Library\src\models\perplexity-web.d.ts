import { AbstractModel } from './abstract-model';
import { StatusEvent } from './types';
interface PerplexityWebModelConfig {
    sessionKey?: string;
}
export declare class PerplexityWebModel extends AbstractModel {
    private csrfToken?;
    private userInfo?;
    private userSettings?;
    private visitorId;
    private sessionKey?;
    constructor(config?: PerplexityWebModelConfig);
    private initializeStorage;
    private validateExistingThreads;
    private isValidPerplexityMetadata;
    getName(): string;
    getModels(): Record<string, Record<string, string[]>>;
    getSearchSources(): string[];
    supportsImageInput(): boolean;
    /**
     * Uploads an image to Perplexity's backend (Cloudinary)
     * @param imageFile The image file to upload
     * @returns The secure URL of the uploaded image
     * @serverOperation
     */
    private uploadImage;
    private getHeaders;
    /**
     * Check if user is authenticated with Perplexity
     * @returns User info if authenticated, null otherwise
     */
    private checkAuth;
    /**
     * Check rate limits for Perplexity
     * @returns Remaining queries count
     */
    private checkRateLimit;
    /**
     * Get recent threads from Perplexity
     */
    private getRecentThreads;
    private ensureThreadLoaded;
    private getPerplexityMetadata;
    private getCurrentThreadSafe;
    initNewThread(): Promise<void>;
    loadThread(threadId: string): Promise<void>;
    protected doSendMessage(params: {
        prompt: string;
        images?: File[];
        model?: string;
        signal?: AbortSignal;
        searchFocus?: string;
        searchSources?: string[];
        onEvent: (event: StatusEvent) => void;
    }): Promise<void>;
    getModelVersion(): Promise<string>;
    /**
     * Deletes a conversation from Perplexity's servers
     * @param threadId The thread ID to delete
     * @returns Response data from the server
     * @serverOperation This method makes direct API calls to Perplexity's servers
     */
    deleteServerThreads(threadIds: string[], updateLocalThread?: boolean, createNewThreadAfterDelete?: boolean): Promise<void>;
    /**
     * Shares a conversation from Perplexity
     * @returns A shareable URL
     * @serverOperation This method makes direct API calls to Perplexity's servers
     */
    shareConversation(): Promise<string>;
    /**
     * Sets a conversation to private in Perplexity
     * @returns True if successful
     * @serverOperation This method makes direct API calls to Perplexity's servers
     */
    unShareConversation(): Promise<boolean>;
    /**
     * Updates the title of a conversation
     * @param newTitle The new title to set for the conversation
     * @serverOperation This method makes direct API calls to Perplexity's servers
     */
    editTitle(newTitle: string): Promise<void>;
}
export {};
