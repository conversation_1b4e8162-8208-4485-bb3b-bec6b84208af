import { AbstractModel } from './abstract-model';
import { ChatMessage, StatusEvent } from './types';
interface GeminiThreadMetadata {
    conversationId: string;
    contextIds: [string, string, string];
    requestParams: {
        atValue: string;
        blValue: string;
        sid: string;
    };
    emoji: string;
    defaultLang: string;
    defaultModel: string;
    shareUrl: string;
}
export declare class GeminiWebModel extends AbstractModel {
    constructor();
    private initializeStorage;
    private validateExistingThreads;
    private isValidGeminiMetadata;
    private getGeminiMetadata;
    getName(): string;
    supportsImageInput(): boolean;
    initNewThread(): Promise<void>;
    private fetchRequestParams;
    private parseBardResponse;
    private uploadImage;
    private ensureThreadLoaded;
    protected doSendMessage(params: {
        prompt: string;
        images?: File[];
        signal?: AbortSignal;
        model?: string;
        onEvent: (event: StatusEvent) => void;
    }): Promise<void>;
    loadThread(threadId: string): Promise<void>;
    private getBardMetadata;
    private getCurrentThreadSafe;
    saveThread(): Promise<void>;
    /**
     * Updates the title and optionally the emoji of a conversation on Gemini's servers
     * @param newTitle The new title to set for the conversation
     * @param emoji Optional emoji character to set
     * @param options Optional configuration options
     * @serverOperation This method makes direct API calls to Gemini's servers
     */
    editTitle(newTitle: string, emoji?: string, options?: {
        loadThread?: boolean;
        metadata?: GeminiThreadMetadata;
        tryUpdateThread?: boolean;
    }): Promise<void>;
    /**
       * Deletes one or more conversations from Gemini's servers.
       * Note: This operation involves two separate API calls per conversation.
       * @param threadIds Array of thread IDs (which correspond to conversation IDs in metadata) to delete.
       * @param updateLocalThread If true, also delete the thread from local storage upon successful server deletion.
       * @param createNewThreadAfterDelete If true and the currently active thread is deleted locally, initialize a new thread.
       * @serverOperation This method makes direct API calls to Gemini's servers.
       */
    deleteServerThreads(threadIds: string[], updateLocalThread?: boolean, createNewThreadAfterDelete?: boolean): Promise<void>;
    /**
     * Gets conversation data (messages) from Gemini's servers
     * @param options Configuration options, requires metadata or loading the current thread
     * @returns The parsed conversation messages in chronological order
     * @serverOperation This method makes direct API calls to Gemini's servers
     */
    getConversationData(options?: {
        loadThread?: boolean;
        metadata?: GeminiThreadMetadata;
    }): Promise<ChatMessage[]>;
    /**
       * Shares a conversation and gets a shareable URL from Gemini's servers
       * @param options Configuration options for sharing
       * @returns A promise that resolves to a shareable URL
       * @serverOperation This method makes direct API calls to Gemini's servers
       */
    shareConversation(options?: {
        loadThread?: boolean;
        title?: string;
        modelName?: string;
        language?: string;
        metadata?: GeminiThreadMetadata;
    }): Promise<string>;
    /**
       * un-shares a conversation from Gemini's servers
       * @param options Configuration options for un-sharing
       * @returns A promise that resolves to a shareable URL
       * @serverOperation This method makes direct API calls to Gemini's servers
       */
    unShareConversation(options?: {
        loadThread?: boolean;
        updateThread?: boolean;
        metadata?: GeminiThreadMetadata;
    }): Promise<boolean>;
}
export {};
