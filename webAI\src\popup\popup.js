/**
 * Popup JavaScript for WebAI Extension
 */

import browser from 'webextension-polyfill';

class PopupManager {
    constructor() {
        this.isLoading = false;
        this.refreshInterval = null;

        this.initializeElements();
        this.attachEventListeners();
        this.loadData();
        this.startAutoRefresh();
    }

    /**
     * Initialize DOM elements
     */
    initializeElements() {
        // Status elements
        this.statusIndicator = document.getElementById('statusIndicator');
        this.statusDot = this.statusIndicator.querySelector('.status-dot');
        this.statusText = this.statusIndicator.querySelector('.status-text');
        this.serverUrl = document.getElementById('serverUrl');
        this.wsStatus = document.getElementById('wsStatus');

        // Action buttons
        this.testConnectionBtn = document.getElementById('testConnectionBtn');
        this.refreshModelsBtn = document.getElementById('refreshModelsBtn');
        this.settingsBtn = document.getElementById('settingsBtn');
        this.helpBtn = document.getElementById('helpBtn');

        // Content areas
        this.modelsSummary = document.getElementById('modelsSummary');
        this.messagesCount = document.getElementById('messagesCount');
        this.modelsCount = document.getElementById('modelsCount');
        this.errorsCount = document.getElementById('errorsCount');
        this.activityList = document.getElementById('activityList');

        // Overlay and toast
        this.loadingOverlay = document.getElementById('loadingOverlay');
        this.toast = document.getElementById('toast');

        // Version
        this.versionText = document.getElementById('versionText');
    }

    /**
     * Attach event listeners
     */
    attachEventListeners() {
        this.testConnectionBtn.addEventListener('click', () => this.testConnection());
        this.refreshModelsBtn.addEventListener('click', () => this.refreshModels());
        this.settingsBtn.addEventListener('click', () => this.openSettings());
        this.helpBtn.addEventListener('click', () => this.openHelp());

        // Handle popup close
        window.addEventListener('beforeunload', () => {
            if (this.refreshInterval) {
                clearInterval(this.refreshInterval);
            }
        });
    }

    /**
     * Load all data
     */
    async loadData() {
        try {
            this.setLoading(true);

            await Promise.all([
                this.loadStatus(),
                this.loadModels(),
                this.loadStatistics(),
                this.loadActivity()
            ]);

        } catch (error) {
            console.error('Error loading popup data:', error);
            this.showToast('Error loading data', 'error');
        } finally {
            this.setLoading(false);
        }
    }

    /**
     * Load extension status
     */
    async loadStatus() {
        try {
            const response = await this.sendMessage({ type: 'GET_STATUS' });

            if (response.success) {
                const status = response.data;

                // Update connection status
                this.updateConnectionStatus(status);

                // Update server info
                this.serverUrl.textContent = status.config.serverUrl || 'Not configured';
                this.wsStatus.textContent = status.websocket.connected ? 'Connected' : 'Disconnected';

            } else {
                throw new Error(response.error || 'Failed to get status');
            }
        } catch (error) {
            console.error('Error loading status:', error);
            this.updateConnectionStatus({ initialized: false, websocket: { connected: false } });
        }
    }

    /**
     * Load available models
     */
    async loadModels() {
        try {
            const response = await this.sendMessage({ type: 'GET_MODELS' });

            if (response.success) {
                const models = response.data || [];
                this.renderModels(models);
                this.modelsCount.textContent = models.length;
            } else {
                throw new Error(response.error || 'Failed to load models');
            }
        } catch (error) {
            console.error('Error loading models:', error);
            this.modelsSummary.innerHTML = '<div class="loading">Error loading models</div>';
            this.modelsCount.textContent = '0';
        }
    }

    /**
     * Load statistics
     */
    async loadStatistics() {
        try {
            // This would typically come from storage or background script
            // For now, we'll show placeholder data
            this.messagesCount.textContent = '0';
            this.errorsCount.textContent = '0';
        } catch (error) {
            console.error('Error loading statistics:', error);
        }
    }

    /**
     * Load recent activity
     */
    async loadActivity() {
        try {
            // This would typically come from storage or background script
            // For now, we'll show placeholder data
            this.activityList.innerHTML = `
                <div class="activity-item">
                    <span class="activity-text">No recent activity</span>
                    <span class="activity-time"></span>
                </div>
            `;
        } catch (error) {
            console.error('Error loading activity:', error);
        }
    }

    /**
     * Render models list
     */
    renderModels(models) {
        if (models.length === 0) {
            this.modelsSummary.innerHTML = '<div class="loading">No models available</div>';
            return;
        }

        // Show first few models with status
        const displayModels = models.slice(0, 4); // Show max 4 models in popup
        const modelsHtml = displayModels.map(model => `
            <div class="model-item">
                <span class="model-name">${model.name || model.id}</span>
                <span class="model-status unknown">Unknown</span>
            </div>
        `).join('');

        const moreText = models.length > 4 ? `<div class="model-item"><span class="model-name">+${models.length - 4} more models</span></div>` : '';

        this.modelsSummary.innerHTML = modelsHtml + moreText;
    }

    /**
     * Update connection status display
     */
    updateConnectionStatus(status) {
        const isConnected = status.initialized && status.websocket.connected;
        const hasError = !status.initialized;

        if (isConnected) {
            this.statusDot.className = 'status-dot connected';
            this.statusText.textContent = 'Connected';
        } else if (hasError) {
            this.statusDot.className = 'status-dot error';
            this.statusText.textContent = 'Error';
        } else {
            this.statusDot.className = 'status-dot disconnected';
            this.statusText.textContent = 'Disconnected';
        }
    }

    /**
     * Test connection
     */
    async testConnection() {
        try {
            this.testConnectionBtn.disabled = true;
            this.testConnectionBtn.innerHTML = '<span class="btn-icon">⏳</span>Testing...';

            const response = await this.sendMessage({ type: 'RECONNECT_WEBSOCKET' });

            if (response.success) {
                this.showToast('Connection successful', 'success');
                await this.loadStatus();
            } else {
                throw new Error(response.error || 'Connection test failed');
            }
        } catch (error) {
            console.error('Connection test failed:', error);
            this.showToast('Connection failed', 'error');
        } finally {
            this.testConnectionBtn.disabled = false;
            this.testConnectionBtn.innerHTML = '<span class="btn-icon">🔗</span>Test Connection';
        }
    }

    /**
     * Refresh models
     */
    async refreshModels() {
        try {
            this.refreshModelsBtn.disabled = true;
            this.refreshModelsBtn.innerHTML = '<span class="btn-icon">⏳</span>Refreshing...';
            this.modelsSummary.innerHTML = '<div class="loading">Refreshing models...</div>';

            await this.loadModels();
            this.showToast('Models refreshed', 'success');
        } catch (error) {
            console.error('Error refreshing models:', error);
            this.showToast('Error refreshing models', 'error');
        } finally {
            this.refreshModelsBtn.disabled = false;
            this.refreshModelsBtn.innerHTML = '<span class="btn-icon">🔄</span>Refresh Models';
        }
    }

    /**
     * Open settings page
     */
    async openSettings() {
        try {
            await browser.runtime.openOptionsPage();
            window.close();
        } catch (error) {
            console.error('Error opening settings:', error);
            this.showToast('Error opening settings', 'error');
        }
    }

    /**
     * Open help/documentation
     */
    async openHelp() {
        try {
            await browser.tabs.create({
                url: 'https://github.com/your-repo/webai-extension#readme'
            });
            window.close();
        } catch (error) {
            console.error('Error opening help:', error);
            this.showToast('Error opening help', 'error');
        }
    }

    /**
     * Start auto-refresh
     */
    startAutoRefresh() {
        // Refresh status every 10 seconds
        this.refreshInterval = setInterval(() => {
            if (!this.isLoading) {
                this.loadStatus();
            }
        }, 10000);
    }

    /**
     * Send message to background script
     */
    async sendMessage(message) {
        try {
            return await browser.runtime.sendMessage(message);
        } catch (error) {
            console.error('Error sending message to background script:', error);
            return { success: false, error: error.message };
        }
    }

    /**
     * Set loading state
     */
    setLoading(loading) {
        this.isLoading = loading;

        if (loading) {
            this.loadingOverlay.classList.add('show');
        } else {
            this.loadingOverlay.classList.remove('show');
        }

        // Disable buttons during loading
        this.testConnectionBtn.disabled = loading;
        this.refreshModelsBtn.disabled = loading;
    }

    /**
     * Show toast notification
     */
    showToast(message, type = 'info') {
        this.toast.textContent = message;
        this.toast.className = `toast ${type}`;
        this.toast.classList.add('show');

        setTimeout(() => {
            this.toast.classList.remove('show');
        }, 3000);
    }
}

// Initialize popup when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new PopupManager();
});
