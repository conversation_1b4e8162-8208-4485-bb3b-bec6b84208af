import { nanoid } from 'nanoid';
import * as fs from 'fs/promises';
import * as path from 'path';
import winston from 'winston';

interface ApiKey {
  key: string;
  name: string;
  created: number;
  lastUsed: number;
  usageCount: number;
  rateLimit?: {
    requests: number;
    window: number; // in milliseconds
  };
  metadata?: Record<string, any>;
}

export class ApiKeyManager {
  private keys: Map<string, ApiKey> = new Map();
  private rateLimitTracking: Map<string, number[]> = new Map();
  private storageFile: string;
  private logger: winston.Logger;

  constructor(storageFile: string, logger: winston.Logger) {
    this.storageFile = storageFile;
    this.logger = logger;
    this.loadKeys().catch(err => logger.error('Failed to load API keys:', err));
  }

  private async loadKeys(): Promise<void> {
    try {
      const data = await fs.readFile(this.storageFile, 'utf-8');
      const keys = JSON.parse(data) as ApiKey[];
      keys.forEach(key => this.keys.set(key.key, key));
      this.logger.info(`Loaded ${keys.length} API keys`);
    } catch (error: any) {
      if (error.code === 'ENOENT') {
        // File doesn't exist, create it
        await this.saveKeys();
        this.logger.info('Created new API keys file');
      } else {
        throw error;
      }
    }
  }

  private async saveKeys(): Promise<void> {
    const keys = Array.from(this.keys.values());
    const dir = path.dirname(this.storageFile);
    await fs.mkdir(dir, { recursive: true });
    await fs.writeFile(this.storageFile, JSON.stringify(keys, null, 2));
  }

  public async generateKey(name: string, rateLimit?: { requests: number; window: number }): Promise<string> {
    const key = `sk-webai-${nanoid(32)}`;
    const apiKey: ApiKey = {
      key,
      name,
      created: Date.now(),
      lastUsed: 0,
      usageCount: 0,
      rateLimit
    };

    this.keys.set(key, apiKey);
    await this.saveKeys();
    this.logger.info(`Generated new API key: ${name}`);
    
    return key;
  }

  public async validateKey(key: string): Promise<boolean> {
    const apiKey = this.keys.get(key);
    if (!apiKey) {
      return false;
    }

    // Check rate limit if configured
    if (apiKey.rateLimit) {
      const now = Date.now();
      const tracking = this.rateLimitTracking.get(key) || [];
      
      // Remove old entries outside the window
      const validEntries = tracking.filter(
        timestamp => now - timestamp < apiKey.rateLimit!.window
      );

      if (validEntries.length >= apiKey.rateLimit.requests) {
        this.logger.warn(`Rate limit exceeded for key: ${apiKey.name}`);
        return false;
      }

      // Add current request
      validEntries.push(now);
      this.rateLimitTracking.set(key, validEntries);
    }

    // Update usage stats
    apiKey.lastUsed = Date.now();
    apiKey.usageCount++;
    
    // Save periodically (every 10 requests)
    if (apiKey.usageCount % 10 === 0) {
      await this.saveKeys();
    }

    return true;
  }

  public async revokeKey(key: string): Promise<boolean> {
    if (this.keys.delete(key)) {
      await this.saveKeys();
      this.rateLimitTracking.delete(key);
      this.logger.info(`Revoked API key: ${key}`);
      return true;
    }
    return false;
  }

  public async listKeys(): Promise<Array<Omit<ApiKey, 'key'> & { key: string }>> {
    return Array.from(this.keys.values()).map(key => ({
      ...key,
      key: `${key.key.substring(0, 12)}...${key.key.substring(key.key.length - 4)}`
    }));
  }

  public getKeyInfo(key: string): ApiKey | undefined {
    return this.keys.get(key);
  }

  public async updateKeyMetadata(key: string, metadata: Record<string, any>): Promise<boolean> {
    const apiKey = this.keys.get(key);
    if (apiKey) {
      apiKey.metadata = { ...apiKey.metadata, ...metadata };
      await this.saveKeys();
      return true;
    }
    return false;
  }

  // Create a default API key if none exist
  public async ensureDefaultKey(): Promise<void> {
    if (this.keys.size === 0) {
      const defaultKey = await this.generateKey('Default Key', {
        requests: 100,
        window: 60000 // 100 requests per minute
      });
      this.logger.info(`Created default API key: ${defaultKey}`);
      console.log(`\n🔑 Default API Key: ${defaultKey}\n`);
    }
  }
}
