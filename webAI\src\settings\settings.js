/**
 * Settings Page JavaScript for WebAI Extension
 */

import browser from 'webextension-polyfill';

class SettingsManager {
    constructor() {
        this.isLoading = false;
        this.currentConfig = {};
        this.models = [];
        this.authTokens = {};
        this.stats = {};

        this.initializeElements();
        this.attachEventListeners();
        this.loadSettings();
    }

    /**
     * Initialize DOM elements
     */
    initializeElements() {
        // Form elements
        this.serverUrlInput = document.getElementById('serverUrl');
        this.wsUrlInput = document.getElementById('wsUrl');
        this.apiKeyInput = document.getElementById('apiKey');
        this.autoConnectCheckbox = document.getElementById('autoConnect');
        this.enableLoggingCheckbox = document.getElementById('enableLogging');
        this.maxConversationsInput = document.getElementById('maxConversations');

        // Buttons
        this.generateApiKeyBtn = document.getElementById('generateApiKey');
        this.toggleApiKeyBtn = document.getElementById('toggleApiKey');
        this.refreshModelsBtn = document.getElementById('refreshModels');
        this.testConnectionBtn = document.getElementById('testConnection');
        this.clearAllTokensBtn = document.getElementById('clearAllTokens');
        this.exportConfigBtn = document.getElementById('exportConfig');
        this.importConfigBtn = document.getElementById('importConfig');
        this.importFileInput = document.getElementById('importFile');
        this.clearAllDataBtn = document.getElementById('clearAllData');
        this.saveSettingsBtn = document.getElementById('saveSettings');
        this.resetSettingsBtn = document.getElementById('resetSettings');

        // Display elements
        this.modelsListDiv = document.getElementById('modelsList');
        this.authTokensListDiv = document.getElementById('authTokensList');
        this.statsDisplayDiv = document.getElementById('statsDisplay');
        this.connectionStatusSpan = document.getElementById('connectionStatus');
        this.lastSavedSpan = document.getElementById('lastSaved');

        // Modal elements
        this.confirmModal = document.getElementById('confirmModal');
        this.modalTitle = document.getElementById('modalTitle');
        this.modalMessage = document.getElementById('modalMessage');
        this.modalConfirm = document.getElementById('modalConfirm');
        this.modalCancel = document.getElementById('modalCancel');

        // Toast
        this.toast = document.getElementById('toast');
    }

    /**
     * Attach event listeners
     */
    attachEventListeners() {
        // Form inputs
        this.serverUrlInput.addEventListener('input', () => this.markUnsaved());
        this.wsUrlInput.addEventListener('input', () => this.markUnsaved());
        this.apiKeyInput.addEventListener('input', () => this.markUnsaved());
        this.autoConnectCheckbox.addEventListener('change', () => this.markUnsaved());
        this.enableLoggingCheckbox.addEventListener('change', () => this.markUnsaved());
        this.maxConversationsInput.addEventListener('input', () => this.markUnsaved());

        // Buttons
        this.generateApiKeyBtn.addEventListener('click', () => this.generateApiKey());
        this.toggleApiKeyBtn.addEventListener('click', () => this.toggleApiKeyVisibility());
        this.refreshModelsBtn.addEventListener('click', () => this.refreshModels());
        this.testConnectionBtn.addEventListener('click', () => this.testConnection());
        this.clearAllTokensBtn.addEventListener('click', () => this.clearAllTokens());
        this.exportConfigBtn.addEventListener('click', () => this.exportConfig());
        this.importConfigBtn.addEventListener('click', () => this.importFileInput.click());
        this.importFileInput.addEventListener('change', (e) => this.importConfig(e));
        this.clearAllDataBtn.addEventListener('click', () => this.clearAllData());
        this.saveSettingsBtn.addEventListener('click', () => this.saveSettings());
        this.resetSettingsBtn.addEventListener('click', () => this.resetSettings());

        // Modal
        this.modalCancel.addEventListener('click', () => this.hideModal());
        this.confirmModal.addEventListener('click', (e) => {
            if (e.target === this.confirmModal) this.hideModal();
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey && e.key === 's') {
                e.preventDefault();
                this.saveSettings();
            }
        });
    }

    /**
     * Load settings from storage and background script
     */
    async loadSettings() {
        try {
            this.setLoading(true);

            // Get status from background script
            const response = await this.sendMessage({ type: 'GET_STATUS' });

            if (response.success) {
                this.currentConfig = response.data.config || {};
                this.updateFormFromConfig();
                this.updateConnectionStatus(response.data.websocket.connected);
            }

            // Load models
            await this.loadModels();

            // Load auth tokens
            await this.loadAuthTokens();

            // Load statistics
            await this.loadStatistics();

        } catch (error) {
            console.error('Error loading settings:', error);
            this.showToast('Error loading settings', 'error');
        } finally {
            this.setLoading(false);
        }
    }

    /**
     * Update form fields from configuration
     */
    updateFormFromConfig() {
        this.serverUrlInput.value = this.currentConfig.serverUrl || 'http://localhost:3000';
        this.wsUrlInput.value = this.currentConfig.wsUrl || 'ws://localhost:3001';
        this.apiKeyInput.value = this.currentConfig.apiKey || '';
        this.autoConnectCheckbox.checked = this.currentConfig.autoConnect !== false;
        this.enableLoggingCheckbox.checked = this.currentConfig.enableLogging !== false;
        this.maxConversationsInput.value = this.currentConfig.maxConversations || 10;
    }

    /**
     * Load available models
     */
    async loadModels() {
        try {
            const response = await this.sendMessage({ type: 'GET_MODELS' });

            if (response.success) {
                this.models = response.data || [];
                this.renderModels();
            } else {
                throw new Error(response.error || 'Failed to load models');
            }
        } catch (error) {
            console.error('Error loading models:', error);
            this.modelsListDiv.innerHTML = `
                <div class="error">
                    <p>Error loading models: ${error.message}</p>
                    <button class="btn btn-secondary" onclick="location.reload()">Retry</button>
                </div>
            `;
        }
    }

    /**
     * Render models list
     */
    renderModels() {
        if (this.models.length === 0) {
            this.modelsListDiv.innerHTML = '<div class="loading">No models available</div>';
            return;
        }

        const modelsHtml = this.models.map(model => `
            <div class="model-item">
                <div class="model-info">
                    <div class="model-name">${model.name || model.id}</div>
                    <div class="model-provider">Provider: ${model.provider}</div>
                    ${model.description ? `<div class="model-description">${model.description}</div>` : ''}
                </div>
                <div class="model-actions">
                    <span class="model-status status-unknown">Unknown</span>
                    <button class="btn btn-secondary btn-sm" onclick="settingsManager.testModel('${model.provider}', '${model.id}')">
                        Test
                    </button>
                </div>
            </div>
        `).join('');

        this.modelsListDiv.innerHTML = modelsHtml;
    }

    /**
     * Load authentication tokens
     */
    async loadAuthTokens() {
        try {
            // This would typically come from storage
            // For now, we'll show a placeholder
            this.authTokensListDiv.innerHTML = `
                <div class="auth-token-item">
                    <div class="token-info">
                        <div class="token-service">No authentication tokens stored</div>
                        <div class="token-updated">Tokens will appear here when retrieved</div>
                    </div>
                </div>
            `;
        } catch (error) {
            console.error('Error loading auth tokens:', error);
        }
    }

    /**
     * Load statistics
     */
    async loadStatistics() {
        try {
            // This would typically come from storage
            // For now, we'll show placeholder stats
            this.statsDisplayDiv.innerHTML = `
                <div class="stat-item">
                    <div class="stat-value">0</div>
                    <div class="stat-label">Messages Processed</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">0</div>
                    <div class="stat-label">Models Used</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">0</div>
                    <div class="stat-label">Errors</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">Never</div>
                    <div class="stat-label">Last Used</div>
                </div>
            `;
        } catch (error) {
            console.error('Error loading statistics:', error);
        }
    }

    /**
     * Generate a new API key
     */
    async generateApiKey() {
        try {
            const response = await this.sendMessage({ type: 'GENERATE_API_KEY' });

            if (response.success) {
                this.apiKeyInput.value = response.data;
                this.markUnsaved();
                this.showToast('API key generated successfully', 'success');
            } else {
                throw new Error(response.error || 'Failed to generate API key');
            }
        } catch (error) {
            console.error('Error generating API key:', error);
            this.showToast('Error generating API key', 'error');
        }
    }

    /**
     * Toggle API key visibility
     */
    toggleApiKeyVisibility() {
        const isPassword = this.apiKeyInput.type === 'password';
        this.apiKeyInput.type = isPassword ? 'text' : 'password';
        this.toggleApiKeyBtn.textContent = isPassword ? '🙈' : '👁️';
    }

    /**
     * Refresh models list
     */
    async refreshModels() {
        this.modelsListDiv.innerHTML = '<div class="loading">Refreshing models...</div>';
        await this.loadModels();
        this.showToast('Models refreshed', 'success');
    }

    /**
     * Test connection to server
     */
    async testConnection() {
        try {
            this.testConnectionBtn.disabled = true;
            this.testConnectionBtn.textContent = 'Testing...';

            const response = await this.sendMessage({ type: 'RECONNECT_WEBSOCKET' });

            if (response.success) {
                this.showToast('Connection test successful', 'success');
                this.updateConnectionStatus(true);
            } else {
                throw new Error(response.error || 'Connection test failed');
            }
        } catch (error) {
            console.error('Connection test failed:', error);
            this.showToast('Connection test failed', 'error');
            this.updateConnectionStatus(false);
        } finally {
            this.testConnectionBtn.disabled = false;
            this.testConnectionBtn.textContent = 'Test Connection';
        }
    }

    /**
     * Test a specific model
     */
    async testModel(provider, modelId) {
        try {
            const response = await this.sendMessage({
                type: 'TEST_MODEL',
                payload: { provider, model: modelId }
            });

            if (response.success) {
                this.showToast(`Model ${modelId} test successful`, 'success');
            } else {
                throw new Error(response.error || 'Model test failed');
            }
        } catch (error) {
            console.error('Model test failed:', error);
            this.showToast(`Model ${modelId} test failed`, 'error');
        }
    }

    /**
     * Clear all authentication tokens
     */
    async clearAllTokens() {
        this.showConfirmModal(
            'Clear All Tokens',
            'Are you sure you want to clear all authentication tokens? This action cannot be undone.',
            async () => {
                try {
                    // Implementation would clear tokens from storage
                    this.showToast('All tokens cleared', 'success');
                    await this.loadAuthTokens();
                } catch (error) {
                    console.error('Error clearing tokens:', error);
                    this.showToast('Error clearing tokens', 'error');
                }
            }
        );
    }

    /**
     * Export configuration
     */
    async exportConfig() {
        try {
            const config = this.getConfigFromForm();
            const exportData = {
                config,
                exportedAt: new Date().toISOString(),
                version: '1.0'
            };

            const blob = new Blob([JSON.stringify(exportData, null, 2)], {
                type: 'application/json'
            });

            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `webai-config-${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            this.showToast('Configuration exported', 'success');
        } catch (error) {
            console.error('Error exporting config:', error);
            this.showToast('Error exporting configuration', 'error');
        }
    }

    /**
     * Import configuration
     */
    async importConfig(event) {
        const file = event.target.files[0];
        if (!file) return;

        try {
            const text = await file.text();
            const data = JSON.parse(text);

            if (data.config) {
                this.currentConfig = { ...this.currentConfig, ...data.config };
                this.updateFormFromConfig();
                this.markUnsaved();
                this.showToast('Configuration imported', 'success');
            } else {
                throw new Error('Invalid configuration file');
            }
        } catch (error) {
            console.error('Error importing config:', error);
            this.showToast('Error importing configuration', 'error');
        }

        // Reset file input
        event.target.value = '';
    }

    /**
     * Clear all data
     */
    async clearAllData() {
        this.showConfirmModal(
            'Clear All Data',
            'Are you sure you want to clear all extension data? This will reset everything to defaults and cannot be undone.',
            async () => {
                try {
                    // Implementation would clear all storage
                    this.showToast('All data cleared', 'success');
                    setTimeout(() => location.reload(), 1000);
                } catch (error) {
                    console.error('Error clearing data:', error);
                    this.showToast('Error clearing data', 'error');
                }
            }
        );
    }

    /**
     * Save settings
     */
    async saveSettings() {
        try {
            this.setLoading(true);

            const config = this.getConfigFromForm();
            const response = await this.sendMessage({
                type: 'UPDATE_CONFIG',
                payload: config
            });

            if (response.success) {
                this.currentConfig = config;
                this.markSaved();
                this.showToast('Settings saved successfully', 'success');
            } else {
                throw new Error(response.error || 'Failed to save settings');
            }
        } catch (error) {
            console.error('Error saving settings:', error);
            this.showToast('Error saving settings', 'error');
        } finally {
            this.setLoading(false);
        }
    }

    /**
     * Reset settings to defaults
     */
    async resetSettings() {
        this.showConfirmModal(
            'Reset Settings',
            'Are you sure you want to reset all settings to their default values?',
            () => {
                this.currentConfig = {
                    serverUrl: 'http://localhost:3000',
                    wsUrl: 'ws://localhost:3001',
                    apiKey: '',
                    autoConnect: true,
                    enableLogging: true,
                    maxConversations: 10
                };
                this.updateFormFromConfig();
                this.markUnsaved();
                this.showToast('Settings reset to defaults', 'info');
            }
        );
    }

    /**
     * Get configuration from form
     */
    getConfigFromForm() {
        return {
            serverUrl: this.serverUrlInput.value.trim(),
            wsUrl: this.wsUrlInput.value.trim(),
            apiKey: this.apiKeyInput.value.trim(),
            autoConnect: this.autoConnectCheckbox.checked,
            enableLogging: this.enableLoggingCheckbox.checked,
            maxConversations: parseInt(this.maxConversationsInput.value) || 10
        };
    }

    /**
     * Send message to background script
     */
    async sendMessage(message) {
        try {
            return await browser.runtime.sendMessage(message);
        } catch (error) {
            console.error('Error sending message to background script:', error);
            return { success: false, error: error.message };
        }
    }

    /**
     * Update connection status display
     */
    updateConnectionStatus(connected) {
        this.connectionStatusSpan.textContent = connected ? 'Connected' : 'Disconnected';
        this.connectionStatusSpan.className = `status-indicator ${connected ? 'connected' : 'disconnected'}`;
    }

    /**
     * Mark settings as unsaved
     */
    markUnsaved() {
        this.saveSettingsBtn.textContent = 'Save Settings *';
        this.lastSavedSpan.textContent = 'Unsaved changes';
    }

    /**
     * Mark settings as saved
     */
    markSaved() {
        this.saveSettingsBtn.textContent = 'Save Settings';
        this.lastSavedSpan.textContent = `Last saved: ${new Date().toLocaleTimeString()}`;
    }

    /**
     * Set loading state
     */
    setLoading(loading) {
        this.isLoading = loading;
        this.saveSettingsBtn.disabled = loading;
        this.refreshModelsBtn.disabled = loading;
        this.testConnectionBtn.disabled = loading;
    }

    /**
     * Show confirmation modal
     */
    showConfirmModal(title, message, onConfirm) {
        this.modalTitle.textContent = title;
        this.modalMessage.textContent = message;
        this.confirmModal.classList.add('show');

        this.modalConfirm.onclick = () => {
            this.hideModal();
            onConfirm();
        };
    }

    /**
     * Hide confirmation modal
     */
    hideModal() {
        this.confirmModal.classList.remove('show');
        this.modalConfirm.onclick = null;
    }

    /**
     * Show toast notification
     */
    showToast(message, type = 'info') {
        this.toast.textContent = message;
        this.toast.className = `toast ${type}`;
        this.toast.classList.add('show');

        setTimeout(() => {
            this.toast.classList.remove('show');
        }, 3000);
    }
}

// Initialize settings manager when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.settingsManager = new SettingsManager();
});
