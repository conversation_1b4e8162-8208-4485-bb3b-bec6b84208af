/**
 * Utility functions for handling cookies and authentication
 */
/**
 * Solves the DeepSeek Proof-of-Work challenge using the loaded WASM module.
 *
 * @param challengeData The challenge string provided by the DeepSeek API.
 * @returns A promise that resolves with the solved PoW token string.
 * @throws If the WASM module is not loaded or the solving process fails.
 */
export declare function solvePowChallenge(challengeData: string): Promise<string>;
