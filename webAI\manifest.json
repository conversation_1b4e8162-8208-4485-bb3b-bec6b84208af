{"name": "WebAI - OpenAI Bridge", "description": "Access web AI services through OpenAI-compatible API", "version": "1.0.0", "manifest_version": 3, "icons": {"16": "icons/icon16.png", "32": "icons/icon32.png", "48": "icons/icon48.png", "128": "icons/icon128.png"}, "action": {"default_popup": "popup.html", "default_icon": {"16": "icons/icon16.png", "32": "icons/icon32.png", "48": "icons/icon48.png", "128": "icons/icon128.png"}}, "background": {"service_worker": "dist/background.bundle.js", "type": "module"}, "options_page": "settings.html", "permissions": ["storage", "declarativeNetRequest", "cookies", "scripting", "tabs", "windows", "webRequest"], "host_permissions": ["https://*.openai.com/*", "https://chatgpt.com/*", "https://gemini.google.com/*", "https://*.claude.ai/*", "https://*.baichuan-ai.com/*", "https://*.openrouter.ai/*", "https://*.anthropic.com/*", "https://copilot.microsoft.com/*", "wss://copilot.microsoft.com/*", "https://*.microsoft.com/*", "https://*.bing.com/*", "https://*.deepseek.com/*", "https://*.perplexity.ai/*"], "declarative_net_request": {"rule_resources": [{"id": "ruleset_1", "enabled": true, "path": "rules.json"}]}, "content_security_policy": {"extension_pages": "script-src 'self' 'wasm-unsafe-eval'; object-src 'self'"}, "web_accessible_resources": [{"resources": ["assets/sha3_wasm_bg.7b9ca65ddd.wasm"], "matches": ["<all_urls>"]}]}